<?xml version="1.0"?>
<!DOCTYPE module PUBLIC
    "-//Checkstyle//DTD Checkstyle Configuration 1.3//EN"
    "https://checkstyle.org/dtds/configuration_1_3.dtd">

<!--
    基于阿里巴巴Java开发手册(嵩山版/黄山版)的CheckStyle配置
    适用于IDEA CheckStyle-IDEA插件
    
    配置说明：
    1. 本配置基于阿里巴巴Java开发规范制定
    2. 重点检查代码风格、异常处理、并发编程等问题
    3. 适合商城、教育等业务项目使用
    4. 已优化Lambda表达式和控制语句的空格检查
-->
<module name="Checker">
    <property name="charset" value="UTF-8"/>
    <property name="severity" value="warning"/>
    <property name="fileExtensions" value="java, properties, xml"/>

    <!-- ========== 文件级别检查 ========== -->
    
    <!-- 【强制】禁止使用tab字符 -->
    <module name="FileTabCharacter">
        <property name="eachLine" value="true"/>
        <message key="file.containsTab" value="【强制】文件包含tab字符，请使用4个空格缩进"/>
    </module>

    <!-- 【强制】禁止使用printStackTrace -->
    <module name="RegexpSingleline">
        <property name="format" value="\.printStackTrace\(\)"/>
        <property name="message" value="【强制】禁止使用printStackTrace()，请使用日志框架记录异常信息"/>
        <property name="severity" value="error"/>
    </module>

    <!-- 【强制】禁止使用System.out/err -->
    <module name="RegexpSingleline">
        <property name="format" value="System\.(out|err)\.print"/>
        <property name="message" value="【强制】禁止使用System.out/err.print，请使用日志框架"/>
        <property name="severity" value="error"/>
    </module>

    <!-- 【推荐】TODO注释规范 -->
    <module name="RegexpSingleline">
        <property name="format" value="//\s*TODO(?!\s*\[@\w+\]\s*\d{4}-\d{2}-\d{2})"/>
        <property name="message" value="【推荐】TODO注释格式：// TODO [@负责人] YYYY-MM-DD 具体描述"/>
        <property name="severity" value="info"/>
    </module>

    <!-- 【强制】文件长度限制 -->
    <module name="FileLength">
        <property name="max" value="2500"/>
        <message key="maxLen.file" value="【强制】文件长度超过2500行，请考虑拆分"/>
    </module>

    <!-- 【强制】单行长度限制 -->
    <module name="LineLength">
        <property name="max" value="200"/>
        <property name="ignorePattern" value="^package.*|^import.*|a href|href|http://|https://|ftp://"/>
        <message key="maxLineLen" value="【强制】单行长度不超过200个字符"/>
    </module>

    <module name="TreeWalker">
        
        <!-- ========== 命名规范 ========== -->
        
        <!-- 【强制】包名规范 -->
        <module name="PackageName">
            <property name="format" value="^[a-z]+(\.[a-z][a-z0-9]*)*$"/>
            <message key="name.invalidPattern" value="【强制】包名必须全小写，使用点分隔"/>
        </module>
        
        <!-- 【强制】类名规范 -->
        <module name="TypeName">
            <property name="format" value="^[A-Z][a-zA-Z0-9]*$"/>
            <message key="name.invalidPattern" value="【强制】类名必须使用UpperCamelCase风格"/>
        </module>
        
        <!-- 【强制】方法名规范 -->
        <module name="MethodName">
            <property name="format" value="^[a-z][a-zA-Z0-9]*$"/>
            <message key="name.invalidPattern" value="【强制】方法名必须使用lowerCamelCase风格"/>
        </module>
        
        <!-- 【强制】成员变量名规范 -->
        <module name="MemberName">
            <property name="format" value="^[a-z][a-zA-Z0-9]*$"/>
            <message key="name.invalidPattern" value="【强制】成员变量必须使用lowerCamelCase风格"/>
        </module>
        
        <!-- 【强制】常量名规范 -->
        <module name="ConstantName">
            <property name="format" value="^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$"/>
            <message key="name.invalidPattern" value="【强制】常量必须全大写，单词间用下划线隔开"/>
        </module>

        <!-- 【强制】局部变量名规范 -->
        <module name="LocalVariableName">
            <property name="format" value="^[a-z][a-zA-Z0-9]*$"/>
            <message key="name.invalidPattern" value="【强制】局部变量必须使用lowerCamelCase风格"/>
        </module>

        <!-- ========== 代码格式 ========== -->
        
        <!-- 【强制】缩进规范 -->
        <module name="Indentation">
            <property name="basicOffset" value="4"/>
            <property name="braceAdjustment" value="0"/>
            <property name="caseIndent" value="4"/>
            <property name="throwsIndent" value="4"/>
            <property name="lineWrappingIndentation" value="4"/>
            <property name="arrayInitIndent" value="4"/>
        </module>

        <!-- 【强制】左大括号规范 -->
        <module name="LeftCurly">
            <property name="option" value="eol"/>
            <message key="line.new" value="【强制】左大括号不能单独一行"/>
        </module>
        
        <!-- 【强制】右大括号规范 -->
        <module name="RightCurly">
            <property name="option" value="same"/>
            <property name="tokens" value="LITERAL_TRY, LITERAL_CATCH, LITERAL_FINALLY, LITERAL_IF, LITERAL_ELSE"/>
        </module>
        
        <!-- 【强制】必须使用大括号 -->
        <module name="NeedBraces">
            <message key="needBraces" value="【强制】if/for/while/do语句必须使用大括号"/>
        </module>

        <!-- 【强制】基本操作符空格检查 - 不检查Lambda和控制语句 -->
        <module name="WhitespaceAround">
            <property name="allowEmptyConstructors" value="true"/>
            <property name="allowEmptyMethods" value="true"/>
            <property name="allowEmptyTypes" value="true"/>
            <property name="allowEmptyLoops" value="true"/>
            <!-- 只检查基本算术和赋值操作符，不检查LAMBDA、COLON、QUESTION等 -->
            <property name="tokens" value="ASSIGN, BAND, BAND_ASSIGN, BOR, BOR_ASSIGN, BSR, BSR_ASSIGN, BXOR, BXOR_ASSIGN, DIV, DIV_ASSIGN, EQUAL, GE, GT, LAND, LE, LOR, LT, MINUS, MINUS_ASSIGN, MOD, MOD_ASSIGN, NOT_EQUAL, PLUS, PLUS_ASSIGN, SL, SL_ASSIGN, SR, SR_ASSIGN, STAR, STAR_ASSIGN"/>
            <message key="ws.notFollowed" value="【强制】操作符周围必须有空格"/>
        </module>

        <!-- ========== 异常处理规范 ========== -->
        
        <!-- 【强制】空catch块检查 -->
        <module name="EmptyCatchBlock">
            <property name="exceptionVariableName" value="expected|ignore"/>
            <message key="catch.block.empty" value="【强制】catch块不能为空，必须处理异常或添加注释说明"/>
        </module>

        <!-- 【推荐】异常重新抛出检查 -->
        <module name="RegexpSinglelineJava">
            <property name="format" value="throw\s+new\s+RuntimeException\s*\(\s*e\s*\)"/>
            <property name="message" value="【推荐】避免简单包装异常，应该添加业务上下文信息"/>
        </module>

        <!-- ========== 并发编程规范 ========== -->
        
        <!-- 【强制】避免直接创建线程 -->
        <module name="RegexpSinglelineJava">
            <property name="format" value="new\s+Thread\s*\("/>
            <property name="message" value="【强制】避免直接创建Thread，建议使用线程池"/>
        </module>

        <!-- 【强制】同步块规范 -->
        <module name="RegexpSinglelineJava">
            <property name="format" value="synchronized\s*\(\s*this\s*\)"/>
            <property name="message" value="【强制】避免对this进行同步，建议使用专门的锁对象"/>
        </module>

        <!-- ========== 集合处理规范 ========== -->
        
        <!-- 【推荐】集合判空检查 -->
        <module name="RegexpSinglelineJava">
            <property name="format" value="\.size\(\)\s*==\s*0"/>
            <property name="message" value="【推荐】判断集合为空使用isEmpty()方法，而不是size() == 0"/>
        </module>

        <!-- ========== OOP规约 ========== -->
        
        <!-- 【强制】equals和hashCode规范 -->
        <module name="EqualsHashCode">
            <message key="equals.noHashCode" value="【强制】重写equals方法时必须同时重写hashCode方法"/>
        </module>
        
        <!-- 【强制】字符串比较规范 -->
        <module name="StringLiteralEquality">
            <message key="string.literal.equality" value="【强制】字符串比较使用equals()方法，不要使用=="/>
        </module>

        <!-- ========== 控制语句规范 ========== -->
        
        <!-- 【强制】switch语句规范 -->
        <module name="DefaultComesLast">
            <message key="default.comes.last" value="【强制】switch语句中default分支应该放在最后"/>
        </module>
        
        <!-- 【强制】switch穿透检查 -->
        <module name="FallThrough">
            <message key="fall.through" value="【强制】switch语句缺少break，如果是故意的请添加注释"/>
        </module>

        <!-- ========== 代码复杂度控制 ========== -->
        
        <!-- 【推荐】圈复杂度控制 -->
        <module name="CyclomaticComplexity">
            <property name="max" value="20"/>
            <message key="cyclomaticComplexity" value="【推荐】方法圈复杂度过高({0})，建议拆分方法"/>
        </module>
        
        <!-- 【推荐】方法长度控制 -->
        <module name="MethodLength">
            <property name="max" value="80"/>
            <property name="countEmpty" value="false"/>
            <message key="maxLen.method" value="【推荐】方法过长({0}行)，建议拆分为多个小方法"/>
        </module>
        
        <!-- 【推荐】参数个数控制 -->
        <module name="ParameterNumber">
            <property name="max" value="7"/>
            <message key="maxParam" value="【推荐】参数过多({0}个)，建议使用对象封装参数"/>
        </module>

        <!-- ========== 导入规范 ========== -->
        
        <!-- 【强制】避免通配符导入 -->
        <module name="AvoidStarImport">
            <message key="import.avoidStar" value="【强制】避免使用通配符导入，明确指定导入的类"/>
        </module>
        
        <!-- 【强制】删除未使用导入 -->
        <module name="UnusedImports">
            <message key="import.unused" value="【强制】未使用的导入，请删除"/>
        </module>
        
        <!-- 【强制】禁止导入内部API -->
        <module name="IllegalImport">
            <property name="illegalPkgs" value="sun, com.sun"/>
            <message key="import.illegal" value="【强制】禁止使用内部API包"/>
        </module>

        <!-- ========== 修饰符规范 ========== -->
        
        <!-- 【强制】修饰符顺序 -->
        <module name="ModifierOrder">
            <message key="mod.order" value="【强制】修饰符顺序错误，正确顺序：public protected private abstract static final transient volatile synchronized native strictfp"/>
        </module>
        
        <!-- 【推荐】删除多余修饰符 -->
        <module name="RedundantModifier">
            <message key="redundantModifier" value="【推荐】多余的修饰符"/>
        </module>

        <!-- ========== 注释规范 ========== -->
        
        <!-- 【推荐】注解位置规范 -->
        <module name="AnnotationLocation">
            <property name="allowSamelineMultipleAnnotations" value="false"/>
            <property name="allowSamelineParameterizedAnnotation" value="false"/>
        </module>

        <!-- ========== 其他规范 ========== -->
        
        <!-- 【推荐】避免魔法数字 -->
        <module name="MagicNumber">
            <property name="ignoreNumbers" value="-1, 0, 1, 2, 10, 100, 1000"/>
            <property name="ignoreHashCodeMethod" value="true"/>
            <property name="ignoreAnnotation" value="true"/>
            <property name="ignoreFieldDeclaration" value="true"/>
            <message key="magic.number" value="【推荐】避免使用魔法数字，请定义为常量"/>
        </module>

        <!-- 【推荐】空行分隔 -->
        <module name="EmptyLineSeparator">
            <property name="allowNoEmptyLineBetweenFields" value="true"/>
            <property name="tokens" value="PACKAGE_DEF, IMPORT, CLASS_DEF, INTERFACE_DEF, ENUM_DEF, STATIC_INIT, INSTANCE_INIT, METHOD_DEF, CTOR_DEF"/>
        </module>

        <!-- 【推荐】一行一个语句 -->
        <module name="OneStatementPerLine">
            <message key="multiple.statements.line" value="【推荐】一行只能有一个语句"/>
        </module>

        <!-- 【推荐】多变量声明 -->
        <module name="MultipleVariableDeclarations">
            <message key="multiple.variable.declarations" value="【推荐】每行只能声明一个变量"/>
        </module>
    </module>
</module>
