apiVersion: apps/v1
kind: Deployment
metadata:
  name: dbj-classpal-books-api
  namespace: {namespace}
spec:
  replicas: 1
  selector:
    matchLabels:
      name: dbj-classpal-books-api
  template:
    metadata:
      labels:
        name: dbj-classpal-books-api
    spec:
      containers:
        - env:
          - name: Nacos_Server_Addr
            value: "172.23.176.188:8848"
          - name: Nacos_Namespace
            value: "k8s-classpal-prod"
          - name: Nacos_Username
            value: "nacos"
          - name: Nacos_Password
            value: "@Dbj#nacos888"
          - name: XMS_OPTS
            value: 1024m
          - name: XMX_OPTS
            value: 4096m
          image: {Image}
          imagePullPolicy: Always
          name: dbj-classpal-books-api
          ports:
          - containerPort: 62003
          resources:
            requests:
              cpu: "20m"
              memory: 600Mi
            limits:
              cpu: "1000m"
              memory: 4096Mi

          startupProbe:
            tcpSocket:
              port: 62003
            initialDelaySeconds: 80
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
      
          livenessProbe:
            tcpSocket:
              port: 62003
            initialDelaySeconds: 10
            periodSeconds: 15
            timeoutSeconds: 5
            failureThreshold: 3
      
          readinessProbe:
            tcpSocket:
              port: 62003
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
            successThreshold: 1

      imagePullSecrets:
      - name: https-banxueketang-harbor
      restartPolicy: Always
