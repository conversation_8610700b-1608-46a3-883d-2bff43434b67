# 团队Java代码规范指南

## 🎯 核心原则

### 1. 异常处理规范
```java
// ❌ 错误做法
try {
    // 业务逻辑
} catch (Exception e) {
    e.printStackTrace();  // 禁止使用
}

// ✅ 正确做法
try {
    // 业务逻辑
} catch (BusinessException e) {
    log.warn("业务异常: {}", e.getMessage());
    return RestResponse.fail(e.getMessage());
} catch (Exception e) {
    log.error("系统异常，操作失败，参数: {}", params, e);
    return RestResponse.fail("系统繁忙，请稍后重试");
}
```

### 2. 日志使用规范
```java
// ❌ 错误做法
log.info("用户登录，用户ID: " + userId + ", 时间: " + loginTime);

// ✅ 正确做法
log.info("用户登录，用户ID: {}, 时间: {}", userId, loginTime);
```

### 3. 并发编程规范
```java
// ❌ 错误做法
synchronized (this) {
    // 业务逻辑
}

// ✅ 正确做法
private final Object lock = new Object();
synchronized (lock) {
    // 业务逻辑
}
```

### 4. 资源管理规范
```java
// ❌ 错误做法
FileInputStream fis = new FileInputStream(file);
// ... 使用资源
fis.close();

// ✅ 正确做法
try (FileInputStream fis = new FileInputStream(file)) {
    // ... 使用资源
} // 自动关闭
```

## 📋 IDEA插件配置

### 必装插件
1. **SonarLint** - 实时代码质量检查
2. **CheckStyle-IDEA** - 代码风格检查
3. **Alibaba Java Coding Guidelines** - 阿里规约
4. **SpotBugs** - 静态代码分析

### CheckStyle配置
1. 安装CheckStyle-IDEA插件
2. 导入 `team-checkstyle-config.xml` 配置文件
3. 设置为保存时自动检查

## 🔧 项目特定规范

### 商城金额处理
```java
// ❌ 错误做法
BigDecimal price = new BigDecimal(19.99);

// ✅ 正确做法
BigDecimal price = BigDecimal.valueOf(19.99);
// 或
BigDecimal price = new BigDecimal("19.99");
```

### 分布式锁使用
```java
// ❌ 错误做法
String lockKey = "lock:" + UUID.randomUUID();

// ✅ 正确做法
String lockKey = "lock:order:" + orderId;
```

### 异步处理
```java
// ❌ 错误做法
new Thread(() -> {
    // 异步逻辑
}).start();

// ✅ 正确做法
@Async
public void processAsync() {
    // 异步逻辑
}
```

## 📊 代码质量指标

### 复杂度控制
- 方法圈复杂度 ≤ 15
- 方法长度 ≤ 80行
- 参数个数 ≤ 7个

### 命名规范
- 类名：大驼峰 `UserService`
- 方法名：小驼峰 `getUserInfo`
- 常量：全大写 `MAX_RETRY_COUNT`
- 包名：全小写 `com.dbj.classpal.books`

## 🚀 最佳实践

### 1. 方法设计
```java
// ✅ 单一职责，参数合理
public RestResponse<UserDTO> getUserById(Long userId) {
    // 实现逻辑
}

// ✅ 使用BO封装多参数
public RestResponse<OrderDTO> createOrder(CreateOrderBO orderBO) {
    // 实现逻辑
}
```

### 2. 异常设计
```java
// ✅ 业务异常
public class OrderNotFoundException extends BusinessException {
    public OrderNotFoundException(Long orderId) {
        super("订单不存在: " + orderId);
    }
}
```

### 3. 日志设计
```java
// ✅ 结构化日志
log.info("订单创建成功，orderId: {}, userId: {}, amount: {}", 
         orderId, userId, amount);

// ✅ 异常日志包含上下文
log.error("订单支付失败，orderId: {}, paymentId: {}", 
          orderId, paymentId, e);
```

## 📝 代码审查清单

### 提交前自检
- [ ] 无 `e.printStackTrace()` 调用
- [ ] 无 `System.out.println()` 调用
- [ ] 异常处理完整且有意义
- [ ] 日志使用参数化格式
- [ ] 资源正确关闭
- [ ] 方法复杂度合理
- [ ] 变量命名清晰

### 审查重点
- [ ] 业务逻辑正确性
- [ ] 异常处理完整性
- [ ] 并发安全性
- [ ] 性能考虑
- [ ] 安全性检查

## 🔍 常见问题修复

### 1. 替换printStackTrace
```java
// 查找: \.printStackTrace\(\)
// 替换为合适的日志记录
```

### 2. 优化字符串拼接
```java
// 查找: log\.(info|error|warn|debug)\s*\([^,]*\+
// 替换为参数化日志
```

### 3. 修复空catch块
```java
// 查找: catch\s*\([^)]*\)\s*\{\s*\}
// 添加适当的异常处理
```

## 📈 持续改进

### 定期检查
- 每周运行完整的代码质量检查
- 每月回顾和更新规范
- 季度进行团队培训

### 工具集成
- CI/CD流水线集成质量检查
- 代码提交前强制检查
- 质量报告定期生成

---

**记住**: 代码规范不是约束，而是团队协作的基础。好的代码规范能提高开发效率，减少bug，提升系统稳定性。
