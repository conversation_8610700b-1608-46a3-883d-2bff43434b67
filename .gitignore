/target/
.classpath
.settings
.project
.idea/
/dbj-printer-service/src/main/resources/bootstrap.yml

# 忽略匹配下列规则的Git 提交 V2.1.0
### gradle ###
.gradle
/build/
!gradle/wrapper/gradle-wrapper.jar

### STS ###
.settings/
.apt_generated
.factorypath
.classpath
.factorypath
.project
.settings
.springBeans
bin/

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr
*.lock
rebel.xml

### NetBeans ###
nbproject/private/
build/
nbbuild/
nbdist/
.nb-gradle/

### maven ###
target/
.flattened-pom.xml
*.war
*.ear
*.zip
*.tar
*.tar.gz

### logs ####
/logs/
*.log
*.log.gz

### temp ignore ###
*.cache
*.diff
*.patch
*.tmp
*.java~
*.properties~
*.xml~

### system ignore ###
.DS_Store
Thumbs.db
Servers
.metadata
upload
gen_code

### node ###
node_modules
