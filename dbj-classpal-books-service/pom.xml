<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.dbj</groupId>
        <artifactId>dbj-classpal-books-bus</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>dbj-classpal-books-service</artifactId>
    <name>dbj-classpal-books-service</name>


    <dependencies>
        <dependency>
            <groupId>com.dbj</groupId>
            <artifactId>dbj-classpal-books-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dbj</groupId>
            <artifactId>dbj-classpal-books-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dbj</groupId>
            <artifactId>dbj-classpal-promotion-client</artifactId>
            <version>${parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dbj</groupId>
            <artifactId>dbj-classpal-commons-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dbj</groupId>
            <artifactId>dbj-classpal-mybatis-plus-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dbj</groupId>
            <artifactId>dbj-classpal-pdf-watermark-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dbj</groupId>
            <artifactId>dbj-classpal-redisson-starter</artifactId>
            <version>${parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dbj</groupId>
            <artifactId>dbj-openapi-starter</artifactId>
            <version>0.0.1-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.dbj</groupId>
            <artifactId>dbj-classpal-tts-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dbj</groupId>
            <artifactId>dbj-business-external-client</artifactId>
            <version>0.0.1-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.dbj</groupId>
            <artifactId>dbj-classpal-admin-client</artifactId>
            <version>${parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dbj</groupId>
            <artifactId>dbj-classpal-app-client</artifactId>
            <version>${parent.version}</version>
        </dependency>
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.activation</groupId>
            <artifactId>activation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>jaxb-runtime</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.5.1</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>3.5.1</version>
        </dependency>
    </dependencies>
    <build>
        <finalName>${project.name}</finalName>
    </build>
</project>