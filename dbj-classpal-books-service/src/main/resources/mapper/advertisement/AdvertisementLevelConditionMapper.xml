<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.advertisement.AdvertisementLevelConditionMapper">

    <select id="getByAdvertisementIds"
            parameterType="java.util.List"
            resultType="com.dbj.classpal.books.client.dto.advertisement.AdvertisementLevelConditionDTO">
        select
            alc.id,
            alc.advertisement_id,
            alc.parent_id,
            alc.logic_type,
            alc.level,
            alc.condition_type,
            alc.name
        from
            advertisement_level_condition alc
        where
            alc.is_deleted = 0
            and alc.advertisement_id in
            <foreach item="advertisementId" collection="advertisementIds" open="(" separator="," close=")">
                #{advertisementId}
            </foreach>
        order by
            alc.create_time
    </select>
</mapper>
