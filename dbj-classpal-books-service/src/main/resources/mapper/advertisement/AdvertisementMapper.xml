<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.advertisement.AdvertisementMapper">

    <select id="getAdvertisementPageList"
            resultType="com.dbj.classpal.books.client.dto.advertisement.AdvertisementDTO">
        select
            *
        from
            advertisement a
        where
            a.is_deleted = 0
            <if test="bo.type != null and bo.type != ''">
                and type = #{bo.type}
            </if>
            <if test="bo.title != null and bo.title != ''">
                and title like concat('%',#{bo.title},'%')
            </if>
        order by
            sort desc, create_time desc, id desc
    </select>
</mapper>
