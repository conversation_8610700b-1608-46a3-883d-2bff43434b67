<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.question.QuestionAnswerMapper">
    <resultMap id="questionAnswerMap" type="com.dbj.classpal.books.service.entity.question.QuestionAnswer">
        <result column="question_id" property="questionId"/>
        <result column="serial_no" property="serialNo"/>
        <result column="option_name" property="optionName"/>
        <result column="option_content" property="optionContent"/>
        <result column="is_answer" property="isAnswer"/>
        <result column="status" property="status"/>
    </resultMap>
</mapper> 