<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.question.QuestionCategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dbj.classpal.books.service.entity.question.QuestionCategory">
        <result column="name" property="name"/>
        <result column="father_id" property="fatherId"/>
        <result column="is_default" property="isDefault"/>
        <result column="sort_num" property="sortNum"/>
        <result column="status" property="status"/>
    </resultMap>

    <!-- 递归查询所有子分类ID -->
    <select id="getChildCategoryIds" resultType="java.lang.Integer">
        WITH RECURSIVE category_tree AS (
            SELECT id, father_id
            FROM question_category
            WHERE id = #{categoryId} AND is_deleted = 0
            UNION ALL
            SELECT c.id, c.father_id
            FROM question_category c
            INNER JOIN category_tree ct ON c.father_id = ct.id
            WHERE c.is_deleted = 0
        )
        SELECT id FROM category_tree
    </select>

    <!-- 统计分类下的题目数量（包含子分类） -->
    <select id="countQuestionsByCategoryId" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT q.id)
        FROM question q
        WHERE q.question_category_id IN (
            WITH RECURSIVE category_tree AS (
                SELECT id, father_id
                FROM question_category
                WHERE id = #{categoryId} AND is_deleted = 0
                UNION ALL
                SELECT c.id, c.father_id
                FROM question_category c
                INNER JOIN category_tree ct ON c.father_id = ct.id
                WHERE c.is_deleted = 0
            )
            SELECT id FROM category_tree
        )
        AND q.is_deleted = 0
    </select>

    <!-- 获取同级分类中的最大排序号 -->
    <select id="selectMaxSortNum" resultType="java.lang.Integer">
        SELECT MAX(sort_num)
        FROM question_category
        WHERE father_id = #{fatherId}
        AND is_deleted = 0
    </select>

</mapper> 