<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.question.AppUserWrongQuestionNormalMapper">
    <select id="queryNormalWrongQuestionsWithJoin" resultType="com.dbj.classpal.books.service.entity.wrongquestion.AppUserWrongQuestionNormal">
        SELECT
        wq.*
        FROM
        app_user_wrong_question_normal wq
        INNER JOIN
        question q ON wq.question_id = q.id
        INNER JOIN
        basic_config bcc ON q.subject_id = bcc.id
        WHERE
        wq.app_user_id = #{appUserId}
        AND wq.is_deleted = 0
        AND bcc.biz_type = #{bizType}
        <if test="subjectId != null">
            AND bcc.id = #{subjectId}
        </if>
        <if test="questionIds != null and questionIds.size() > 0">
            AND wq.question_id IN
            <foreach collection="questionIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="type != null">
            AND q.type != #{type}
        </if>
        ORDER BY
        wq.create_time DESC
    </select>

</mapper> 