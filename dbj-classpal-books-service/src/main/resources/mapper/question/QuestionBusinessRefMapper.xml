<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.question.QuestionBusinessRefMapper">
    <select id="pageList" resultType="com.dbj.classpal.books.common.dto.question.QuestionBusinessRefDTO">
        SELECT
            qbr.id as id,
            q.id as questionId,
            q.title as title,
            q.type as type,
            q.media_type as mediaType,
            s.id as subjectId,
            s.name as subjectName,
            qbr.order_num as orderNum
        FROM question_business_ref qbr
        LEFT JOIN question q on qbr.question_id = q.id
        LEFT JOIN basic_config s ON s.id = q.subject_id
        WHERE qbr.is_deleted = 0 and qbr.business_id = #{condition.businessId} and qbr.business_type = #{condition.businessType}
        ORDER BY qbr.order_num desc,qbr.id asc
    </select>
    <select id="getMaxSortNum">
        select
            ifnull(max(order_num),0)
        from question_business_ref
        where is_deleted = 0 and business_id = #{bo.businessId} and business_type = #{bo.businessType}
    </select>
</mapper> 