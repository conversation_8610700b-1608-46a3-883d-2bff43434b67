<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.audio.AudioSpeakerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dbj.classpal.books.service.entity.audio.AudioSpeaker">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="icon" property="icon" />
        <result column="audio_model_url" property="audioModelUrl" />
        <result column="voice_type" property="voiceType" />
        <result column="is_emotion" property="isEmotion" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

</mapper>
