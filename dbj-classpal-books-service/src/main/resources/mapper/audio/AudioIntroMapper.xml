<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.audio.AudioIntroMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dbj.classpal.books.service.entity.audio.AudioIntro">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="audio_classify_id" property="audioClassifyId" />
        <result column="duration" property="duration" />
        <result column="size" property="size" />
        <result column="frequency" property="frequency" />
        <result column="app_material_id" property="appMaterialId" />
        <result column="audio_url" property="audioUrl" />
        <result column="status" property="status" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

</mapper>
