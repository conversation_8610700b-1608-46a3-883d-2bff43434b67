<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.audio.AudioClassifyMapper">

    <select id="getPathList" resultType="com.dbj.classpal.books.client.dto.audio.AudioClassifyPathDTO">
        WITH RECURSIVE ancestor_path AS (
            -- 基础查询：选择起始节点
            SELECT
                c.id,
                c.parent_id,
                c.classify_name,
                i.id as audio_intro_id,
                c.tenant_id
            FROM audio_classify c
                     join audio_intro i on i.audio_classify_id = c.id and c.is_deleted = 0
            WHERE i.id in
              <foreach collection="audioIntroIds" item="audioIntroId" separator="," open="(" close=")">
                #{audioIntroId}
              </foreach>
              and i.is_deleted = 0

            UNION ALL

            -- 递归查询：连接父节点
            SELECT
                c.id,
                c.parent_id,
                c.classify_name,
                ap.audio_intro_id,
                c.tenant_id
            FROM audio_classify c
                     JOIN ancestor_path ap ON c.id = ap.parent_id
        )
        SELECT * FROM ancestor_path
    </select>

    <select id="getChildren" resultType="java.lang.Integer">
        WITH RECURSIVE tree AS (
            SELECT id, tenant_id
            FROM audio_classify
            WHERE is_deleted = 0
                and id in
                <foreach collection="ids" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            UNION ALL
            SELECT c.id, c.tenant_id
            FROM audio_classify c
                     JOIN tree t ON c.parent_id = t.id
            where
                c.is_deleted = 0
        )
        SELECT id FROM tree
    </select>

    <select id="getParentIds" resultType="java.lang.Integer">
        WITH RECURSIVE hierarchy AS (
            SELECT id , parent_id, tenant_id
            FROM audio_classify
            WHERE id = #{id}
            UNION ALL
            SELECT p.id, p.parent_id, p.tenant_id
            FROM audio_classify p
            JOIN hierarchy h ON p.id = h.parent_id
        )
        SELECT id FROM hierarchy
    </select>

</mapper>
