<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.audio.AudioContextInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dbj.classpal.books.service.entity.audio.AudioContextInfo">
        <id column="id" property="id" />
        <result column="audio_intro_id" property="audioIntroId" />
        <result column="text" property="text" />
        <result column="audio_speaker_id" property="audioSpeakerId" />
        <result column="emotion" property="emotion" />
        <result column="volume" property="volume" />
        <result column="speechRate" property="speechRate" />
        <result column="pitchRate" property="pitchRate" />
        <result column="intensity" property="intensity" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <select id="statRefNum" resultType="com.dbj.classpal.books.common.dto.audio.AudioHintRefNumDTO">
        WITH search_values AS (
            <foreach collection="audioHintMusicIds" item="audioHintMusicId" separator=" union all ">
                select #{audioHintMusicId} as audioHintMusicId
            </foreach>
        )
        SELECT
            sv.audioHintMusicId,
            SUM(IF(JSON_CONTAINS(t.audio_hint_music_ids, CAST(sv.audioHintMusicId AS JSON)), 1, 0)) AS count
        FROM
            audio_context_info t
            CROSS JOIN search_values sv
        GROUP BY
            sv.audioHintMusicId
    </select>

</mapper>
