<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.pinyin.PinyinMapper">

    <select id="getPinyinPage" resultType="com.dbj.classpal.books.client.dto.pinyin.PinyinDTO">
        select
            p.id,
            p.classify_id AS classifyId,
            p.title,
            p.status,
            p.sort
        from
            pinyin p
        where
            is_deleted = 0
            <if test="bo.classifyId != null">
                and p.classify_id = #{bo.classifyId}
            </if>
            <if test="bo.title != null">
                and p.title LIKE CONCAT('%', #{bo.title}, '%')
            </if>
            <if test="bo.status != null">
                and p.status = #{bo.status}
            </if>
        order by
            p.sort desc,
            p.create_time desc,
            p.id desc
    </select>
</mapper>
