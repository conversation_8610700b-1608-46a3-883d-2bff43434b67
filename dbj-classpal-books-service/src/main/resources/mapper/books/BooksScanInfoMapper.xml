<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.books.BooksScanInfoMapper">

    <select id="bookScanCount" resultType="com.dbj.classpal.books.client.dto.books.BooksScanCountDTO">
        select books_id as booksId, count(1) as scanCount
        from books_scan_info
        where
            is_deleted = 0
            and books_id in
        <foreach collection="bookIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by books_id
    </select>
</mapper>
