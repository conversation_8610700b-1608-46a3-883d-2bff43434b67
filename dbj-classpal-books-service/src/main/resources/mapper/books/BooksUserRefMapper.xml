<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.books.BooksUserRefMapper">

    <select id="booksUserShellist" resultType="com.dbj.classpal.books.client.dto.books.app.BooksUserShelfDTO">
        SELECT
            b.blurb,
            b.book_name,
            b.id AS books_id
        FROM
            books_user_ref a,
            books_info b
        WHERE
            a.is_deleted = 0
          AND b.is_deleted = 0
          AND a.books_id = b.id
          AND a.app_user_id = #{appUserId}
        ORDER BY
            a.is_last_study DESC,
            a.last_study_time DESC,
            a.create_time DESC
    </select>

    <select id="booksUserCount" resultType="com.dbj.classpal.books.client.dto.books.BooksUserCountDTO">
        select
            books_id,
            count(1) as userCount
        from
            books_user_ref
        where
            is_deleted = 0
          and books_id in
          <foreach collection="bookIds" item="bookId" open="(" separator="," close=")">
              #{bookId}
          </foreach>
        group by books_id
    </select>
</mapper>
