<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.books.BooksRankInCodesContentsMapper">


    <select id="page" resultType="com.dbj.classpal.books.client.dto.books.BooksRankInCodesContentsPageDTO">
        SELECT
            a.id as contentId,
            a.content_name,
            b.product_item_name,
            b.id as rankId,
            a.h5_page_url,
            a.new_print_code_url
        FROM
            books_rank_in_codes_contents a
            LEFT JOIN books_rank_info b ON a.rank_id = b.id
        WHERE
            a.books_id = #{bo.booksId}
            and a.type != 'directory'
            and b.status = 1
            and a.is_deleted = 0
            and b.is_deleted = 0
        <if test="bo.contentName != null and bo.contentName != ''">
            and a.content_name like CONCAT('%', #{bo.contentName}, '%')
        </if>
        <if test="bo.productItemName != null and bo.productItemName != ''">
            and b.product_item_name like CONCAT('%', #{bo.productItemName}, '%')
        </if>
    </select>

    <select id="listCount" resultType="com.dbj.classpal.books.client.dto.books.app.BooksRankInCodesContentsCountDTO">
        SELECT
            a.books_id,
            count(1) as contentsNum
        FROM
            books_rank_in_codes_contents a,
            books_rank_info b
        WHERE
            a.rank_id = b.id
            and a.is_deleted = 0
            and b.is_deleted = 0
            and a.type != 'directory'
            and b.status = 1
            and a.books_id in
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        GROUP BY
            a.books_id
    </select>
    <select id="listRankCount" resultType="com.dbj.classpal.books.client.dto.books.app.BooksRankInCodesContentsRankCountDTO">
        SELECT
            a.rank_id,
            count(1) as contentsNum
        FROM
            books_rank_in_codes_contents a,
            books_rank_info b
        WHERE
            a.rank_id = b.id
            and a.is_deleted = 0
            and b.is_deleted = 0
            and b.status = 1
            and a.type != 'directory'
            and a.rank_id in
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        GROUP BY
            a.rank_id
    </select>
</mapper>
