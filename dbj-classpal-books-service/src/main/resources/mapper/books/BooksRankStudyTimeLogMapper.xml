<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.books.BooksRankStudyTimeLogMapper">

    <select id="getLastStudyTime" resultType="java.lang.Long">
        select sum(study_time) from books_rank_study_time_log
                 where is_deleted = 0
                 and app_user_id = #{appUserId}
                 and rank_id = #{rankId}

    </select>
</mapper>
