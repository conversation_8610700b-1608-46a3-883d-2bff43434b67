<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.books.BooksCategoryMapper">


    <select id="categoryNameTree" resultType="com.dbj.classpal.books.client.dto.books.BooksCategoryNameTreeApiDTO">
        WITH RECURSIVE CTE AS (-- 初始查询（保持不变）
        SELECT
        id,
        parent_id,
        NAME,
        tenant_id,
        CAST(
        id AS CHAR ( 255 )) AS original_id,
        CAST(
        NAME AS CHAR ( 255 )) AS path,
        1 AS depth -- 记录层级深度

        FROM
        books_category
        WHERE
        is_deleted  = 0
        and
        id IN (
        <foreach item="item" collection="categoryIds" separator="," close="">
            #{item}
        </foreach>
        )
        UNION ALL
        SELECT
        c.id,
        c.parent_id,
        c.NAME,
        c.tenant_id,
        cte.original_id,
        CONCAT(  c.NAME, '/',cte.path ) AS path,-- 修正路径拼接
        cte.depth + 1 AS depth -- 层级+1

        FROM
        books_category c
        INNER JOIN CTE cte ON c.id = cte.parent_id
        WHERE
        cte.parent_id IS NOT NULL
        AND c.id != cte.original_id -- 排除初始节点（关键修改）

        )
        SELECT
        original_id as id,
        path AS nameTree
        FROM
        ( SELECT *, ROW_NUMBER() OVER ( PARTITION BY original_id ORDER BY depth DESC ) AS rn -- 按层级倒序编号
        FROM CTE ) numbered_cte
        WHERE
        rn = 1
    </select>

    <delete id="delete">
        DELETE FROM books_category WHERE
        is_deleted  = 0
        and
        id IN (
        <foreach item="item" collection="categoryIds" separator="," close="">
            #{item}
        </foreach>
        )
    </delete>
</mapper>
