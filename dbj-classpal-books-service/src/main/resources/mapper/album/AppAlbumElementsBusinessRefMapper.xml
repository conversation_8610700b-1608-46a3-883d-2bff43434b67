<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.album.AppAlbumElementsBusinessRefMapper">
    <select id="checkMenuRefCount">
        select
            count(1)
        from app_album_elements_business_ref aaebr
        where aaebr.app_album_id in (
            WITH RECURSIVE cte AS (
                SELECT
                    aam.id,
                    aam.is_root,
                    aam.parent_id,
                    aam.album_menu_name,
                    aam.album_type,
                    aam.album_menu_status,
                    aam.order_num,
                    aam.create_time,
                    aam.tenant_id,
                    aam.is_deleted
                FROM app_album_menus aam
                WHERE aam.id = #{id}   -- 替换为父节点ID
                UNION ALL
                SELECT
                    t.id,
                    t.is_root,
                    t.parent_id,
                    t.album_menu_name,
                    t.album_type,
                    t.album_menu_status,
                    t.order_num,
                    t.create_time,
                    t.tenant_id,
                    t.is_deleted
                FROM app_album_menus t
                         INNER JOIN cte AS tc ON t.parent_id = tc.id
            )
            SELECT
                aae.id
            FROM cte st
                     LEFT JOIN app_album_elements aae ON st.id = aae.app_album_menu_id
            WHERE st.is_deleted = 0 and aae.is_deleted = 0
        ) and aaebr.is_deleted = 0
    </select>

    <select id="refBusinessList" resultType="com.dbj.classpal.books.common.dto.album.AppAlbumElementsBusinessRefQueryDTO" parameterType="com.dbj.classpal.books.common.bo.album.AppAlbumElementsBusinessRefQueryCommonBO">
        select
            aaebr.*,
            aae.id as "albumId",
            aae.album_title as "albumTitle",
            aae.album_remark as "albumRemark",
            aae.album_cover as "albumCover",
            aae.album_type as "appMaterialType"
        from app_album_elements_business_ref aaebr
        left join app_album_elements aae on aae.id = aaebr.app_album_id
        where aaebr.is_deleted = 0
        <if test="albumId != null and albumId != ''">
            and aaebr.app_album_id = #{albumId}
        </if>
        <if test="businessId != null and businessId != ''">
            and aaebr.business_id = #{businessId}
        </if>
        <if test="businessIds != null and businessIds.size() > 0">
            and aaebr.business_id in
            <foreach item="item" index="index" collection="businessIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="businessType != null and businessType != ''">
            and aaebr.business_type = #{businessType}
        </if>
        <if test="albumType != null and albumType != ''">
            and aae.album_type = #{albumType}
        </if>
        order by aaebr.order_num asc,aaebr.create_time asc
    </select>
</mapper>
