<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.album.AppAlbumElementsMapper">

    <select id="listPage" resultType="com.dbj.classpal.books.common.dto.album.AppAlbumElementsQueryDTO">
        select
            e.id,
            e.app_album_menu_id,
            e.album_type,
            e.album_cover,
            e.album_title,
            e.album_remark,
            e.album_visible,
            e.album_status,
            sc.trial_count,
            sc.sales_mode_status,
            sc.sales_mode,
            sc.sales_price,
            sc.original_price
        from
            app_album_elements e
            left join product_sales_config sc on e.id = sc.product_id and sc.product_type = #{bo.productType} and sc.is_deleted = 0
        where
            e.is_deleted = 0 and e.album_type = #{bo.albumType}
        <if test="bo.albumStatus != null">
            and e.album_status = #{bo.albumStatus}
        </if>
        <if test="bo.albumStatus != null">
            and e.album_visible = #{bo.albumVisible}
        </if>
        <if test="bo.appAlbumMenuId != null">
            and e.app_album_menu_id = #{bo.appAlbumMenuId}
        </if>
        <if test="bo.albumTitle != null and bo.albumTitle != ''">
            and e.album_title like concat('%', #{bo.albumTitle}, '%')
        </if>
        <if test="bo.salesMode != null">
            and sc.sales_mode_status = 1 and sc.sales_mode = #{bo.salesMode}
        </if>
        <if test="bo.ids != null and bo.ids.size() > 0">
            and e.id in
            <foreach collection="bo.ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>
</mapper>
