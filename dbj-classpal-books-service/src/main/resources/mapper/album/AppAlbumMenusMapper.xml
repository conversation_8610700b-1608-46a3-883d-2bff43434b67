<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.album.AppAlbumMenusMapper">
    <select id="getAppAlbumMenusInfoByDeptIdGroup" resultType="com.dbj.classpal.books.common.dto.album.AppAlbumMenusInfoDTO">
        WITH RECURSIVE menus_tree AS (
            -- 基础查询：获取所有专辑分类（作为根节点）
            SELECT
                id,
                is_root,
                parent_id,
                default_type,
                album_menu_name,
                album_type,
                album_menu_status,
                order_num,
                tenant_id,
                is_deleted,
                id AS root_id  -- 标记当前部门的根父节点ID
            FROM app_album_menus
            UNION ALL
            -- 递归查询：逐级关联子部门
            SELECT
                d.id,
                d.is_root,
                d.parent_id,
                d.default_type,
                d.album_menu_name,
                d.album_type,
                d.album_menu_status,
                d.order_num,
                d.tenant_id,
                d.is_deleted,
                dt.root_id  -- 继承根父节点ID
            FROM app_album_menus d
            INNER JOIN menus_tree dt ON d.parent_id = dt.id
        )
        -- 按根父节点统计所有子节点的专辑数
        SELECT
            dt.root_id AS menusId,
            COUNT(aae.id) AS num
        FROM menus_tree dt
        LEFT JOIN app_album_elements aae ON aae.app_album_menu_id = dt.id  -- 关联子节点的专辑
        WHERE dt.album_type = #{bo.albumType} and dt.is_deleted = 0 and aae.is_deleted = 0
        GROUP BY dt.root_id
        ORDER BY dt.root_id;
    </select>

    <select id="getChildrenDepth">
        WITH RECURSIVE cte_tree AS (
            -- 初始查询：定位目标节点，层级从1开始
            SELECT
                id,
                parent_id,
                tenant_id,
                is_deleted,
                1 AS depth
            FROM app_album_menus
            WHERE id = #{id}
            UNION ALL
            -- 递归查询：逐层向下遍历子节点，层级+1
            SELECT
                c.id,
                c.parent_id,
                c.tenant_id,
                c.is_deleted,
                t.depth + 1
            FROM app_album_menus c
            INNER JOIN cte_tree t ON c.parent_id = t.id
        )
        SELECT
            IFNULL(MAX(depth),1)
        FROM cte_tree
        WHERE id != #{id} and is_deleted = 0;  -- 排除自身，仅显示子孙节点
    </select>

    <select id="getRootDepth">
        WITH RECURSIVE cte_path AS (
            -- 初始查询：定位目标节点，深度初始化为1
            SELECT
                id,
                parent_id,
                tenant_id,
                is_deleted,
                1 AS depth
            FROM app_album_menus
            WHERE id = #{id}  -- 指定查询的节点
            UNION ALL
            -- 递归查询：逐层向上遍历父节点，深度+1
            SELECT
                c.id,
                c.parent_id,
                c.tenant_id,
                c.is_deleted,
                p.depth + 1
            FROM app_album_menus c
            INNER JOIN cte_path p ON c.id = p.parent_id
        )
        -- 结果处理：取最大深度值（即到根节点的层级数）
        SELECT
            IFNULL(MAX(depth),1)
        FROM cte_path;
    </select>
    <select id="getMaxOrderNum">
        select
            ifnull(max(order_num),0)
        from app_album_menus
        where parent_id = #{id}
    </select>
</mapper>
