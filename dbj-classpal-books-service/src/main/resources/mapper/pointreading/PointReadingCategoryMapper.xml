<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.pointreading.PointReadingCategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dbj.classpal.books.service.entity.pointreading.PointReadingCategory">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="parent_id" property="parentId" />
        <result column="is_default" property="isDefault" />
        <result column="sort_num" property="sortNum" />
        <result column="status" property="status" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
        <result column="version" property="version" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, parent_id, is_default, sort_num, status, 
        create_by, create_time, update_by, update_time, is_deleted, version, tenant_id
    </sql>

    <!-- 查询条件 -->
    <sql id="Query_Condition">
        <where>
            is_deleted = 0
            <if test="query.name != null and query.name != ''">
                AND name LIKE CONCAT('%', #{query.name}, '%')
            </if>
            <if test="query.parentId != null">
                AND parent_id = #{query.parentId}
            </if>
            <if test="query.status != null">
                AND status = #{query.status}
            </if>
            <if test="query.isDefault != null">
                AND is_default = #{query.isDefault}
            </if>
            <if test="query.tenantId != null">
                AND tenant_id = #{query.tenantId}
            </if>
        </where>
    </sql>

    <!-- 分页查询点读书分类 -->
    <select id="selectPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM point_reading_category
        <include refid="Query_Condition" />
        ORDER BY sort_num ASC, id ASC
    </select>

    <!-- 查询所有分类（用于构建树形结构） -->
    <select id="selectAllForTree" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM point_reading_category
        <include refid="Query_Condition" />
        ORDER BY 
            CASE WHEN parent_id IS NULL OR parent_id = 0 THEN 0 ELSE 1 END,
            sort_num ASC, 
            id ASC
    </select>

    <!-- 查询子分类数量 -->
    <select id="countChildren" resultType="int">
        SELECT COUNT(1)
        FROM point_reading_category
        WHERE parent_id = #{parentId}
        AND is_deleted = 0
    </select>

</mapper>
