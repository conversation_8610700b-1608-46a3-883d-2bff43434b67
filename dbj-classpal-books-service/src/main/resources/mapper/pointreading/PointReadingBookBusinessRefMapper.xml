<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.pointreading.PointReadingBookBusinessRefMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dbj.classpal.books.service.entity.pointreading.PointReadingBookBusinessRef">
        <id column="id" property="id" />
        <result column="book_id" property="bookId" />
        <result column="business_type" property="businessType" />
        <result column="business_id" property="businessId" />
        <result column="sort_num" property="sortNum" />
        <result column="status" property="status" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
        <result column="version" property="version" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, book_id, business_type, business_id, sort_num, status,
        create_by, create_time, update_by, update_time, is_deleted, version, tenant_id
    </sql>

    <!-- 根据点读书ID查询业务关联列表 -->
    <select id="selectByBookId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM point_reading_book_business_ref
        WHERE book_id = #{bookId}
        AND is_deleted = 0
        ORDER BY sort_num ASC, id ASC
    </select>

    <!-- 根据业务类型和业务ID查询关联的点读书列表 -->
    <select id="selectByBusiness" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM point_reading_book_business_ref
        WHERE business_type = #{businessType}
        AND business_id = #{businessId}
        AND is_deleted = 0
        ORDER BY sort_num ASC, id ASC
    </select>

    <!-- 根据业务类型查询关联的点读书列表 -->
    <select id="selectByBusinessType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM point_reading_book_business_ref
        WHERE business_type = #{businessType}
        AND is_deleted = 0
        ORDER BY sort_num ASC, id ASC
    </select>

    <!-- 查询业务关联的点读书数量 -->
    <select id="countByBusiness" resultType="int">
        SELECT COUNT(1)
        FROM point_reading_book_business_ref
        WHERE business_type = #{businessType}
        AND business_id = #{businessId}
        AND is_deleted = 0
    </select>

    <!-- 删除点读书的所有业务关联 -->
    <update id="deleteByBookId">
        UPDATE point_reading_book_business_ref
        SET is_deleted = 1, update_time = NOW()
        WHERE book_id = #{bookId}
        AND is_deleted = 0
    </update>

    <!-- 删除业务的所有点读书关联 -->
    <update id="deleteByBusiness">
        UPDATE point_reading_book_business_ref
        SET is_deleted = 1, update_time = NOW()
        WHERE business_type = #{businessType}
        AND business_id = #{businessId}
        AND is_deleted = 0
    </update>

</mapper>
