<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.pointreading.PointReadingBookMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dbj.classpal.books.service.entity.pointreading.PointReadingBook">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="category_id" property="categoryId" />
        <result column="cover_id" property="coverId" />
        <result column="cover_url" property="coverUrl" />
        <result column="cover_name" property="coverName" />
        <result column="original_file_id" property="originalFileId" />
        <result column="original_url" property="originalUrl" />
        <result column="original_name" property="originalName" />
        <result column="original_md5" property="originalMd5" />
        <result column="parse_status" property="parseStatus" />
        <result column="launch_status" property="launchStatus" />
        <result column="sort_num" property="sortNum" />
        <result column="status" property="status" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
        <result column="version" property="version" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, category_id, cover_id, cover_url, cover_name, 
        original_file_id, original_url, original_name, original_md5, 
        parse_status, launch_status, sort_num, status,
        create_by, create_time, update_by, update_time, is_deleted, version, tenant_id
    </sql>

    <!-- 查询条件 -->
    <sql id="Query_Condition">
        <where>
            is_deleted = 0
            <if test="query.name != null and query.name != ''">
                AND name LIKE CONCAT('%', #{query.name}, '%')
            </if>
            <if test="query.categoryId != null">
                AND category_id = #{query.categoryId}
            </if>
            <if test="query.parseStatus != null">
                AND parse_status = #{query.parseStatus}
            </if>
            <if test="query.launchStatus != null">
                AND launch_status = #{query.launchStatus}
            </if>
            <if test="query.status != null">
                AND status = #{query.status}
            </if>
            <if test="query.originalMd5 != null and query.originalMd5 != ''">
                AND original_md5 = #{query.originalMd5}
            </if>
            <if test="query.tenantId != null">
                AND tenant_id = #{query.tenantId}
            </if>
        </where>
    </sql>

    <!-- 分页查询点读书 -->
    <select id="selectPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM point_reading_book
        <include refid="Query_Condition" />
        ORDER BY sort_num ASC, id DESC
    </select>

    <!-- 根据MD5查询点读书 -->
    <select id="selectByMd5" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM point_reading_book
        WHERE original_md5 = #{md5}
        AND is_deleted = 0
        LIMIT 1
    </select>

    <!-- 统计分类下的点读书数量 -->
    <select id="countByCategory" resultType="int">
        SELECT COUNT(1)
        FROM point_reading_book
        WHERE category_id = #{categoryId}
        AND is_deleted = 0
    </select>

</mapper>
