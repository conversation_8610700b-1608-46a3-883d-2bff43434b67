<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.pointreading.PointReadingChapterMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dbj.classpal.books.service.entity.pointreading.PointReadingChapter">
        <id column="id" property="id" />
        <result column="menu_id" property="menuId" />
        <result column="name" property="name" />
        <result column="image_id" property="imageId" />
        <result column="image_url" property="imageUrl" />
        <result column="image_name" property="imageName" />
        <result column="image_source_type" property="imageSourceType" />
        <result column="hotspot_count" property="hotspotCount" />
        <result column="media_count" property="mediaCount" />
        <result column="media_type" property="mediaType" />
        <result column="sort_num" property="sortNum" />
        <result column="status" property="status" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
        <result column="version" property="version" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, menu_id, name, image_id, image_url, image_name, image_source_type,
        hotspot_count, media_count, media_type, sort_num, status,
        create_by, create_time, update_by, update_time, is_deleted, version, tenant_id
    </sql>

    <!-- 查询条件 -->
    <sql id="Query_Condition">
        <where>
            is_deleted = 0
            <if test="query.name != null and query.name != ''">
                AND name LIKE CONCAT('%', #{query.name}, '%')
            </if>
            <if test="query.menuId != null">
                AND menu_id = #{query.menuId}
            </if>
            <if test="query.status != null">
                AND status = #{query.status}
            </if>
            <if test="query.tenantId != null">
                AND tenant_id = #{query.tenantId}
            </if>
        </where>
    </sql>

    <!-- 分页查询点读书页面 -->
    <select id="selectPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM point_reading_page
        <include refid="Query_Condition" />
        ORDER BY sort_num ASC, id ASC
    </select>

    <!-- 查询目录下的页面列表 -->
    <select id="selectByMenuId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM point_reading_page
        WHERE menu_id = #{menuId}
        AND is_deleted = 0
        ORDER BY sort_num ASC, id ASC
    </select>

    <!-- 查询目录下的页面数量 -->
    <select id="countByMenu" resultType="int">
        SELECT COUNT(1)
        FROM point_reading_page
        WHERE menu_id = #{menuId}
        AND is_deleted = 0
    </select>

</mapper>
