<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.pointreading.PointReadingHotspotMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dbj.classpal.books.service.entity.pointreading.PointReadingHotspot">
        <id column="id" property="id" />
        <result column="chapter_id" property="chapterId" />
        <result column="name" property="name" />
        <result column="node_id" property="nodeId" />
        <result column="parent_node_id" property="parentNodeId" />
        <result column="node_name" property="nodeName" />
        <result column="node_level" property="nodeLevel" />
        <result column="area_type" property="areaType" />
        <result column="event_type" property="eventType" />
        <result column="coordinate_x" property="coordinateX" />
        <result column="coordinate_y" property="coordinateY" />
        <result column="width" property="width" />
        <result column="height" property="height" />
        <result column="media_id" property="mediaId" />
        <result column="media_url" property="mediaUrl" />
        <result column="media_name" property="mediaName" />
        <result column="media_type" property="mediaType" />
        <result column="media_source" property="mediaSource" />
        <result column="follow_read" property="followRead" />
        <result column="verify_text" property="verifyText" />
        <result column="sort_num" property="sortNum" />
        <result column="status" property="status" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
        <result column="version" property="version" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, chapter_id, name, node_id, parent_node_id, node_name, node_level, area_type, event_type, coordinate_x, coordinate_y, width, height,
        media_id, media_url, media_name, media_type, media_source, follow_read, verify_text,
        sort_num, status, create_by, create_time, update_by, update_time, is_deleted, version, tenant_id
    </sql>

    <!-- 查询条件 -->
    <sql id="Query_Condition">
        <where>
            is_deleted = 0
            <if test="query.name != null and query.name != ''">
                AND name LIKE CONCAT('%', #{query.name}, '%')
            </if>
            <if test="query.chapterId != null">
                AND chapter_id = #{query.chapterId}
            </if>
            <if test="query.nodeId != null and query.nodeId != ''">
                AND node_id = #{query.nodeId}
            </if>
            <if test="query.parentNodeId != null and query.parentNodeId != ''">
                AND parent_node_id = #{query.parentNodeId}
            </if>
            <if test="query.nodeLevel != null">
                AND node_level = #{query.nodeLevel}
            </if>
            <if test="query.areaType != null">
                AND area_type = #{query.areaType}
            </if>
            <if test="query.eventType != null">
                AND event_type = #{query.eventType}
            </if>
            <if test="query.mediaType != null">
                AND media_type = #{query.mediaType}
            </if>
            <if test="query.status != null">
                AND status = #{query.status}
            </if>
            <if test="query.tenantId != null">
                AND tenant_id = #{query.tenantId}
            </if>
        </where>
    </sql>

    <!-- 分页查询点读书热点区域 -->
    <select id="selectPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM point_reading_hotspot
        <include refid="Query_Condition" />
        ORDER BY sort_num ASC, id ASC
    </select>

    <!-- 查询章节下的热点列表 -->
    <select id="selectByChapterId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM point_reading_hotspot
        WHERE chapter_id = #{chapterId}
        AND is_deleted = 0
        ORDER BY sort_num ASC, id ASC
    </select>

    <!-- 查询章节下的热点数量 -->
    <select id="countByChapter" resultType="int">
        SELECT COUNT(1)
        FROM point_reading_hotspot
        WHERE chapter_id = #{chapterId}
        AND is_deleted = 0
    </select>

</mapper>
