<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.pointreading.PointReadingModeConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dbj.classpal.books.service.entity.pointreading.PointReadingModeConfig">
        <id column="id" property="id" />
        <result column="config_dict_id" property="configDictId" />
        <result column="book_id" property="bookId" />
        <result column="display_name" property="displayName" />
        <result column="process_status" property="processStatus" />
        <result column="original_file_id" property="originalFileId" />
        <result column="original_url" property="originalUrl" />
        <result column="original_name" property="originalName" />
        <result column="original_md5" property="originalMd5" />
        <result column="sort_num" property="sortNum" />
        <result column="status" property="status" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
        <result column="version" property="version" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, config_dict_id, book_id, display_name, process_status, original_file_id, original_url, original_name, original_md5,
        sort_num, status, create_by, create_time, update_by, update_time, is_deleted, version, tenant_id
    </sql>

    <!-- 查询条件 -->
    <sql id="Query_Condition">
        <where>
            is_deleted = 0
            <if test="query.displayName != null and query.displayName != ''">
                AND display_name LIKE CONCAT('%', #{query.displayName}, '%')
            </if>
            <if test="query.bookId != null">
                AND book_id = #{query.bookId}
            </if>
            <if test="query.configDictId != null">
                AND config_dict_id = #{query.configDictId}
            </if>
            <if test="query.processStatus != null">
                AND process_status = #{query.processStatus}
            </if>
            <if test="query.status != null">
                AND status = #{query.status}
            </if>
            <if test="query.tenantId != null">
                AND tenant_id = #{query.tenantId}
            </if>
        </where>
    </sql>

    <!-- 分页查询点读书模式配置 -->
    <select id="selectPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM point_reading_mode_config
        <include refid="Query_Condition" />
        ORDER BY sort_num ASC, id ASC
    </select>

    <!-- 查询点读书下的模式配置列表 -->
    <select id="selectByBookId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM point_reading_mode_config
        WHERE book_id = #{bookId}
        AND is_deleted = 0
        ORDER BY sort_num ASC, id ASC
    </select>

    <!-- 查询点读书下的模式配置数量 -->
    <select id="countByBook" resultType="int">
        SELECT COUNT(1)
        FROM point_reading_mode_config
        WHERE book_id = #{bookId}
        AND is_deleted = 0
    </select>

    <!-- 根据字典ID和点读书ID查询配置 -->
    <select id="selectByDictAndBook" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM point_reading_mode_config
        WHERE config_dict_id = #{configDictId}
        AND book_id = #{bookId}
        AND is_deleted = 0
        LIMIT 1
    </select>

</mapper>
