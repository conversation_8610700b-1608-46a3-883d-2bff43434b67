<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.ebooks.AppEBookshelfMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dbj.classpal.books.service.entity.product.AppEBookshelf">
        <id column="id" property="id" />
        <result column="shelf_title" property="shelfTitle" />
        <result column="cover_url" property="coverUrl" />
        <result column="blurb" property="blurb" />
        <result column="launch_status" property="launchStatus" />
        <result column="sort_num" property="sortNum" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 分页查询书架列表 -->
    <select id="pageBookshelf" resultMap="BaseResultMap">
        SELECT 
            *
        FROM 
            product_bookshelf
        WHERE 
            is_deleted = 0
        <if test="shelfTitle != null and shelfTitle != ''">
            AND shelf_title LIKE CONCAT('%', #{shelfTitle}, '%')
        </if>
        <if test="isHide != null">
            AND is_hide = #{isHide}
        </if>
        <if test="launchStatus != null">
            AND launch_status = #{launchStatus}
        </if>
        ORDER BY sort_num DESC, create_time DESC
    </select>

    <!-- H5分页查询书架列表（支持书城关联查询） -->
    <select id="pageForH5" resultType="com.dbj.classpal.books.service.entity.product.AppEBookshelf">
        SELECT DISTINCT s.*
        FROM app_ebookshelf s
        <if test="query.storeId != null">
            INNER JOIN app_ebookstore_shelf_ref ssr ON s.id = ssr.shelf_id AND ssr.store_id = #{query.storeId}
        </if>
        <where>
            s.launch_status = 1
            AND s.is_deleted = 0
            <if test="query.shelfTitle != null and query.shelfTitle != ''">
                AND s.shelf_title LIKE CONCAT('%', #{query.shelfTitle}, '%')
            </if>
        </where>
        ORDER BY s.sort_num DESC, s.create_time DESC
    </select>

</mapper>