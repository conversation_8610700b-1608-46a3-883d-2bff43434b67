<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.ebooks.AppEBookshelfBookRefMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dbj.classpal.books.service.entity.product.AppEBookshelfBookRef">
        <id column="id" property="id" />
        <result column="shelf_id" property="shelfId" />
        <result column="book_id" property="bookId" />
        <result column="sort_num" property="sortNum" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>
    
    <!-- 根据书架ID查询关联的单书列表 -->
    <select id="getBooksByShelfId" resultType="com.dbj.classpal.books.service.entity.product.AppEBook">
        SELECT 
            b.*
        FROM 
            product_book b
        JOIN 
            product_bookshelf_book_ref ref ON b.id = ref.book_id
        WHERE 
            ref.shelf_id = #{shelfId}
            AND ref.is_deleted = 0
            AND b.is_deleted = 0
        ORDER BY 
            ref.sort_num DESC, ref.create_time ASC
    </select>
    
    <!-- 批量插入书架-单书关联 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO product_bookshelf_book_ref (
            shelf_id, 
            book_id, 
            sort_num, 
            tenant_id, 
            create_by, 
            create_time,
            status
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.shelfId}, 
                #{item.bookId}, 
                #{item.sortNum},
                #{item.tenantId},
                #{item.createBy},
                now(),
                1
            )
        </foreach>
    </insert>
    
</mapper> 