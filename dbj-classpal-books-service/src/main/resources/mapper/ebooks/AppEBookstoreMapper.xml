<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.ebooks.AppEBookstoreMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dbj.classpal.books.service.entity.product.AppEBookstore">
        <id column="id" property="id" />
        <result column="store_title" property="storeTitle" />
        <result column="pic_url" property="picUrl" />
        <result column="blurb" property="blurb" />
        <result column="launch_status" property="launchStatus" />
        <result column="sort_num" property="sortNum" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 分页查询书城列表 -->
    <select id="pageBookstore" resultMap="BaseResultMap">
        SELECT 
            *
        FROM 
            product_bookstore
        WHERE 
            is_deleted = 0
        <if test="storeTitle != null and storeTitle != ''">
            AND store_title LIKE CONCAT('%', #{storeTitle}, '%')
        </if>
        <if test="isHide != null">
            AND is_hide = #{isHide}
        </if>
        <if test="launchStatus != null">
            AND launch_status = #{launchStatus}
        </if>
        ORDER BY sort_num DESC, create_time DESC
    </select>

</mapper> 