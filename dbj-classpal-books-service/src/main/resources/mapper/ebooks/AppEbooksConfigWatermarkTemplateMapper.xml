<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.ebooks.AppEbooksConfigWatermarkTemplateMapper">
    <select id="pageInfo" resultType="com.dbj.classpal.books.common.dto.ebooks.AppEbooksConfigWatermarkTemplateQueryDTO">
        SELECT
            aecwt.*
        FROM app_ebooks_config_watermark_template aecwt
        WHERE aecwt.is_deleted = 0
        <if test="bo.templateName != null and bo.templateName != ''">
            AND aecwt.template_name LIKE CONCAT('%',#{bo.templateName},'%')
        </if>
        ORDER BY aecwt.sort DESC,aecwt.create_time DESC
    </select>
</mapper>
