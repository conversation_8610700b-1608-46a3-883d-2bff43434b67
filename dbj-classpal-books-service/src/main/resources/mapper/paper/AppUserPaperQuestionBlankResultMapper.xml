<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.paper.AppUserPaperQuestionBlankResultMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dbj.classpal.books.service.entity.paper.AppUserPaperQuestionBlankResult">
        <id column="id" property="id"/>
        <result column="user_paper_id" property="userPaperId"/>
        <result column="question_id" property="questionId"/>
        <result column="blank_index" property="blankIndex"/>
        <result column="user_answer" property="userAnswer"/>
        <result column="is_correct" property="isCorrect"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_paper_id, question_id, blank_index, user_answer, is_correct, create_time, update_time, status
    </sql>

</mapper> 