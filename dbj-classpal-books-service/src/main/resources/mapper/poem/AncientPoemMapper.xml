<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.poem.AncientPoemMapper">

    <select id="getAncientPoemPage" resultType="com.dbj.classpal.books.client.dto.poem.AncientPoemDTO">
        select
            a.*
        from
            ancient_poem a
        where
            a.is_deleted = 0
            <if test="bo.title != null and bo.title != ''">
                and title like concat('%',#{bo.title},'%')
            </if>
            <if test="bo.author != null and bo.author != ''">
                and author like concat('%',#{bo.author},'%')
            </if>
            <if test="bo.dynasty != null and bo.dynasty != ''">
                and dynasty like concat('%',#{bo.dynasty},'%')
            </if>
            <if test="bo.grades != null and bo.grades.size() > 0">
                and grade in
                <foreach item="item" collection="bo.grades" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="bo.isOut != null">
                and is_out = #{bo.isOut}
            </if>
            <if test="bo.subTreeIds != null and bo.subTreeIds.size() > 0">
                and a.classify_id in
                <foreach item="item" collection="bo.getSubTreeIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            order by
                a.sort desc, a.create_time asc, a.id asc
    </select>
</mapper>
