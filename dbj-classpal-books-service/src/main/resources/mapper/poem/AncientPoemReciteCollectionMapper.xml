<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.poem.AncientPoemReciteCollectionMapper">


    <select id="listAncientPoemReciteCollection" resultType="com.dbj.classpal.books.client.dto.poem.AncientPoemReciteCollectionDTO">
        select * from ancient_poem_recite_collection
        <where>
            is_deleted = 0
            and category_id = #{bo.categoryId}
            <if test="bo.title != null and bo.title != ''">
                and title like concat('%',#{bo.title},'%')
            </if>
            <if test="bo.status != null">
                and status = #{bo.status}
            </if>
        </where>
        order by sort asc
    </select>
    <select id="pageAncientPoemReciteCollection" resultType="com.dbj.classpal.books.client.dto.poem.app.AppAncientPoemReciteCollectionPageDTO">
        select * from ancient_poem_recite_collection
        <where>
            is_deleted = 0
            and status = 1
            and category_id = #{bo.categoryId}
            <if test="bo.title != null and bo.title != ''">
                and title like concat('%',#{bo.title},'%')
            </if>
        </where>
        order by sort asc
    </select>
</mapper>
