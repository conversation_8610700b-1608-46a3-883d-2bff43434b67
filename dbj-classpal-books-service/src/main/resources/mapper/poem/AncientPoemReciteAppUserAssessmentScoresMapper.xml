<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.poem.AncientPoemReciteAppUserAssessmentScoresMapper">


    <select id="pageAncientPoemReciteAppUserAssessmentScore" resultType="com.dbj.classpal.books.client.dto.poem.app.AncientPoemReciteAppUserAssessmentScorePageDTO">
        select
            id,
            poem_id as ancientPoemId,
            app_user_id as appUserId,
            total_score as totalScore
        from ancient_poem_recite_app_user_assessment_scores
        where is_deleted = 0
        <if test="bo.appUserId != null">
            and app_user_id = #{bo.appUserId}
        </if>
        order by assessment_time desc
    </select>
</mapper>
