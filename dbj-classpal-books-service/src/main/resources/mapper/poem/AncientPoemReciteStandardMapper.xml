<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.poem.AncientPoemReciteStandardMapper">


    <select id="getByScore" resultType="com.dbj.classpal.books.service.entity.poem.AncientPoemReciteStandard">
        select
            id,
            lowest_score,
            highest_score,
            lowest_percentage,
            highest_percentage,
            remark
        from ancient_poem_recite_standard
        where
            is_deleted = 0
            and <![CDATA[ lowest_score <=  #{score} ]]>
            and  type = #{type}
            order by lowest_score DESC
            limit 1
    </select>
</mapper>
