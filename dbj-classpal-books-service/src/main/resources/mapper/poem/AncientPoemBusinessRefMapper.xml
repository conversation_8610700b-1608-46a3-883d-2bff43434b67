<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.poem.AncientPoemBusinessRefMapper">

    <select id="getCount"  resultType="com.dbj.classpal.books.common.dto.poem.AncientPoemBusinessRefCountDTO">
        SELECT
            business_id AS businessId,
            COUNT(1) AS poemNum
        FROM ancient_poem_business_ref
        WHERE business_id IN
        <foreach item="item" collection="businessIds" separator="," open="(" close=")" index="index">
            #{item}
        </foreach>
        AND business_type = #{businessType}
        and is_deleted = 0
        GROUP BY business_id
    </select>
    <select id="listAncientPoemBusinessRef"  resultType="com.dbj.classpal.books.client.dto.poem.AncientPoemBusinessRefListDTO">
        SELECT
            a.id,
            a.ancient_poem_id AS ancientPoemId,
            a.business_id AS businessId,
            a.business_type AS businessType,
            b.title,
            b.author,
            b.grade,
            b.cover_url,
            b.is_out,
            b.dynasty
        FROM
            ancient_poem_business_ref a,
            ancient_poem b
        WHERE
            a.is_deleted = 0
            AND b.is_deleted = 0
            AND a.ancient_poem_id = b.id
        AND a.business_type = #{businessType}
        AND a.business_id = #{businessId}
        <if test="title != null">
            AND b.title LIKE CONCAT('%',#{title},'%')
        </if>
        <if test="author != null">
            AND b.author LIKE CONCAT('%',#{author},'%')
        </if>
        <if test="dynasty != null">
            AND b.dynasty LIKE CONCAT('%',#{dynasty},'%')
        </if>
        <if test="grade != null and grade.size() > 0 ">
            AND b.grade IN
            <foreach item="item" collection="grade" separator="," open="(" close=")" index="index">
                #{item}
            </foreach>
        </if>
        <if test="isOut != null">
            AND b.is_out = #{isOut}
        </if>
        order by a.sort asc,a.create_time asc
    </select>
    <select id="pageAncientPoemBusinessRef"  resultType="com.dbj.classpal.books.client.dto.poem.app.AncientPoemBusinessRefPageDTO">
        SELECT
        a.id,
        a.ancient_poem_id AS ancientPoemId,
        a.business_id AS businessId,
        a.business_type AS businessType,
        b.title,
        b.author,
        b.grade,
        b.is_out,
        b.verify_text,
        b.introduction,
        b.background_url,
        b.cover_url,
        b.dynasty
        FROM
        ancient_poem_business_ref a,
        ancient_poem b
        WHERE
        a.is_deleted = 0
        AND b.is_deleted = 0
        AND a.ancient_poem_id = b.id
        AND a.business_type = #{bo.businessType}
        AND a.business_id = #{bo.businessId}
        <if test="bo.title != null">
            AND b.title LIKE CONCAT('%',#{bo.title},'%')
        </if>
        <if test="bo.author != null">
            AND b.author LIKE CONCAT('%',#{bo.author},'%')
        </if>
        <if test="bo.dynasty != null">
            AND b.dynasty LIKE CONCAT('%',#{bo.dynasty},'%')
        </if>
        <if test="bo.grade != null and bo.grade.size() > 0 ">
            AND b.grade IN
            <foreach item="item" collection="bo.grade" separator="," open="(" close=")" index="index">
                #{item}
            </foreach>
        </if>
        <if test="bo.isOut != null">
            AND b.is_out = #{bo.isOut}
        </if>
        order by a.sort asc,a.create_time asc
    </select>

</mapper>
