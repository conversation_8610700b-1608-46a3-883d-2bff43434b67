<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.evaluation.AppEvaluationMapper">
    <select id="pageInfo" resultType="com.dbj.classpal.books.service.entity.evaluation.AppEvaluation">
        SELECT
           ae.*
        FROM app_evaluation ae
        WHERE ae.is_deleted = 0
        <if test="bo.evaluationName != null and bo.evaluationName != ''">
            AND ae.evaluation_name LIKE CONCAT('%', #{bo.evaluationName}, '%')
        </if>
        <if test="bo.evaluationStatus != null">
            AND ae.evaluation_status = #{bo.evaluationStatus}
        </if>
        <if test="bo.evaluationVisible != null">
            AND ae.evaluation_visible = #{bo.evaluationVisible}
        </if>
        <if test="bo.evaluationOpen != null">
            AND ae.evaluation_open = #{bo.evaluationOpen}
        </if>
        order by ae.create_time desc
    </select>
</mapper>
