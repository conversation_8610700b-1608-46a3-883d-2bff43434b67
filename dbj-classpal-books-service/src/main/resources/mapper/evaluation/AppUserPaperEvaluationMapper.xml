<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.evaluation.AppUserPaperEvaluationMapper">
    <select id="pageInfo" resultType="com.dbj.classpal.books.service.entity.evaluation.AppUserPaperEvaluation">
        SELECT
            aupe.*
        FROM app_user_paper_evaluation aupe
        WHERE aupe.app_evaluation_id = #{bo.id} and aupe.is_generated = 1 and aupe.is_deleted = 0
        <if test="bo.userId != null and bo.userId != ''">
            AND aupe.app_user_id = #{bo.userId}
        </if>
        <if test="bo.region != null and bo.region != ''">
            AND aupe.region like concat('%',#{bo.region},'%')
        </if>
        <if test="bo.gradeIds != null and bo.gradeIds.size() >0 ">
            AND aupe.grade_id in
            <foreach collection="bo.gradeIds" item="grade" open="(" separator="," close=")">
                #{grade}
            </foreach>
        </if>
        order by aupe.create_time desc
    </select>
</mapper>
