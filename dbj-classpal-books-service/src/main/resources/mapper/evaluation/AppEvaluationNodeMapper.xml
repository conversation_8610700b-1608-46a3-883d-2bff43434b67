<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.evaluation.AppEvaluationNodeMapper">
    <select id="getMaxSortNum">
        select
            ifnull(max(app_evaluation_order),0)
        from app_evaluation_node
        where app_evaluation_id = #{id} and is_deleted = 0
    </select>
</mapper>
