<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.material.AppMaterialMapper">
    <select id="pageTree" resultType="com.dbj.classpal.books.service.entity.material.AppMaterial">
        <if test="bo.materialName != null and bo.materialName != ''">
            WITH RECURSIVE cte AS (
                    SELECT
                        am.id,
                        am.is_root,
                        am.parent_id,
                        am.material_name,
                        am.first_pin_yin,
                        am.pin_yin,
                        am.material_type,
                        am.material_extension,
                        am.material_path,
                        am.material_origin_url,
                        am.material_size,
                        am.material_status,
                        am.material_caption,
                        am.material_md5,
                        am.job_id,
                        am.order_num,
                        am.create_time,
                        am.tenant_id,
                        am.is_deleted
                    FROM app_material am
                    where am.is_root != 1
                    <if test="bo.parentId != null and  bo.parentId != ''">
                        and am.parent_id = #{bo.parentId} -- 起始节点 ID
                    </if>
                UNION ALL
                    SELECT
                        tn.id,
                        tn.is_root,
                        tn.parent_id,
                        tn.material_name,
                        tn.first_pin_yin,
                        tn.pin_yin,
                        tn.material_type,
                        tn.material_extension,
                        tn.material_path,
                        tn.material_origin_url,
                        tn.material_size,
                        tn.material_status,
                        tn.material_caption,
                        tn.material_md5,
                        tn.job_id,
                        tn.order_num,
                        tn.create_time,
                        tn.tenant_id,
                        tn.is_deleted
                    FROM app_material tn
                    INNER JOIN cte as tnc ON tn.parent_id = tnc.id
            )
        </if>
        SELECT
            p.id,
            p.is_root,
            p.parent_id,
            p.material_name,
            p.first_pin_yin,
            p.pin_yin,
            p.material_type,
            p.material_extension,
            p.material_path,
            p.material_origin_url,
            p.material_size,
            p.material_status,
            p.material_caption,
            p.material_md5,
            p.job_id,
            p.order_num,
            p.create_time,
            p.is_deleted
        FROM
        <choose>
            <when test="bo.materialName != null and  bo.materialName != ''">
                cte as p
            </when>
            <otherwise>
                app_material as p
            </otherwise>
        </choose>
        where p.is_deleted = 0
        <if test="bo.materialType != null and  bo.materialType != ''">
            <choose>
                <when test="bo.materialName != null and  bo.materialName != ''">
                    and p.material_type = #{bo.materialType}
                </when>
                <otherwise>
                    and p.material_type in (1,#{bo.materialType})
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="bo.materialName != null and  bo.materialName != ''">
                and p.material_name LIKE CONCAT('%', #{bo.materialName}, '%')
            </when>
            <otherwise>
                <if test="bo.parentId != null and  bo.parentId != ''">
                    and p.parent_id = #{bo.parentId}
                </if>
            </otherwise>
        </choose>
        <if test="bo.filters != null and bo.filters.size() > 0">
            AND (
            <foreach collection="bo.filters" item="filter" separator=" OR ">
                p.material_type = #{filter.materialType}
                <if test="filter.materialExtensions != null and filter.materialExtensions.size() > 0">
                    AND p.material_extension IN
                    <foreach collection="filter.materialExtensions" item="extension" open="(" separator="," close=")">
                        #{extension}
                    </foreach>
                </if>
            </foreach>
            )
        </if>
        <choose>
            <when test="bo.orders != null and bo.orders.size() > 0">
                order by IF(p.material_type = 1, 9999, 0) desc
                <foreach collection="bo.orders" item="orderItem">
                    <if test="orderItem.column == 'material_name'">
                        <if test="orderItem.asc == true">
                            ,p.first_pin_yin asc,p.pin_yin asc,p.material_name asc
                        </if>
                        <if test="orderItem.asc == false">
                            ,p.first_pin_yin desc,p.pin_yin desc,p.material_name desc
                        </if>
                    </if>
                    <if test="orderItem.column == 'create_time'">
                        <if test="orderItem.asc == true">
                            ,p.create_time asc
                        </if>
                        <if test="orderItem.asc == false">
                            ,p.create_time desc
                        </if>
                    </if>
                </foreach>
            </when>
            <otherwise>
                order by IF(p.material_type = 1, 9999, p.material_type) desc,p.first_pin_yin asc,p.pin_yin asc,p.material_name asc
            </otherwise>
        </choose>
    </select>

    <select id="getMaterialParentsPath" resultType="com.dbj.classpal.books.service.entity.material.AppMaterial">
        WITH RECURSIVE cte AS (
            SELECT
                am.id,
                am.is_root,
                am.parent_id,
                am.material_name,
                am.material_type,
                am.material_extension,
                am.material_path,
                am.material_origin_url,
                am.material_size,
                am.material_status,
                am.material_caption,
                am.job_id,
                am.order_num,
                am.create_time,
                am.tenant_id,
                0 AS level,
                am.is_deleted
            FROM app_material am
            where am.is_root != 1 and am.id = #{id}  -- 初始子节点ID
            UNION ALL
            SELECT
                t.id,
                t.is_root,
                t.parent_id,
                t.material_name,
                t.material_type,
                t.material_extension,
                t.material_path,
                t.material_origin_url,
                t.material_size,
                t.material_status,
                t.material_caption,
                t.job_id,
                t.order_num,
                t.create_time,
                t.tenant_id,
                tc.level + 1,
                t.is_deleted
            FROM app_material t
            INNER JOIN cte as tc ON t.id = tc.parent_id
        )
        SELECT
            p.id,
            p.is_root,
            p.parent_id,
            p.material_name,
            p.material_type,
            p.material_extension,
            p.material_path,
            p.material_origin_url,
            p.material_size,
            p.material_status,
            p.material_caption,
            p.job_id,
            p.order_num,
            p.create_time,
            p.level,
            p.is_deleted
        FROM cte as p
        WHERE p.parent_id IS NOT NULL and p.is_deleted = 0 -- 排除根节点自身（可选）
        ORDER BY p.level DESC
    </select>

    <select id="getMaterialParentsNames">
        WITH RECURSIVE cte AS (
            SELECT
                am.id,
                am.parent_id,
                am.material_name,
                am.tenant_id,
                0 AS level,
                am.is_deleted
            FROM app_material am
            where am.is_root != 1 and am.id = #{id}  -- 初始子节点ID
            UNION ALL
            SELECT
                t.id,
                t.parent_id,
                t.material_name,
                t.tenant_id,
                tc.level + 1,
                t.is_deleted
            FROM app_material t
            INNER JOIN cte as tc ON t.id = tc.parent_id
        )
        SELECT
            ifnull(GROUP_CONCAT(
                    DISTINCT p.material_name
                    ORDER BY p.level desc
                    SEPARATOR '/'
                ),"") as material_dir
        FROM cte as p
        WHERE p.id != #{id} and  p.is_deleted = 0 -- 排除根节点自身（可选）
        GROUP BY p.is_deleted
    </select>

    <select id="checkAimIdInChildren" resultType="Integer">
        WITH RECURSIVE cte AS (
            SELECT
                am.id,
                am.is_root,
                am.parent_id,
                am.material_name,
                am.material_type,
                am.material_extension,
                am.material_path,
                am.material_origin_url,
                am.material_size,
                am.material_status,
                am.job_id,
                am.order_num,
                am.create_time,
                am.tenant_id,
                am.is_deleted
            FROM app_material am
            WHERE am.parent_id = #{bo.id}   -- 替换为父节点ID
            UNION ALL
            SELECT
                t.id,
                t.is_root,
                t.parent_id,
                t.material_name,
                t.material_type,
                t.material_extension,
                t.material_path,
                t.material_origin_url,
                t.material_size,
                t.material_status,
                t.job_id,
                t.order_num,
                t.create_time,
                t.tenant_id,
                t.is_deleted
            FROM app_material t
            INNER JOIN cte AS tc ON t.parent_id = tc.id
        )
        SELECT EXISTS (
            SELECT
                1
            FROM cte
            WHERE cte.id = #{bo.aimParentId} and cte.is_deleted = 0  -- 替换为需判断的节点ID
        ) AS is_descendant
    </select>

    <select id="getChildrenFiles" resultType="com.dbj.classpal.books.service.entity.material.AppMaterial">
        WITH RECURSIVE cte AS (
            SELECT
                am.id,
                am.is_root,
                am.parent_id,
                am.material_name,
                am.material_type,
                am.material_extension,
                am.material_path,
                am.material_origin_url,
                am.material_size,
                am.material_status,
                am.job_id,
                am.order_num,
                am.create_time,
                am.tenant_id,
                0 AS level,
                am.is_deleted
            FROM app_material am
            where am.is_root != 1 and am.id = #{id}  -- 初始子节点ID
            UNION ALL
            SELECT
                t.id,
                t.is_root,
                t.parent_id,
                t.material_name,
                t.material_type,
                t.material_extension,
                t.material_path,
                t.material_origin_url,
                t.material_size,
                t.material_status,
                t.job_id,
                t.order_num,
                t.create_time,
                t.tenant_id,
                tc.level + 1,
                t.is_deleted
            FROM app_material t
            INNER JOIN cte as tc ON t.parent_id = tc.id
            )
        SELECT
            p.id,
            p.is_root,
            p.parent_id,
            p.material_name,
            p.material_type,
            p.material_extension,
            p.material_path,
            p.material_origin_url,
            p.material_size,
            p.material_status,
            p.job_id,
            p.order_num,
            p.create_time,
            p.level,
            p.is_deleted
        FROM cte as p
        WHERE p.parent_id IS NOT NULL and p.is_deleted = 0
        ORDER BY p.level DESC
    </select>


    <select id="getBatchDirChildrenFiles" resultType="com.dbj.classpal.books.service.entity.material.AppMaterial">
        WITH RECURSIVE cte AS (
            SELECT
                am.id,
                am.is_root,
                am.parent_id,
                am.material_name,
                am.material_type,
                am.material_extension,
                am.material_path,
                am.material_origin_url,
                am.material_size,
                am.material_status,
                am.job_id,
                am.order_num,
                am.create_time,
                am.tenant_id,
                0 AS level,
                am.is_deleted
            FROM app_material am
            where am.is_root != 1  and am.id in
            <foreach collection="ids" separator="," item="id" open="(" close=")">
                 #{id}
            </foreach>
        UNION ALL
        SELECT
            t.id,
            t.is_root,
            t.parent_id,
            t.material_name,
            t.material_type,
            t.material_extension,
            t.material_path,
            t.material_origin_url,
            t.material_size,
            t.material_status,
            t.job_id,
            t.order_num,
            t.create_time,
            t.tenant_id,
            tc.level + 1,
            t.is_deleted
        FROM app_material t
                 INNER JOIN cte as tc ON t.parent_id = tc.id
            )
        SELECT
            p.id,
            p.is_root,
            p.parent_id,
            p.material_name,
            p.material_type,
            p.material_extension,
            p.material_path,
            p.material_origin_url,
            p.material_size,
            p.material_status,
            p.job_id,
            p.order_num,
            p.create_time,
            p.level,
            p.is_deleted
        FROM cte as p
        WHERE p.parent_id IS NOT NULL and p.is_deleted = 0
        ORDER BY p.level DESC
    </select>

    <select id="sumDuration">
        select
            sum(ifnull(material_duration,0))
        from app_material
        where id in
        <foreach collection="idSet" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getPathList" resultType="com.dbj.classpal.books.client.dto.material.AppMaterialPathDTO">
        WITH RECURSIVE ancestor_path AS (
            SELECT
                m.id,
                m.parent_id,
                m.material_name,
                m.tenant_id
            FROM app_material m
            WHERE
                m.is_deleted = 0 and m.id in
                <foreach collection="materialIds" item="materialId" separator="," open="(" close=")">
                    #{materialId}
                </foreach>
            UNION ALL
            SELECT
                c.id,
                c.parent_id,
                c.material_name,
                c.tenant_id
            FROM app_material c
            JOIN ancestor_path ap ON c.id = ap.parent_id
        )
        SELECT distinct id, parent_id, material_name FROM ancestor_path
    </select>
</mapper>
