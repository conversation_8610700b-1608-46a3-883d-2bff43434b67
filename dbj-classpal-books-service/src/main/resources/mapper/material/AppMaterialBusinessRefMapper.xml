<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.material.AppMaterialBusinessRefMapper">
    <select id="refBusinessList" resultType="com.dbj.classpal.books.common.dto.material.AppMaterialBusinessRefDTO" parameterType="com.dbj.classpal.books.common.bo.material.AppMaterialBusinessRefQueryCommonBO">
        select
            ambr.*,
            am.material_name as "appMaterialName",
            am.material_caption as "appMaterialCaption",
            am.material_type as "appMaterialType",
            am.material_size as "appMaterialSize",
            am.material_path as "materialPath",
            am.material_origin_url as "materialOriginUrl",
            am.material_duration as "materialDuration",
            am.material_extension as "materialExtension"
        from app_material_business_ref ambr
        left join app_material am on am.id = ambr.app_material_id
        where ambr.is_deleted = 0
        <if test="appMaterialId != null and appMaterialId != ''">
            and ambr.app_material_id = #{appMaterialId}
        </if>
        <if test="businessId != null and businessId != ''">
            and ambr.business_id = #{businessId}
        </if>
        <if test="businessType != null and businessType != ''">
            and ambr.business_type = #{businessType}
        </if>
        <if test="businessName != null and businessName != ''">
            and ambr.business_name like concat('%',#{businessName},'%')
        </if>
        <if test="appMaterialType != null and appMaterialType != ''">
            and am.material_type = #{appMaterialType}
        </if>
        order by ambr.order_num asc,ambr.create_time asc
    </select>
    <select id="getBusinessList" resultType="com.dbj.classpal.books.common.dto.material.AppMaterialBusinessRefDTO">
        select
            ambr.*,
            am.material_name as "appMaterialName",
            am.material_caption as "appMaterialCaption",
            am.material_type as "appMaterialType",
            am.material_size as "appMaterialSize",
            am.material_path as "materialPath"
        from app_material_business_ref ambr
        left join app_material am on am.id = ambr.app_material_id
        where ambr.is_deleted = 0
            and ambr.business_id in
        <foreach collection="businessIds" item="businessId" open="(" separator="," close=")">
            #{businessId}
        </foreach>
        and am.material_type in
        <foreach collection="appMaterialTypes" item="appMaterialType" open="(" separator="," close=")">
            #{appMaterialType}
        </foreach>
        <if test="businessType != null and businessType != ''">
            and ambr.business_type = #{businessType}
        </if>
        order by ambr.order_num asc,ambr.create_time asc
    </select>


    <select id="getCommonBusinessList" resultType="com.dbj.classpal.books.client.dto.material.AppCommonMediaDTO">
        select
            ambr.business_id as businessId,
            ambr.app_material_id as materialId,
            am.material_name as materialName,
            am.material_path as materialPath,
            am.material_type as materialType,
            am.material_extension as materialExtension,
            am.material_size as materialSize,
            am.material_duration as materialDuration,
            am.material_caption as materialCaption,
            am.material_origin_url as materialOriginUrl,
            ambr.order_num as orderNum
        from
            app_material_business_ref ambr
            left join app_material am on am.id = ambr.app_material_id and am.is_deleted = 0
        where
            ambr.is_deleted = 0
            and ambr.business_id in
            <foreach collection="businessIds" item="businessId" open="(" separator="," close=")">
                #{businessId}
            </foreach>
            <if test="businessType != null and businessType != ''">
                and ambr.business_type = #{businessType}
            </if>
        order by
            ambr.order_num asc,ambr.create_time asc
    </select>

    <select id="refBusinessTypeCount" resultType="com.dbj.classpal.books.common.dto.material.AppMaterialBusinessRefTypeCountDTO">
        select
            am.material_type as "appMaterialType",
            count(1) as "appMaterialTypeRefCount"
        from app_material_business_ref ambr
        left join app_material am on am.id = ambr.app_material_id
        where ambr.is_deleted = 0  and ambr.business_id = #{bo.id} and ambr.business_type = 3
        group by am.material_type
    </select>

    <select id="pageInfo" resultType="com.dbj.classpal.books.common.dto.material.AppMaterialBusinessRefDTO">
        select
            ambr.*,
            am.material_size as "materialSize"
        from app_material_business_ref ambr
        left join app_material am on am.id = ambr.app_material_id
        where ambr.business_id = #{bo.businessId} and ambr.business_type = #{bo.businessType} and ambr.is_deleted = 0
        <if test="bo.businessName != null and bo.businessName != ''">
            and ambr.business_name like concat('%',#{bo.businessName},'%')
        </if>
        order by ambr.order_num asc,ambr.create_time asc
    </select>

</mapper>
