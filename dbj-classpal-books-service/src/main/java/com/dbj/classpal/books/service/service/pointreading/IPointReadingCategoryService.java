package com.dbj.classpal.books.service.service.pointreading;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingCategoryQueryBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingCategorySaveBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingCategoryUpdateBO;
import com.dbj.classpal.books.common.dto.pointreading.PointReadingCategoryDTO;
import com.dbj.classpal.books.service.entity.pointreading.PointReadingCategory;
import com.dbj.classpal.framework.commons.exception.BusinessException;

import com.dbj.classpal.framework.commons.request.PageInfo;
import java.util.List;

/**
 * 点读书分类 服务接口
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
public interface IPointReadingCategoryService extends IService<PointReadingCategory> {

    /**
     * 分页查询点读书分类列表
     *
     * @param pageRequest 分页查询参数
     * @return 分类分页数据
     */
    Page<PointReadingCategoryDTO> page(PageInfo<PointReadingCategoryQueryBO> pageRequest) throws BusinessException;

    /**
     * 查询分类树形结构
     *
     * @param queryBO 查询条件
     * @return 树形分类列表
     */
    List<PointReadingCategoryDTO> tree(PointReadingCategoryQueryBO queryBO) throws BusinessException;

    /**
     * 查询分类详情
     *
     * @param id 分类ID
     * @return 分类详情数据
     */
    PointReadingCategoryDTO detail(Integer id) throws BusinessException;

    /**
     * 新增分类
     *
     * @param saveBO 分类保存参数
     * @return 新增分类ID
     */
    Integer save(PointReadingCategorySaveBO saveBO) throws BusinessException;

    /**
     * 更新分类
     *
     * @param updateBO 分类更新参数
     * @return 更新结果
     */
    boolean update(PointReadingCategoryUpdateBO updateBO) throws BusinessException;

    /**
     * 删除分类
     *
     * @param id 分类ID
     * @return 删除结果
     */
    boolean delete(Integer id) throws BusinessException;

    /**
     * 批量删除分类
     *
     * @param ids ID列表
     * @return 批量删除结果
     */
    boolean deleteBatch(List<Integer> ids) throws BusinessException;

    /**
     * 批量启用分类
     *
     * @param ids ID列表
     * @return 批量启用结果
     */
    boolean enableBatch(List<Integer> ids) throws BusinessException;

    /**
     * 批量禁用分类
     *
     * @param ids ID列表
     * @return 批量禁用结果
     */
    boolean disableBatch(List<Integer> ids) throws BusinessException;
}
