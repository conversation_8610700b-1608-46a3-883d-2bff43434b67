package com.dbj.classpal.books.service.openapi.factory;

import com.dbj.classpal.books.service.openapi.credential.CredentialManager;
import com.dbj.framework.openapi.factory.DbjClientFactory;
import com.dbj.framework.openapi.provider.AuthProvider;
import com.dbj.framework.openapi.provider.DbjAuthProvider;
import com.dbj.framework.openapi.request.ApiRequest;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * 动态API客户端工厂
 * 根据服务名称动态创建API客户端
 */
@Component
public class DynamicApiClientFactory {
    
    private final CredentialManager credentialManager;
    private final String baseUrl;
    private final Map<String, ApiRequest> apiRequestCache = new ConcurrentHashMap<>();
    private final DbjClientFactory dbjClientFactory;
    private final Map<String, String> serviceUrls = new ConcurrentHashMap<>();
    
    public DynamicApiClientFactory(
            CredentialManager credentialManager,
            @Value("${dbj.api-client.base-url}") String baseUrl,
            DbjClientFactory dbjClientFactory) {
        this.credentialManager = credentialManager;
        this.baseUrl = baseUrl;
        this.dbjClientFactory = dbjClientFactory;
        serviceUrls.put("default", baseUrl);
    }
    

    /**
     * 注册服务URL
     *
     * @param serviceName 服务名称
     * @param serviceUrl  服务URL
     */
    public void registerServiceUrl(String serviceName, String serviceUrl) {
        serviceUrls.put(serviceName, serviceUrl);
        apiRequestCache.remove(serviceName);
    }

    /**
     * 获取指定服务的API请求实例
     * @param serviceName 服务名称
     * @return API请求CompletableFuture
     */
    public CompletableFuture<ApiRequest> createAuthClient(String serviceName) {
        ApiRequest cachedRequest = apiRequestCache.get(serviceName);
        if (cachedRequest != null) {
            return CompletableFuture.completedFuture(cachedRequest);
        }
        return credentialManager.getCredential()
                .thenApply(credential -> {
                    String serviceUrl = serviceUrls.getOrDefault(serviceName, baseUrl);
                    AuthProvider authProvider = new DbjAuthProvider(
                            credential.getAppId(), 
                            credential.getAppSecret());
                    ApiRequest apiRequest = dbjClientFactory.createWithAuth(
                            serviceUrl, 
                            authProvider);
                    apiRequestCache.put(serviceName, apiRequest);
                    
                    return apiRequest;
                });
    }
    /**
     * 清除API请求缓存
     * 当凭证刷新时调用
     */
    public void clearCache() {
        apiRequestCache.clear();
    }
}