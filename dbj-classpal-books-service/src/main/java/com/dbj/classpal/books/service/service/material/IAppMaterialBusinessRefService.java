package com.dbj.classpal.books.service.service.material;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.dto.material.AppCommonMediaDTO;
import com.dbj.classpal.books.common.bo.common.CommonIdBO;
import com.dbj.classpal.books.common.bo.common.CommonIdsBO;
import com.dbj.classpal.books.common.bo.material.AppMaterialBusinessRefQueryBO;
import com.dbj.classpal.books.common.bo.material.AppMaterialBusinessRefQueryCommonBO;
import com.dbj.classpal.books.common.bo.material.AppMaterialBusinessRefReNameBO;
import com.dbj.classpal.books.common.bo.material.AppMaterialBusinessRefSaveBO;
import com.dbj.classpal.books.common.dto.books.BooksRefDirectDTO;
import com.dbj.classpal.books.common.dto.material.*;
import com.dbj.classpal.books.client.enums.BusinessTypeEnum;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppMaterialBusinessRefService
 * Date:     2025-04-14 09:39:53
 * Description: 表名： ,描述： 表
 */
public interface IAppMaterialBusinessRefService {

    /**
     * 查询被引用素材列表
     * @param bo
     * @return
     */
    List<AppMaterialBusinessRefDTO> beRefBusinessList(AppMaterialBusinessRefQueryCommonBO bo);

    /**
     * 查询关联引用列表
     * @param bo
     * @return
     */
    List<AppMaterialBusinessRefDTO> refBusinessList(AppMaterialBusinessRefQueryCommonBO bo);

    /**
     * 查询关联引用下资源各文件类型数量统计
     * @param bo
     * @return
     */
    List<AppMaterialBusinessRefTypeCountDTO>  getMaterialBusinessRefTypeCount(CommonIdBO bo);

    /**
     * 保存资源关联关系
     * @param bo
     * @return
     */
    Boolean saveAppMaterialBusinessRef(AppMaterialBusinessRefSaveBO bo) throws BusinessException;


    /**
     * 资源关联关系重命名
     * @param bo
     * @return
     * @throws BusinessException
     */
    Boolean reNameAppMaterialBusinessRef(AppMaterialBusinessRefReNameBO bo) throws BusinessException;


    /**
     * 移除资源关联关系
     * @param bo
     * @return
     * @throws BusinessException
     */
    Boolean removeAppMaterialBusinessRef(CommonIdBO bo) throws BusinessException;


    /**
     * 批量移除资源关联关系
     * @param bo
     * @return
     * @throws BusinessException
     */
    Boolean removeBatchAppMaterialBusinessRef(CommonIdsBO bo) throws BusinessException;

    /**
     * 交换资源关联关系排序
     * @param bo
     * @return
     * @throws BusinessException
     */
    Boolean changeAppMaterialBusinessRefOrderNum(CommonIdsBO bo) throws BusinessException;

    /**
     * 分页素材中心查询关联
     * @param bo
     * @return
     */
    Page<AppMaterialBusinessRefDTO>pageInfo(PageInfo<AppMaterialBusinessRefQueryBO>bo);


    /**
     * 素材关联内容管理-专辑引用跳转页面所需参数
     */
    AppMaterialBusinessRefAlbumDirectDTO getAppMaterialBusinessRefAlbum(CommonIdBO bo) throws BusinessException;


    /**
     * 素材关联图书管理-图书资源引用跳转页面所需参数
     */
    BooksRefDirectDTO getAppMaterialBusinessRefBooks(CommonIdBO bo) throws BusinessException;

    /**
     * 素材关联图书管理-题库引用跳转页面所需参数
     */
    AppMaterialBusinessRefQuestionDirectDTO getAppMaterialBusinessRefQuestion(CommonIdBO bo) throws BusinessException;


    /**
     * 素材关联通用资源管理-通用资源引用跳转页面所需参数
     */
    List<AppCommonMediaDTO> getCommonBusinessList(Collection<Integer> businessIds, BusinessTypeEnum typeEnum);
}
