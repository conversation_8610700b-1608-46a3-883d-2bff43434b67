package com.dbj.classpal.books.service.service.tree;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.tree.TreeClassifyAddBO;
import com.dbj.classpal.books.client.bo.tree.TreeClassifyEditBO;
import com.dbj.classpal.books.client.bo.tree.TreeClassifyListBO;
import com.dbj.classpal.books.client.bo.tree.TreeClassifyMoveBO;
import com.dbj.classpal.books.client.dto.tree.TreeClassifyDTO;
import com.dbj.classpal.books.service.entity.tree.TreeClassify;
import com.dbj.classpal.framework.commons.exception.BusinessException;

import java.util.List;

/**
 * <p>
 * 树形分类菜单 业务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
public interface ITreeClassifyService extends IService<TreeClassify> {

    /**
     * 获取树形分类菜单
     * @param bo
     * @return
     */
    List<TreeClassifyDTO> getTree(TreeClassifyListBO bo);
    /**
     * 新增树形分类菜单
     * @param bo
     * @return
     */
    Boolean addNode(TreeClassifyAddBO bo) throws BusinessException;
    /**
     * 重命名树形分类菜单
     * @param bo
     * @return
     */
    Boolean renameNode(TreeClassifyEditBO bo) throws BusinessException;
    /**
     * 删除树形分类菜单
     * @param bo
     * @return
     */
    Boolean deleteNode(CommonIdApiBO bo) throws BusinessException;
    /**
     * 移动树形分类菜单
     * @param bo
     * @return
     */
    Boolean moveNode(TreeClassifyMoveBO bo) throws BusinessException;
}
