package com.dbj.classpal.books.service.strategy.business.common.impl;

import com.dbj.classpal.books.service.biz.poem.IAncientPoemBiz;
import com.dbj.classpal.books.service.entity.poem.AncientPoem;
import com.dbj.classpal.books.service.strategy.business.handler.ICommonBusinessStrategyHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
     * 古诗文 媒体引用策略实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Component
public class AncientPoemMaterialStrategy extends ICommonBusinessStrategyHandler {

    @Resource
    private IAncientPoemBiz ancientPoemBiz;

    @Override
    public Map<Integer, String> getBusinessName(List<Integer> businessId) {
        List<AncientPoem> ancientPoems = ancientPoemBiz.listByIds(businessId);
        return ancientPoems.stream()
                .filter(pinyin -> pinyin.getTitle() != null)
                .collect(Collectors.toMap(AncientPoem::getId, AncientPoem::getTitle));
    }
}
