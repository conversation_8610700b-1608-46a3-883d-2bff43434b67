package com.dbj.classpal.books.service.service.studycenter.impl;

import com.dbj.classpal.books.common.bo.studycenter.StudyCenterModuleListQueryBO;
import com.dbj.classpal.books.common.bo.studycenter.StudyCenterModuleStudyBO;
import com.dbj.classpal.books.common.constant.AppErrorCode;
import com.dbj.classpal.books.common.dto.studycenter.AppStudyModuleProgressListDTO;
import com.dbj.classpal.books.service.biz.studycenter.IAppStudyModuleProgressBiz;
import com.dbj.classpal.books.service.service.studycenter.IAppStudyModuleProgressService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class AppStudyModuleProgressServiceImpl implements IAppStudyModuleProgressService {
    @Resource
    private IAppStudyModuleProgressBiz appStudyModuleProgressBiz;


    @Override
    public Boolean delete(List<Integer> ids) throws BusinessException {
        log.info("删除进度 入参: {}", ids);
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException(AppErrorCode.ID_LIST_NOT_EMPTY_CODE, AppErrorCode.ID_LIST_NOT_EMPTY_MSG);
        }
        Boolean result = appStudyModuleProgressBiz.delete(ids);
        log.info("删除进度 返回: {}", result);
        return result;
    }

    @Override
    public Boolean recordStudy(StudyCenterModuleStudyBO bo) throws BusinessException {
        return appStudyModuleProgressBiz.recordStudy(bo);
    }

    @Override
    public List<AppStudyModuleProgressListDTO> listRecentStudy(StudyCenterModuleListQueryBO queryBO) throws BusinessException {
        return appStudyModuleProgressBiz.listRecentStudy(queryBO);
    }

    @Override
    public Boolean deleteStudyRecord(StudyCenterModuleStudyBO bo) throws BusinessException {
        return appStudyModuleProgressBiz.deleteStudyRecord(bo);
    }
} 