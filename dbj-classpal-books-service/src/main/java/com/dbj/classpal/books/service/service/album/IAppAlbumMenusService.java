package com.dbj.classpal.books.service.service.album;

import com.dbj.classpal.books.common.bo.album.*;
import com.dbj.classpal.books.common.dto.album.AppAlbumMenusTreeDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppAlbumMenusService
 * Date:     2025-04-08 16:21:26
 * Description: 表名： ,描述： 表
 */
@Mapper
public interface IAppAlbumMenusService {

    /**
     * 获取所有分类
     * @return
     */
    AppAlbumMenusTreeDTO getAllAlbumMenusTree(AppAlbumMenusQueryBO bo);


    /**
     * 重命名分类
     * @return
     */
    Boolean reNameAlbumMenus(AppAlbumMenusReNameBO bo);


    /**
     * 新增分类
     * @return
     */
    Boolean saveAlbumMenus(AppAlbumMenusSaveBO bo);


    /**
     * 删除分类
     * @return
     */
    Boolean deleteAlbumMenus(AppAlbumMenusDeleteBO bo) throws BusinessException;


    /**
     * 分类重新排序
     * @return
     */
    Boolean resetAlbumMenusOrderNum(AppAlbumMenusBatchMoveBO bo) throws BusinessException;


    /**
     * 获取某个节点下子孙节点深度层数
     * @param id
     * @return
     */
    Integer getChildrenDepth(Integer id);


    /**
     * 获取某个节点到根节点深度层数
     * @param id
     * @return
     */
    Integer getRootDepth(Integer id);


    /**
     * 获取最大排序号
     * @param id
     * @return
     */
    Integer getMaxOrderNum(Integer id);
}
