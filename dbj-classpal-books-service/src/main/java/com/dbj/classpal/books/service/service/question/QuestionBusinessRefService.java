package com.dbj.classpal.books.service.service.question;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.common.bo.question.QuestionBusinessRefPageBO;
import com.dbj.classpal.books.common.dto.question.QuestionBusinessRefDTO;
import com.dbj.classpal.framework.commons.request.PageInfo;

public interface QuestionBusinessRefService {
    /**
     * 分页查询题目列表
     */
    Page<QuestionBusinessRefDTO> pageList(PageInfo<QuestionBusinessRefPageBO> pageBO);


    /**
     * 查询最大排序号
     * @param questionBusinessRefPageBO
     * @return
     */
    Integer getMaxSortNum(QuestionBusinessRefPageBO questionBusinessRefPageBO);
} 