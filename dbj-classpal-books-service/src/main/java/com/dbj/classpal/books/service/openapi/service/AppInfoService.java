package com.dbj.classpal.books.service.openapi.service;

import com.dbj.business.external.client.model.request.AppInfoRequest;
import com.dbj.business.external.client.model.response.AppInfoResponse;
import com.dbj.classpal.framework.commons.exception.BusinessException;

import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * description: description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/04/27 14:36:57
 */
public interface AppInfoService {
    void registerApp(String clientId,String appName, String appDesc);

    AppInfoResponse createApp(AppInfoRequest appInfoRequest) throws ExecutionException, InterruptedException, BusinessException;

    AppInfoResponse getAppInfo(String appId) throws ExecutionException, InterruptedException, BusinessException;

    List<AppInfoResponse> getAppsByClientId(String clientId) throws ExecutionException, InterruptedException, BusinessException;

    List<AppInfoResponse> listApps(int page, int pageSize) throws ExecutionException, InterruptedException, BusinessException;

    Boolean updateApp(AppInfoRequest appInfoRequest) throws ExecutionException, InterruptedException, BusinessException;

    String refreshAppSecret(String appId) throws ExecutionException, InterruptedException, BusinessException;
}
