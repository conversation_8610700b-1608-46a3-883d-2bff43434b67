package com.dbj.classpal.books.service.service.config.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.enums.BasicConfigBizTypeEnum;
import com.dbj.classpal.books.common.bo.config.BasicConfigBO;
import com.dbj.classpal.books.common.bo.config.BasicConfigIdBO;
import com.dbj.classpal.books.common.bo.config.BasicConfigIdsBO;
import com.dbj.classpal.books.common.bo.config.BasicConfigQueryBO;
import com.dbj.classpal.books.common.dto.config.BasicConfigDTO;
import com.dbj.classpal.books.service.biz.config.BasicConfigBiz;
import com.dbj.classpal.books.service.config.ConfigCacheManager;
import com.dbj.classpal.books.service.entity.config.BasicConfig;
import com.dbj.classpal.books.service.factory.BasicConfigBusinessStrategyFactory;
import com.dbj.classpal.books.service.service.config.BasicConfigService;
import com.dbj.classpal.books.service.strategy.basicConfig.common.IBasicConfigCommonBusinessStrategy;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import jakarta.annotation.Resource;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class BasicConfigServiceImpl implements BasicConfigService {

    @Resource
    private BasicConfigBusinessStrategyFactory basicConfigBusinessStrategyFactory;

    private final BasicConfigBiz configBiz;

    @Resource
    private ConfigCacheManager configCacheManager;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer create(BasicConfigBO bo) throws BusinessException {
        Integer result = configBiz.create(bo);
        // 清除统一配置缓存
        configCacheManager.clearCache();
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(BasicConfigBO bo) throws BusinessException {
        configBiz.update(bo);
        // 清除统一配置缓存
        configCacheManager.clearCache();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(BasicConfigIdsBO idsBo) throws BusinessException {
        if(CollectionUtils.isNotEmpty(idsBo.getIds())) {
            List<BasicConfig> basicConfigs = configBiz.listByIds(idsBo.getIds());
            Map<String,List<Integer>>typeIdsMap = new HashMap<>();
            for (BasicConfig record : basicConfigs) {
                for (BasicConfigBizTypeEnum configBizTypeEnum : BasicConfigBizTypeEnum.values()){
                    if (record.getBizType().equals(configBizTypeEnum.getCode())) {
                        if (typeIdsMap.containsKey(configBizTypeEnum.getCode())) {
                            typeIdsMap.get(configBizTypeEnum.getCode()).add(record.getId());
                        }else{
                            List<Integer> ids = new ArrayList<>();
                            ids.add(record.getId());
                            typeIdsMap.put(configBizTypeEnum.getCode(),ids);
                        }
                    }
                }
            }
            Map<String,Map<Integer,Long>> typeRefMap = new HashMap<>();
            for (String code : typeIdsMap.keySet()) {
                BasicConfigBizTypeEnum typeEnum = BasicConfigBizTypeEnum.fromCode(code);
                if (typeEnum == null) {
                    continue;
                }
                IBasicConfigCommonBusinessStrategy strategy = basicConfigBusinessStrategyFactory.getStrategy(typeEnum.getStrategy());
                if (strategy == null) {
                    continue;
                }
                List<Integer> ids = typeIdsMap.get(code);
                Map<Integer, Long> refMap = strategy.getRefMap(ids);
                if (typeRefMap.containsKey(typeEnum.getCode())) {
                    typeRefMap.get(typeEnum.getCode()).putAll(refMap);
                }else{
                    typeRefMap.put(typeEnum.getCode(),refMap);
                }
            }

            for (BasicConfig record : basicConfigs) {
                if (!typeRefMap.containsKey(record.getBizType())){
                    continue;
                }
                Map<Integer, Long> refMap = typeRefMap.get(record.getBizType());
                if (!refMap.containsKey(record.getId())) {
                    continue;
                }
                if (refMap.get(record.getId())<=0){
                    continue;
                }
                String bizType = record.getBizType();
                BasicConfigBizTypeEnum typeEnum = BasicConfigBizTypeEnum.fromCode(bizType);
                if (typeEnum == null) {
                    continue;
                }
                IBasicConfigCommonBusinessStrategy strategy = basicConfigBusinessStrategyFactory.getStrategy(typeEnum.getStrategy());
                strategy.throwException(bizType);
                break;
            }
            configBiz.batchDelete(idsBo.getIds());
            // 清除统一配置缓存
            configCacheManager.clearCache();
        }
    }

    @Override
    public BasicConfigDTO detail(BasicConfigIdBO idBO) {
        return configBiz.detail(idBO.getId());
    }

    @Override
    public Page<BasicConfigDTO> pageList(PageInfo<BasicConfigQueryBO> queryBO) {
        BasicConfig condition = new BasicConfig();
        BeanUtil.copyProperties(queryBO.getData(), condition);
        Page<BasicConfig> page = configBiz.pageList(
                new Page<>(queryBO.getPageNum(), queryBO.getPageSize()),
                condition
        );
        Map<String,List<Integer>>typeIdsMap = new HashMap<>();
        for (BasicConfig record : page.getRecords()) {
            for (BasicConfigBizTypeEnum configBizTypeEnum : BasicConfigBizTypeEnum.values()){
                if (record.getBizType().equals(configBizTypeEnum.getCode())) {
                    if (typeIdsMap.containsKey(configBizTypeEnum.getCode())) {
                        typeIdsMap.get(configBizTypeEnum.getCode()).add(record.getId());
                    }else{
                        List<Integer> ids = new ArrayList<>();
                        ids.add(record.getId());
                        typeIdsMap.put(configBizTypeEnum.getCode(),ids);
                    }
                }
            }
        }
        Map<String,Map<Integer,Long>> typeRefMap = new HashMap<>();
        for (String code : typeIdsMap.keySet()) {
            BasicConfigBizTypeEnum typeEnum = BasicConfigBizTypeEnum.fromCode(code);
            if (typeEnum == null) {
                continue;
            }
            IBasicConfigCommonBusinessStrategy strategy = basicConfigBusinessStrategyFactory.getStrategy(typeEnum.getStrategy());
            if (strategy == null) {
                continue;
            }
            List<Integer> ids = typeIdsMap.get(code);
            Map<Integer, Long> refMap = strategy.getRefMap(ids);
            if (typeRefMap.containsKey(typeEnum.getCode())) {
                typeRefMap.get(typeEnum.getCode()).putAll(refMap);
            }else{
                typeRefMap.put(typeEnum.getCode(),refMap);
            }
        }

        for (BasicConfig record : page.getRecords()) {
            record.setBizCount(0);
            if (typeRefMap.containsKey(record.getBizType())) {
                Map<Integer, Long> refMap = typeRefMap.get(record.getBizType());
                record.setBizCount(refMap.getOrDefault(record.getId(),0L).intValue());
            }
        }

        // 增量更新缓存
        configCacheManager.updateBasicConfigsToCache(page.getRecords());

        return (Page<BasicConfigDTO>) page.convert(entity -> {
            BasicConfigDTO vo = new BasicConfigDTO();
            BeanUtil.copyProperties(entity, vo);
            return vo;
        });

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSort(List<BasicConfigBO> boList) {
        if (CollectionUtils.isEmpty(boList)) {
            return;
        }
        configBiz.updateSort(boList);
    }

    @Override
    public List<BasicConfigDTO> list(BasicConfigQueryBO serviceBo) {
        if (Objects.isNull(serviceBo)) {
            return List.of();
        }
        LambdaQueryWrapper<BasicConfig> wrapper = new LambdaQueryWrapper<BasicConfig>()
                .like(StringUtil.isNotBlank(serviceBo.getName()), BasicConfig::getName, serviceBo.getName())
                .eq(StringUtil.isNotBlank(serviceBo.getBizType()), BasicConfig::getBizType, serviceBo.getBizType())
                .orderByDesc(BasicConfig::getCreateTime);
        List<BasicConfig> basicConfigs = configBiz.getBaseMapper().selectList(wrapper);
        // 增量更新缓存
        configCacheManager.updateBasicConfigsToCache(basicConfigs);
        return BeanUtil.copyToList(basicConfigs, BasicConfigDTO.class);
    }
}