package com.dbj.classpal.books.service.service.pointreading;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingHotspotQueryBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingHotspotSaveBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingHotspotUpdateBO;
import com.dbj.classpal.books.common.dto.pointreading.PointReadingHotspotDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;

import com.dbj.classpal.framework.commons.request.PageInfo;
import java.util.List;

/**
 * 点读书热点区域 服务接口
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface IPointReadingHotspotService {

    /**
     * 分页查询点读书热点区域
     *
     * @param pageInfo 分页参数
     * @return 分页结果
     */
    Page<PointReadingHotspotDTO> pageHotspot(PageInfo<PointReadingHotspotQueryBO> pageInfo) throws BusinessException;

    /**
     * 查询热点详情
     *
     * @param id 热点ID
     * @return 热点详情
     */
    PointReadingHotspotDTO detail(Integer id) throws BusinessException;

    /**
     * 保存点读书热点区域
     *
     * @param saveBO 保存参数
     * @return 热点ID
     */
    Integer save(PointReadingHotspotSaveBO saveBO) throws BusinessException;

    /**
     * 批量保存章节热点区域
     *
     * @param chapterId 章节ID
     * @param hotspots 热点列表
     * @return 是否成功
     */
    Boolean saveChapterHotspots(Integer chapterId, List<PointReadingHotspotSaveBO> hotspots) throws BusinessException;

    /**
     * 批量更新章节热点区域（先删除后新增）
     *
     * @param chapterId 章节ID
     * @param hotspots 热点列表
     * @return 是否成功
     */
    Boolean updateChapterHotspots(Integer chapterId, List<PointReadingHotspotSaveBO> hotspots) throws BusinessException;

    /**
     * 更新点读书热点区域
     *
     * @param updateBO 更新参数
     * @return 是否成功
     */
    Boolean update(PointReadingHotspotUpdateBO updateBO) throws BusinessException;

    /**
     * 删除点读书热点区域
     *
     * @param id 热点ID
     * @return 是否成功
     */
    Boolean delete(Integer id) throws BusinessException;

    /**
     * 批量删除点读书热点区域
     *
     * @param ids 热点ID列表
     * @return 是否成功
     */
    Boolean deleteBatch(List<Integer> ids) throws BusinessException;

    /**
     * 查询章节下的热点列表
     *
     * @param chapterId 章节ID
     * @return 热点列表
     */
    List<PointReadingHotspotDTO> getHotspotsByChapter(Integer chapterId) throws BusinessException;

    /**
     * 查询章节下的热点树形结构
     *
     * @param chapterId 章节ID
     * @return 热点树形结构
     */
    List<PointReadingHotspotDTO> getHotspotsTreeByChapter(Integer chapterId) throws BusinessException;

}
