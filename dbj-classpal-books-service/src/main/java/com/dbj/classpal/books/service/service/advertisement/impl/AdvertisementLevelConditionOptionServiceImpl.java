package com.dbj.classpal.books.service.service.advertisement.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.service.service.advertisement.IAdvertisementLevelConditionOptionService;
import com.dbj.classpal.books.service.entity.advertisement.AdvertisementLevelConditionOption;
import com.dbj.classpal.books.service.mapper.advertisement.AdvertisementLevelConditionOptionMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 广告层级条件选项关联表 业务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Service
public class AdvertisementLevelConditionOptionServiceImpl extends ServiceImpl<AdvertisementLevelConditionOptionMapper, AdvertisementLevelConditionOption> implements IAdvertisementLevelConditionOptionService {

}
