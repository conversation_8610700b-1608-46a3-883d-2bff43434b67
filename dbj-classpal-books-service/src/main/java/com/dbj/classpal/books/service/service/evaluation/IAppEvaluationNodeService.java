package com.dbj.classpal.books.service.service.evaluation;

import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.evaluation.AdminEvaluationNodeEditApiBO;
import com.dbj.classpal.books.client.bo.evaluation.AdminEvaluationNodeSaveApiBO;
import com.dbj.classpal.books.client.bo.evaluation.AdminEvaluationNodeSortApiBO;
import com.dbj.classpal.books.service.entity.evaluation.AppEvaluationNode;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppEvaluationNodeService
 * Date:     2025-05-16 14:59:38
 * Description: 表名： ,描述： 表
 */
@Mapper
public interface IAppEvaluationNodeService {
    Boolean saveEvaluationNode(AdminEvaluationNodeSaveApiBO bo) throws BusinessException;
    Boolean editEvaluationNode(AdminEvaluationNodeEditApiBO bo) throws BusinessException;
    Boolean reSort(AdminEvaluationNodeSortApiBO bo) throws BusinessException;
    Boolean delete(CommonIdsApiBO bo) throws BusinessException;
}
