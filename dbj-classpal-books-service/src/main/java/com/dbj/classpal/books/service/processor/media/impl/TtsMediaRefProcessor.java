package com.dbj.classpal.books.service.processor.media.impl;

import com.dbj.classpal.books.common.bo.pointreading.PointReadingHotspotMediaBO;
import com.dbj.classpal.books.common.dto.pointreading.PointReadingHotspotMediaDTO;
import com.dbj.classpal.books.common.enums.MediaSourceEnum;
import com.dbj.classpal.books.service.biz.audio.IAudioIntroBusinessRefBiz;
import com.dbj.classpal.books.service.processor.media.MediaRefProcessor;
import jakarta.annotation.Resource;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * TTS合成媒体引用处理器
 * MediaSourceEnum = TTS_SYNTHESIS
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@Component
public class TtsMediaRefProcessor implements MediaRefProcessor {

    @Resource
    private IAudioIntroBusinessRefBiz audioIntroBusinessRefBiz;

    @Override
    public MediaSourceEnum getSupportedMediaSource() {
        return MediaSourceEnum.TTS_SYNTHESIS;
    }

    @Override
    public void saveMediaRefs(Integer hotspotId, List<PointReadingHotspotMediaBO> mediaList) throws Exception {
        log.info("保存TTS媒体引用，hotspotId：{}，数量：{}", hotspotId, mediaList.size());
        
        String businessType = "POINT_READING_HOTSPOT";
        audioIntroBusinessRefBiz.saveBusinessAudioRefs(hotspotId, businessType, mediaList);
        
        log.debug("TTS媒体引用保存完成，hotspotId：{}", hotspotId);
    }

    @Override
    public List<PointReadingHotspotMediaDTO> getMediaRefs(Integer hotspotId) throws Exception {
        log.debug("查询TTS媒体引用，hotspotId：{}", hotspotId);
        
        String businessType = "POINT_READING_HOTSPOT";
        List<PointReadingHotspotMediaDTO> mediaList = audioIntroBusinessRefBiz.getBusinessAudioRefs(hotspotId, businessType);
        
        log.debug("TTS媒体引用查询完成，hotspotId：{}，数量：{}", hotspotId, mediaList.size());
        return mediaList;
    }

    @Override
    public void deleteMediaRefs(Integer hotspotId) throws Exception {
        log.info("删除TTS媒体引用，hotspotId：{}", hotspotId);
        
        String businessType = "POINT_READING_HOTSPOT";
        audioIntroBusinessRefBiz.deleteBusinessAudioRefs(hotspotId, businessType);
        
        log.debug("TTS媒体引用删除完成，hotspotId：{}", hotspotId);
    }

    @Override
    public void batchDeleteMediaRefs(List<Integer> hotspotIds) throws Exception {
        log.info("批量删除TTS媒体引用，hotspotIds：{}", hotspotIds);
        
        String businessType = "POINT_READING_HOTSPOT";
        audioIntroBusinessRefBiz.batchDeleteBusinessAudioRefs(hotspotIds, businessType);
        
        log.debug("TTS媒体引用批量删除完成，hotspotIds：{}", hotspotIds);
    }

    @Override
    public int getPriority() {
        return 10;
    }
}
