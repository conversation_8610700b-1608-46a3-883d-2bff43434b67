package com.dbj.classpal.books.service.strategy.basicConfig.common.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dbj.classpal.books.service.biz.studycenter.IAppStudyModuleBiz;
import com.dbj.classpal.books.service.entity.studycenter.AppStudyModule;
import com.dbj.classpal.books.service.strategy.basicConfig.handler.IBasicConfigCommonBusinessStrategyHandler;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.APP_CATEGORY_HAS_MODULE_FAIL_CODE;
import static com.dbj.classpal.books.common.constant.AppErrorCode.APP_CATEGORY_HAS_MODULE_FAIL_MSG;

@Service("basicConfigStudyCenterRefBizStrategy")
public class BasicConfigStudyCenterRefBizStrategy extends IBasicConfigCommonBusinessStrategyHandler {

    @Resource
    private IAppStudyModuleBiz studyModuleBiz;

    @Override
    public Map<Integer, Long> getRefMap(List<Integer> ids) {
        Map<Integer, Long> refMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(ids)) {
            List<AppStudyModule> modules = studyModuleBiz.getBaseMapper().selectList(new LambdaQueryWrapper<AppStudyModule>().in(AppStudyModule::getBelongCategoryId, ids));
            Map<Integer, Long> moduleIdToCountMap = modules.stream()
                    .collect(Collectors.groupingBy(
                            AppStudyModule::getBelongCategoryId,
                            Collectors.counting()
                    ));
            refMap = ids.stream()
                    .collect(Collectors.toMap(
                            id -> id,
                            id -> moduleIdToCountMap.getOrDefault(id, 0L)
                    ));
        }
        return refMap;
    }

    @Override
    public void throwException(String type) throws BusinessException {
        throw new BusinessException(APP_CATEGORY_HAS_MODULE_FAIL_CODE,APP_CATEGORY_HAS_MODULE_FAIL_MSG);
    }
}
