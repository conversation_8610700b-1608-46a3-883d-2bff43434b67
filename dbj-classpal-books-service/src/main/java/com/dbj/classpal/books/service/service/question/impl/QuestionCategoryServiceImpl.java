package com.dbj.classpal.books.service.service.question.impl;

import cn.hutool.core.bean.BeanUtil;
import com.dbj.classpal.books.client.enums.BusinessTypeEnum;
import com.dbj.classpal.books.common.bo.question.QuestionCategoryBO;
import com.dbj.classpal.books.common.bo.question.QuestionCategoryIdBO;
import com.dbj.classpal.books.common.bo.question.QuestionCategoryIdQueryBO;
import com.dbj.classpal.books.common.bo.question.QuestionCategoryIdsBO;
import com.dbj.classpal.books.common.bo.question.QuestionCategoryQueryBO;
import com.dbj.classpal.books.common.bo.question.QuestionCategorySortBO;
import com.dbj.classpal.books.common.dto.question.QuestionCategoryDTO;
import com.dbj.classpal.books.common.dto.question.QuestionCategoryRefDTO;
import com.dbj.classpal.books.service.biz.question.IQuestionCategoryBusinessRefBiz;
import com.dbj.classpal.books.service.biz.question.QuestionBiz;
import com.dbj.classpal.books.service.biz.question.QuestionCategoryBiz;
import com.dbj.classpal.books.service.entity.question.QuestionCategoryBusinessRef;
import com.dbj.classpal.books.service.processor.BusinessRefProcessorManager;
import com.dbj.classpal.books.service.service.question.QuestionCategoryService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import static com.dbj.classpal.books.common.constant.AppErrorCode.APP_QUESTION_CATEGORY_HAS_CHILDREN_CODE;
import static com.dbj.classpal.books.common.constant.AppErrorCode.APP_QUESTION_CATEGORY_HAS_CHILDREN_MSG;
import static com.dbj.classpal.books.common.constant.AppErrorCode.APP_QUESTION_CATEGORY_HAS_QUESTIONS_CODE;
import static com.dbj.classpal.books.common.constant.AppErrorCode.APP_QUESTION_CATEGORY_HAS_QUESTIONS_MSG;
import static com.dbj.classpal.books.common.constant.AppErrorCode.APP_QUESTION_CATEGORY_HAS_REF_CODE;
import static com.dbj.classpal.books.common.constant.AppErrorCode.APP_QUESTION_CATEGORY_HAS_REF_MSG;

/**
 * 题目分类服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QuestionCategoryServiceImpl implements QuestionCategoryService {

    private final QuestionCategoryBiz questionCategoryBiz;
    private final QuestionBiz questionBiz;
    private final IQuestionCategoryBusinessRefBiz questionCategoryBusinessRefBiz;
    private final BusinessRefProcessorManager processorManager;

    private static final  Integer CATEGORY_REF_CODE = 1;
    private static final  String CATEGORY_REF_NAME = "答题";


    @Override
    public List<QuestionCategoryDTO> getCategoryTree(QuestionCategoryQueryBO queryBO) {
        return questionCategoryBiz.getCategoryTree(queryBO);
    }

    @Override
    public QuestionCategoryDTO getCategory(QuestionCategoryIdBO idBO) {
        if (Objects.isNull(idBO)) {
            return null;
        }
        List<QuestionCategoryDTO> categories = questionCategoryBiz.getCategoryWithChildren(idBO.getId());
        return CollectionUtils.isEmpty(categories) ? null : categories.get(0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer createCategory(QuestionCategoryBO categoryBO) throws BusinessException {
        return questionCategoryBiz.createCategory(categoryBO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCategory(QuestionCategoryBO categoryBO) throws BusinessException {
        questionCategoryBiz.updateCategory(categoryBO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteCategory(QuestionCategoryIdsBO idsBO) throws BusinessException {
        if (Objects.isNull(idsBO)) {
            return;
        }

        // 检查是否存在子分类
        if (questionCategoryBiz.hasChildren(idsBO.getIds())) {
            throw new BusinessException(APP_QUESTION_CATEGORY_HAS_CHILDREN_CODE,APP_QUESTION_CATEGORY_HAS_CHILDREN_MSG);
        }

        // 检查是否有关联的试题
        if (questionBiz.hasCategoryQuestions(idsBO.getIds())) {
            throw new BusinessException(APP_QUESTION_CATEGORY_HAS_QUESTIONS_CODE,APP_QUESTION_CATEGORY_HAS_QUESTIONS_MSG);
        }

        // 检查分类是否已被配套引用
        if (questionCategoryBusinessRefBiz.hasBusinessRefs(idsBO.getIds())) {
            throw new BusinessException(APP_QUESTION_CATEGORY_HAS_REF_CODE,APP_QUESTION_CATEGORY_HAS_REF_MSG);
        }

        // 删除分类
        questionCategoryBiz.removeByIds(idsBO.getIds());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSort(QuestionCategorySortBO sortBO) throws BusinessException {
        questionCategoryBiz.updateSort(sortBO);
    }

    @Override
    public boolean checkNameExists(String name, Integer fatherId, Integer excludeId) {
        return questionCategoryBiz.checkNameExists(name, fatherId, excludeId);
    }

    @Override
    public List<QuestionCategoryRefDTO> getBusinessRefs(QuestionCategoryIdQueryBO queryBO){
        List<QuestionCategoryBusinessRef> entityList = questionCategoryBusinessRefBiz.getBusinessRefs(List.of(queryBO.getCategoryId()));
        List<QuestionCategoryRefDTO> result = new ArrayList<>();

        for (QuestionCategoryBusinessRef businessRef : entityList) {
            QuestionCategoryRefDTO dto = new QuestionCategoryRefDTO();
            BeanUtil.copyProperties(businessRef, dto);

            BusinessTypeEnum typeEnum = BusinessTypeEnum.getByCode(dto.getBusinessType());
            if (typeEnum == null) {
                log.warn("未知的业务类型: {}", dto.getBusinessType());
                continue;
            }

            dto.setBusinessTypeStr(typeEnum.getName());
            dto.setAppMaterialType(CATEGORY_REF_CODE);
            dto.setAppMaterialTypeStr(CATEGORY_REF_NAME);

            try {
                processorManager.processBusinessInfo(dto, businessRef);
                result.add(dto);
            } catch (Exception e) {
                log.error("处理业务引用信息失败, businessId: {}, businessType: {}",
                    businessRef.getBusinessId(), businessRef.getBusinessType(), e);
            }
        }

        return result;
    }


} 