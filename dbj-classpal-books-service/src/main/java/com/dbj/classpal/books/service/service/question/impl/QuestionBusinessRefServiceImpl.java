package com.dbj.classpal.books.service.service.question.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.common.bo.question.QuestionBusinessRefPageBO;
import com.dbj.classpal.books.common.dto.question.QuestionBusinessRefDTO;
import com.dbj.classpal.books.service.biz.question.QuestionBusinessRefBiz;
import com.dbj.classpal.books.service.service.question.QuestionBusinessRefService;
import com.dbj.classpal.framework.commons.request.PageInfo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class QuestionBusinessRefServiceImpl implements QuestionBusinessRefService {
    @Resource
    private QuestionBusinessRefBiz questionBusinessRefBiz;

    @Override
    public Page<QuestionBusinessRefDTO> pageList(PageInfo<QuestionBusinessRefPageBO> pageBO) {
        return questionBusinessRefBiz.pageList(pageBO.getPage(),pageBO.getData());
    }

    @Override
    public Integer getMaxSortNum(QuestionBusinessRefPageBO questionBusinessRefPageBO) {
        return questionBusinessRefBiz.getMaxSortNum(questionBusinessRefPageBO);
    }
}