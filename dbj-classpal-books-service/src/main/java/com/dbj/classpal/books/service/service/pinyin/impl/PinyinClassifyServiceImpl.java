package com.dbj.classpal.books.service.service.pinyin.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinClassifyBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinClassifyUpsertBO;
import com.dbj.classpal.books.client.dto.pinyin.PinyinClassifyDTO;
import com.dbj.classpal.books.service.biz.pinyin.IPinyinBiz;
import com.dbj.classpal.books.service.biz.pinyin.IPinyinClassifyBiz;
import com.dbj.classpal.books.service.entity.pinyin.Pinyin;
import com.dbj.classpal.books.service.entity.pinyin.PinyinClassify;
import com.dbj.classpal.books.service.mapper.pinyin.PinyinClassifyMapper;
import com.dbj.classpal.books.service.service.pinyin.IPinyinClassifyService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.redisson.config.RedissonRedisUtils;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.dbj.classpal.books.common.constant.AppErrorCode.PINYIN_VERIFICATION_FAIL_CODE;
import static com.dbj.classpal.books.common.constant.RedisKeyConstants.DBJ_CLASSPAL_PINYIN_CLASSIFY_CACHE_KEY;
import static com.dbj.classpal.books.common.constant.RedisKeyConstants.DBJ_CLASSPAL_PINYIN_CLASSIFY_DETAIL_CACHE_KEY;

/**
 * <p>
 * 拼音分类表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Service
public class PinyinClassifyServiceImpl extends ServiceImpl<PinyinClassifyMapper, PinyinClassify> implements IPinyinClassifyService {
    @Resource
    private IPinyinClassifyBiz pinyinClassifyBiz;

    @Resource
    private IPinyinBiz pinyinBiz;

    @Resource
    private RedissonRedisUtils redisUtils;


    @Override
    public List<PinyinClassifyDTO> getPinyinClassifyAll(PinyinClassifyBO bo) {
        List<PinyinClassify> pinyinClassifyList = pinyinClassifyBiz.lambdaQuery()
                .orderByDesc(PinyinClassify::getSort)
                .list();
        return BeanUtil.copyToList(pinyinClassifyList, PinyinClassifyDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean savePinyinClassify(PinyinClassifyUpsertBO bo) throws BusinessException {
        checkPinyinClassifyUpsertParams(bo, false);
        PinyinClassify pinyinClassify = buildPinyinClassify(bo);
        boolean save = pinyinClassifyBiz.save(pinyinClassify);
        Assert.isTrue(save, () -> new BusinessException(PINYIN_VERIFICATION_FAIL_CODE, "添加失败"));
        redisUtils.delKey(DBJ_CLASSPAL_PINYIN_CLASSIFY_CACHE_KEY);
        return Boolean.TRUE;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updatePinyinClassify(PinyinClassifyUpsertBO bo) throws BusinessException {
        checkPinyinClassifyUpsertParams(bo, true);
        PinyinClassify pinyinClassify = buildPinyinClassify(bo);
        boolean update = pinyinClassifyBiz.updateById(pinyinClassify);
        Assert.isTrue(update, () -> new BusinessException(PINYIN_VERIFICATION_FAIL_CODE, "保存失败"));
        redisUtils.delKey(DBJ_CLASSPAL_PINYIN_CLASSIFY_CACHE_KEY);
        return Boolean.TRUE;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deletePinyinClassify(CommonIdApiBO bo) {
        pinyinClassifyBiz.removeById(bo.getId());
        deletedPinyin(bo.getId());
        redisUtils.delKey(DBJ_CLASSPAL_PINYIN_CLASSIFY_CACHE_KEY);
        redisUtils.delKey(MessageFormat.format(DBJ_CLASSPAL_PINYIN_CLASSIFY_DETAIL_CACHE_KEY, bo.getId()));
        return Boolean.TRUE;
    }

    @Override
    public Boolean sortPinyinClassify(CommonIdsApiBO bo) {
        AtomicInteger sort = new AtomicInteger(bo.getIds().size());
        List<PinyinClassify> pinyinClassifyList = bo.getIds().stream()
                .map(id -> new PinyinClassify().setSort(sort.decrementAndGet()).setId(id)).toList();
        redisUtils.delKey(DBJ_CLASSPAL_PINYIN_CLASSIFY_CACHE_KEY);
        return pinyinClassifyBiz.updateBatchById(pinyinClassifyList);
    }

    @Override
    public List<PinyinClassifyDTO> getAppPinyinClassify(PinyinClassifyBO bo) {
        if(redisUtils.hasKey(DBJ_CLASSPAL_PINYIN_CLASSIFY_CACHE_KEY)) {
            return JSON.parseArray(redisUtils.getValue(DBJ_CLASSPAL_PINYIN_CLASSIFY_CACHE_KEY), PinyinClassifyDTO.class);
        } else {
            List<PinyinClassify> pinyinClassifyList = pinyinClassifyBiz.lambdaQuery()
                    .orderByDesc(PinyinClassify::getSort)
                    .list();
            List<PinyinClassifyDTO> pinyinClassifyDTOList = BeanUtil.copyToList(pinyinClassifyList, PinyinClassifyDTO.class);
            redisUtils.setValue(DBJ_CLASSPAL_PINYIN_CLASSIFY_CACHE_KEY, JSON.toJSONString(pinyinClassifyDTOList) ,7, TimeUnit.DAYS);
            return pinyinClassifyDTOList;
        }
    }

    private void checkPinyinClassifyUpsertParams(PinyinClassifyUpsertBO bo, boolean isUpdate) throws BusinessException {
        //标题不能重复
        LambdaQueryWrapper<PinyinClassify> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PinyinClassify::getTitle, bo.getTitle());
        if (isUpdate) {
            Assert.notNull(bo.getId(), () -> new BusinessException(PINYIN_VERIFICATION_FAIL_CODE, "ID不能为空"));
            queryWrapper.ne(PinyinClassify::getId, bo.getId());
        }
        long count = pinyinClassifyBiz.count(queryWrapper);
        Assert.isTrue(count == 0, () -> new BusinessException(PINYIN_VERIFICATION_FAIL_CODE, "拼音分类名称已存在"));
    }

    private PinyinClassify buildPinyinClassify(PinyinClassifyUpsertBO bo)  {
        return BeanUtil.copyProperties(bo, PinyinClassify.class);
    }


    private boolean deletedPinyin(Integer classifyId) {
        return pinyinBiz.remove(new LambdaQueryWrapper<Pinyin>()
                .eq(Pinyin::getClassifyId, classifyId));
    }

}
