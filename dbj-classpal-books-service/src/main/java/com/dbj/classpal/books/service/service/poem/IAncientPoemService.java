package com.dbj.classpal.books.service.service.poem;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemCopyBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemMoveBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemPageBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemUpsertBO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemDTO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemRelateDTO;
import com.dbj.classpal.books.service.entity.poem.AncientPoem;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;

import java.util.List;

/**
 * <p>
 * 古诗文 业务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
public interface IAncientPoemService extends IService<AncientPoem> {
    /**
     * 古诗文分页
     * @param pageInfo
     * @return
     */
    Page<AncientPoemDTO> getAncientPoemPage(PageInfo<AncientPoemPageBO> pageInfo);

    /**
     * 古诗文详情
     * @param bo
     * @return
     */
    AncientPoemDTO getAncientPoemInfo(CommonIdApiBO bo);

    /**
     * 古诗文新增
     * @param bo
     * @return
     */
    Boolean saveAncientPoem(AncientPoemUpsertBO bo) throws BusinessException;

    /**
     * 古诗文修改
     * @param bo
     * @return
     */
    Boolean updateAncientPoem(AncientPoemUpsertBO bo) throws BusinessException;

    /**
     * 古诗文删除
     * @param bo
     * @return
     */
    Boolean deleteAncientPoem(CommonIdsApiBO bo) throws BusinessException;

    /**
     * 古诗文复制
     * @param bo
     * @return
     */
    Boolean copyAncientPoem(AncientPoemCopyBO bo) throws BusinessException;

    /**
     * 古诗文移动
     * @param bo
     * @return
     */
    Boolean moveAncientPoem(AncientPoemMoveBO bo);

    /**
     * 古诗文引用
     * @param bo
     * @return
     */
    List<AncientPoemRelateDTO> getAncientPoemRelate(CommonIdApiBO bo) throws BusinessException;
}
