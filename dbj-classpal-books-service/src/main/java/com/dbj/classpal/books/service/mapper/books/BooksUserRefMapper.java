package com.dbj.classpal.books.service.mapper.books;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.books.app.BooksUserShelfBO;
import com.dbj.classpal.books.client.dto.books.BooksUserCountDTO;
import com.dbj.classpal.books.client.dto.books.app.BooksUserShelfDTO;
import com.dbj.classpal.books.service.entity.books.BooksUserRef;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 产品分类配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
public interface BooksUserRefMapper extends BaseMapper<BooksUserRef> {


    /**
     * <AUTHOR>
     * @Description  分页查询书架上的图书信息
     * @Date 2025/4/21 16:57
     * @param
     * @return
     **/
    List<BooksUserShelfDTO> booksUserShellist(@Param("bo") BooksUserShelfBO booksUserShelfBO, @Param("appUserId")Integer appUserId);


    /**
     * @param
     * @return
     * <AUTHOR>
     * @Description 统计书本被添加次数
     * @Date 2025/4/21 16:57
     **/
    List<BooksUserCountDTO> booksUserCount(@Param("bookIds") List<Integer> bookIds);
}
