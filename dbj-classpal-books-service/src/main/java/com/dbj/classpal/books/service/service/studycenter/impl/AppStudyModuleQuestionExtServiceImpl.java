package com.dbj.classpal.books.service.service.studycenter.impl;

import cn.hutool.core.bean.BeanUtil;
import com.dbj.classpal.books.common.bo.studycenter.AppStudyModuleQuestionExtCreateBO;
import com.dbj.classpal.books.common.dto.studycenter.AppStudyModuleQuestionExtDetailDTO;
import com.dbj.classpal.books.common.dto.studycenter.AppStudyModuleQuestionExtUpdateBO;
import com.dbj.classpal.books.service.biz.studycenter.IAppStudyModuleQuestionExtBiz;
import com.dbj.classpal.books.service.entity.studycenter.AppStudyModuleQuestionExt;
import com.dbj.classpal.books.service.service.studycenter.IAppStudyModuleQuestionExtService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class AppStudyModuleQuestionExtServiceImpl implements IAppStudyModuleQuestionExtService {
    @Resource
    private IAppStudyModuleQuestionExtBiz appStudyModuleQuestionExtBiz;

    @Override
    public boolean create(AppStudyModuleQuestionExtCreateBO bo) {
        return appStudyModuleQuestionExtBiz.save(BeanUtil.copyProperties(bo,AppStudyModuleQuestionExt.class));
    }

    @Override
    public boolean update(AppStudyModuleQuestionExtUpdateBO bo) {
        return appStudyModuleQuestionExtBiz.updateById(BeanUtil.copyProperties(bo,AppStudyModuleQuestionExt.class));
    }

    @Override
    public boolean delete(Integer id) {
        return appStudyModuleQuestionExtBiz.removeById(id);
    }

    @Override
    public AppStudyModuleQuestionExtDetailDTO detail(Integer id) {
        return BeanUtil.copyProperties(appStudyModuleQuestionExtBiz.getById(id),AppStudyModuleQuestionExtDetailDTO.class);
    }

} 