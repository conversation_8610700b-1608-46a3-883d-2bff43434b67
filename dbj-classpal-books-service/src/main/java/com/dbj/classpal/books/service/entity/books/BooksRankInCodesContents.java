package com.dbj.classpal.books.service.entity.books;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 图书书内码分类表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("books_rank_in_codes_contents")
@Tag(name="BooksRankInCodesContents对象", description="图书书内码分类表")
public class BooksRankInCodesContents extends BizEntity implements Serializable {

    @Schema(description = "图书id")
    private Integer booksId;

    @Schema(description = "册书id")
    private Integer rankId;

    @Schema(description = "册数功能分类id")
    private Integer rankClassifyId;

    @Schema(description = "名称")
    private String contentName;

    @Schema(description = "类型 目录-directory 图书资源-resource 答题-question 音频专辑-audio 视频专辑-video 点读书-pointReading")
    private String type;

    @Schema(description = "父级id")
    private Integer fatherId;

    @Schema(description = "排序序号")
    private Integer contentsIndex;

    @Schema(description = "新印码")
    private String newPrintCodeUrl;

    @Schema(description = "H5链接")
    private String h5PageUrl;

    @Schema(description = "强跳链接")
    private String forcePromotionUrl;

    @Schema(description = "是否启用 1-是 0-否")
    private Integer status;

}
