package com.dbj.classpal.books.service.remote.vip;

import com.dbj.classpal.books.client.enums.BusinessTypeEnum;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.promotion.client.api.vip.VipRightsTagBusinessRefApi;
import com.dbj.classpal.promotion.client.bo.vip.VipRightsTagBusinessQueryBO;
import com.dbj.classpal.promotion.client.bo.vip.VipRightsTagBusinessRefListBO;
import com.dbj.classpal.promotion.client.bo.vip.VipRightsTagBusinessRefSaveBO;
import com.dbj.classpal.promotion.client.dto.vip.VipRightsTagBusinessRefListDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/8/1 10:46
 */
@Service
@RequiredArgsConstructor
public class VipRightsTagBusinessRemoteService {

    private final VipRightsTagBusinessRefApi vipRightsTagBusinessRefApi;

    public Boolean saveVipRightsTagBusinessRef(BusinessTypeEnum businessType, Integer businessId, Collection<Integer> vipRightsTagIds) throws BusinessException {
        VipRightsTagBusinessRefSaveBO bo = new VipRightsTagBusinessRefSaveBO();
        bo.setVipRightsTagIds(vipRightsTagIds);
        bo.setBusinessType(businessType.getCode());
        bo.setBusinessId(businessId);
        return vipRightsTagBusinessRefApi.saveVipRightsTagBusinessRef(bo).returnProcess();
    }

    public Boolean bindVipRightsTagBusinessRef(BusinessTypeEnum businessType, Integer businessId, Collection<Integer> vipRightsTagIds) throws BusinessException {
        VipRightsTagBusinessRefSaveBO bo = new VipRightsTagBusinessRefSaveBO();
        bo.setVipRightsTagIds(vipRightsTagIds);
        bo.setBusinessType(businessType.getCode());
        bo.setBusinessId(businessId);
        return vipRightsTagBusinessRefApi.bind(bo).returnProcess();
    }

    public Set<Integer> getBusinessIds(BusinessTypeEnum businessType, Set<Integer> vipRightsTagIds) throws BusinessException {
        VipRightsTagBusinessQueryBO bo = new VipRightsTagBusinessQueryBO();
        bo.setBusinessType(businessType.getCode());
        bo.setVipRightsTagIds(vipRightsTagIds);
        return vipRightsTagBusinessRefApi.getBusinessIds(bo).returnProcess();
    }

    public List<VipRightsTagBusinessRefListDTO> getVipRightsTagList(BusinessTypeEnum businessType, Set<Integer> businessIds) throws BusinessException {
        VipRightsTagBusinessRefListBO bo = new VipRightsTagBusinessRefListBO();
        bo.setBusinessType(businessType.getCode());
        bo.setBusinessIds(businessIds);
        return vipRightsTagBusinessRefApi.getVipRightsTagBusinessRefList(bo).returnProcess();
    }
}
