package com.dbj.classpal.books.service.service.evaluation;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.dto.evaluation.app.AppUserPaperEvaluationAiParamsApiDTO;
import com.dbj.classpal.books.common.bo.evaluation.AdminUserPaperEvaluationQueryBO;
import com.dbj.classpal.books.common.bo.evaluation.app.AppUserPaperEvaluationGenerateBO;
import com.dbj.classpal.books.common.bo.evaluation.app.AppUserPaperEvaluationSaveBO;
import com.dbj.classpal.books.common.dto.evaluation.AdminUserPaperEvaluationQueryPageDTO;
import com.dbj.classpal.books.common.dto.evaluation.app.AppEvaluationReportQueryDTO;
import com.dbj.classpal.books.common.dto.evaluation.app.AppUserPaperEvaluationSaveDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppUserPaperEvaluationService
 * Date:     2025-05-16 14:59:38
 * Description: 表名： ,描述： 表
 */
@Mapper
public interface IAppUserPaperEvaluationService {

    /**
     * 分页查询评测模板列表
     * @param pageRequest
     * @return
     */
    Page<AdminUserPaperEvaluationQueryPageDTO> pageInfo(PageInfo<AdminUserPaperEvaluationQueryBO> pageRequest) throws BusinessException;


    /**
     * 用户新增评测报告,如果存在该评测表未生成的报告记录，则忽略新增
     * @param bo
     * @return
     * @throws BusinessException
     */
    AppUserPaperEvaluationSaveDTO saveEvaluationReport(AppUserPaperEvaluationSaveBO bo) throws BusinessException;


    /**
     * 获取扣子评测需要的参数
     * @param bo
     * @return
     */
    AppUserPaperEvaluationAiParamsApiDTO getAiParams(CommonIdApiBO bo) throws BusinessException;


    /**
     * 检查是否满足生成评测报告的条件
     * @param bo
     * @return
     * @throws BusinessException
     */
    Boolean checkSubmitEvaluationReport(CommonIdApiBO bo) throws BusinessException;


    /**
     * 生成评测报告
     * @param bo
     * @return
     * @throws BusinessException
     */
    AppEvaluationReportQueryDTO genEvaluationReport(AppUserPaperEvaluationGenerateBO bo) throws BusinessException;

}
