package com.dbj.classpal.books.service.service.poem.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.service.entity.poem.AncientPoemBusinessRef;
import com.dbj.classpal.books.service.mapper.poem.AncientPoemBusinessRefMapper;
import com.dbj.classpal.books.service.service.poem.IAncientPoemBusinessRefService;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 古诗文关联业务关系表 业务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Service
public class AncientPoemBusinessRefServiceImpl extends ServiceImpl<AncientPoemBusinessRefMapper, AncientPoemBusinessRef> implements IAncientPoemBusinessRefService {

}
