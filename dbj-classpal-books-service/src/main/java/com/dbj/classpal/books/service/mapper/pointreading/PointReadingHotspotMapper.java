package com.dbj.classpal.books.service.mapper.pointreading;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingHotspotQueryBO;
import com.dbj.classpal.books.service.entity.pointreading.PointReadingHotspot;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 点读书热点区域 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Mapper
public interface PointReadingHotspotMapper extends BaseMapper<PointReadingHotspot> {

    /**
     * 分页查询点读书热点区域
     *
     * @param page 分页参数
     * @param query 查询条件
     * @return 分页结果
     */
    Page<PointReadingHotspot> selectPage(Page<PointReadingHotspot> page, @Param("query") PointReadingHotspotQueryBO query);

    /**
     * 查询章节下的热点列表
     *
     * @param chapterId 章节ID
     * @return 热点列表
     */
    List<PointReadingHotspot> selectByChapterId(@Param("chapterId") Integer chapterId);

    /**
     * 查询章节下的热点数量
     *
     * @param chapterId 章节ID
     * @return 热点数量
     */
    int countByChapter(@Param("chapterId") Integer chapterId);

}
