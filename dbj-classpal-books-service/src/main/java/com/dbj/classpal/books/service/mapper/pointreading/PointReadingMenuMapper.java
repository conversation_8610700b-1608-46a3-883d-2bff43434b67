package com.dbj.classpal.books.service.mapper.pointreading;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingMenuQueryBO;
import com.dbj.classpal.books.service.entity.pointreading.PointReadingMenu;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 点读书目录 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Mapper
public interface PointReadingMenuMapper extends BaseMapper<PointReadingMenu> {

    /**
     * 分页查询点读书目录
     *
     * @param page 分页参数
     * @param query 查询条件
     * @return 分页结果
     */
    Page<PointReadingMenu> selectPage(Page<PointReadingMenu> page, @Param("query") PointReadingMenuQueryBO query);

    /**
     * 查询所有目录（用于构建树形结构）
     *
     * @param query 查询条件
     * @return 目录列表
     */
    List<PointReadingMenu> selectAllForTree(@Param("query") PointReadingMenuQueryBO query);

    /**
     * 查询子目录数量
     *
     * @param parentId 父级目录ID
     * @return 子目录数量
     */
    int countChildren(@Param("parentId") Integer parentId);

    /**
     * 查询点读书下的目录数量
     *
     * @param bookId 点读书ID
     * @return 目录数量
     */
    int countByBook(@Param("bookId") Integer bookId);
}
