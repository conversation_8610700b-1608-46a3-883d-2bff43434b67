package com.dbj.classpal.books.service.openapi.facade;

import com.dbj.classpal.books.service.api.client.printer.service.PrinterRemoteService;
import org.springframework.stereotype.Component;

/**
 * API服务门面
 * 提供对所有业务服务的统一访问入口
 */
@Component
public class ApiServiceFacade {
    
    private final PrinterRemoteService printerRemoteService;

    public ApiServiceFacade(
            PrinterRemoteService printerRemoteService) {
        this.printerRemoteService = printerRemoteService;
    }
    
    /**
     * 获取图书服务
     */
    public PrinterRemoteService printerRemoteService() {
        return printerRemoteService;
    }
}