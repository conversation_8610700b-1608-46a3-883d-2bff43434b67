package com.dbj.classpal.books.service.service.paper;

import com.dbj.classpal.books.common.bo.paper.*;
import com.dbj.classpal.books.common.bo.question.QueryQuestionsCorrectAnswerBO;
import com.dbj.classpal.books.common.dto.paper.*;
import com.dbj.classpal.books.common.dto.question.QuestionCorrectAnswerDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;

import java.util.List;

/**
 * 用户试卷服务接口
 */
public interface IAppUserPaperService {

    /**
     * 获取试卷题目列表
     *
     * @param queryBO 查询参数
     * @return 题目列表
     */
    List<PaperQuestionDTO> getPaperQuestions(QueryPaperQuestionsBO queryBO) throws BusinessException;
    /**
     * 提交试卷
     *
     * @param submitBO 提交试卷参数
     * @return 是否成功
     */
    UserPaperDTO submitPaper(SubmitPaperBO submitBO) throws BusinessException;
    /**
     * 获取题目作答结果列表
     *
     * @param queryBO 查询题目结果参数
     * @return 题目结果列表
     */
    PaperQuestionResultDTO getPaperQuestionResult(QueryPaperResultBO queryBO) throws BusinessException;
    /**
     * 重新考试（删除原有答题记录，重新获取试卷）
     *
     * @param retakePaperBO@throws BusinessException 业务异常
     */
    void retakePaper(RetakePaperBO retakePaperBO) throws BusinessException;

    /**
     * 获取所有题目的正确答案信息
     *
     * @param bo 查询参数
     * @return 正确答案DTO列表
     * @throws BusinessException 业务异常
     */
    List<QuestionCorrectAnswerDTO> getAllQuestionsCorrectAnswers(QueryQuestionsCorrectAnswerBO bo) throws BusinessException;

    /**
     * checkSubmit
     * @param serviceBO
     * @return
     * @throws BusinessException
     */
    UserPaperCheckSubmitDTO checkSubmit(CheckSubmitBO serviceBO) throws BusinessException;
}