package com.dbj.classpal.books.service.remote.sys;

import com.dbj.classpal.admin.client.api.sys.dict.ISysDictApi;
import com.dbj.classpal.admin.client.bo.app.dict.DictItemApiQueryBo;
import com.dbj.classpal.admin.client.dto.sys.dict.SysDictItemApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: SysDictItemRemoteService
 * Date:     2025-05-19 09:44:36
 * Description: 表名： ,描述： 表
 */
@Component
public class SysDictItemRemoteService {
    @Resource
    private ISysDictApi sysDictApi;

    /**
     * 通过code查询字典列表
     * @param queryBo
     * @return
     * @throws BusinessException
     */
//    public List<SysDictItemApiDTO>getDictByCode(DictItemApiQueryBo queryBo) throws BusinessException {
//        RestResponse<List<SysDictItemApiDTO>> result = sysDictApi.getDictByCode(queryBo);
//        return result.returnProcess(result);
//    }

    /**
     * 通过id查询字典数据
     * @param queryBo
     * @return
     * @throws BusinessException
     */
    public SysDictItemApiDTO getDictItemById(DictItemApiQueryBo queryBo) throws BusinessException {
        RestResponse<SysDictItemApiDTO> result = sysDictApi.getDictItem(queryBo);
        return result.returnProcess(result);
    }

    /**
     * @return
     * @throws BusinessException
     */
    public Map<String,List<SysDictItemApiDTO>> findAll() throws BusinessException {
        RestResponse<Map<String,List<SysDictItemApiDTO>>> result = sysDictApi.getSysDictInfoAll();
        return result.returnProcess(result);
    }
}
