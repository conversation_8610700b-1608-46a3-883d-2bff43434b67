package com.dbj.classpal.books.service.service.audio;

import com.dbj.classpal.books.service.entity.audio.AudioEmotionClassifyConfig;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 多情感分类配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Mapper
public interface IAudioEmotionClassifyConfigService {

    /**
     * 批量保存
     * @param saveList
     * @return
     */
    int saveBatch(List<AudioEmotionClassifyConfig> saveList);
}
