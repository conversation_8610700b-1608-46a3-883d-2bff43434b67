package com.dbj.classpal.books.service.factory;

import com.dbj.classpal.books.service.strategy.advertisement.IAdvertisementConditionStrategy;
import com.dbj.classpal.books.service.api.client.advertisement.AdvertisementContext;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-05-23
 */
@Component
@RequiredArgsConstructor
public class AdvertisementConditionStrategyFactory {

    private final List<IAdvertisementConditionStrategy> strategyList;

    public void handle(AdvertisementContext context) {
        for (IAdvertisementConditionStrategy strategy : strategyList) {
            if (strategy.getCode().equals(context.getAdvertisementLevelCondition().getConditionType())) {
                strategy.doHandle(context);
            }
        }
    }
}

