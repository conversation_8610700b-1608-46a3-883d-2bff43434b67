package com.dbj.classpal.books.service.service.pinyin;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinAppBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinDelBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinPageBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinStatusUpdateBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinUpsertBO;
import com.dbj.classpal.books.client.dto.pinyin.PinyinAppDTO;
import com.dbj.classpal.books.client.dto.pinyin.PinyinDTO;
import com.dbj.classpal.books.service.entity.pinyin.Pinyin;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;

/**
 * <p>
 * 拼音信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
public interface IPinyinService extends IService<Pinyin> {

    /**
     * 获取拼音分页列表
     *
     * @param pageInfo
     * @return
     */
    Page<PinyinDTO> getPinyinPage(PageInfo<PinyinPageBO> pageInfo);

    /**
     * 获取拼音信息
     *
     * @param bo
     * @return
     */
    PinyinDTO getPinyinInfo(CommonIdApiBO bo);

    /**
     * 保存拼音信息
     *
     * @param bo
     * @return
     */
    Boolean savePinyin(PinyinUpsertBO bo) throws BusinessException;

    /**
     * 更新拼音信息
     *
     * @param bo
     * @return
     */
    Boolean updatePinyin(PinyinUpsertBO bo) throws BusinessException;

    /**
     * 更新拼音状态
     *
     * @param bo
     * @return
     */
    Boolean updatePinyinStatus(PinyinStatusUpdateBO bo);

    /**
     * 删除拼音信息
     *
     * @param bo
     * @return
     */
    Boolean deletePinyin(PinyinDelBO bo);

    /**
     * 获取拼音分类及拼音信息
     *
     * @param bo
     * @return
     */
    PinyinAppDTO getAppPinyinData(PinyinAppBO bo);
}
