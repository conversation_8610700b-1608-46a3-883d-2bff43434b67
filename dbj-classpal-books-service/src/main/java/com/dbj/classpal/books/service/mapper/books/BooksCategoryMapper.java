package com.dbj.classpal.books.service.mapper.books;

import com.dbj.classpal.books.client.dto.books.BooksCategoryNameTreeApiDTO;
import com.dbj.classpal.books.service.entity.books.BooksCategory;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 产品分类配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
public interface BooksCategoryMapper extends BaseMapper<BooksCategory> {


    List<BooksCategoryNameTreeApiDTO> categoryNameTree(@Param("categoryIds") List<Integer> categoryIds);

    Boolean delete(@Param("categoryIds") List<Integer> categoryIds);
}
