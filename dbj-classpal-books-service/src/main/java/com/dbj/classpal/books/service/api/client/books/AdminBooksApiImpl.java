package com.dbj.classpal.books.service.api.client.books;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.business.external.client.model.request.ProductInfoIdRequest;
import com.dbj.business.external.client.model.request.ProductInfoRequest;
import com.dbj.business.external.client.model.response.ProductInfoDetailResponse;
import com.dbj.business.external.client.model.response.ProductInfoResponse;
import com.dbj.classpal.books.client.api.books.AdminBooksApi;
import com.dbj.classpal.books.client.bo.books.BooksBatchHideApiBO;
import com.dbj.classpal.books.client.bo.books.BooksBatchLaunchApiBO;
import com.dbj.classpal.books.client.bo.books.BooksInfoPageBO;
import com.dbj.classpal.books.client.bo.books.BooksInfoSaleConfigBO;
import com.dbj.classpal.books.client.bo.books.BooksInfoSaveApiBO;
import com.dbj.classpal.books.client.bo.books.BooksInfoToggleBO;
import com.dbj.classpal.books.client.bo.books.BooksInfoUpdApiBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInfoSaveApiBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInfoUpdApiBO;
import com.dbj.classpal.books.client.bo.books.ProductInfoApiBO;
import com.dbj.classpal.books.client.bo.books.ProductInfoIdApiBO;
import com.dbj.classpal.books.client.dto.books.BooksCategoryNameTreeApiDTO;
import com.dbj.classpal.books.client.dto.books.BooksInfoDetailApiDTO;
import com.dbj.classpal.books.client.dto.books.BooksInfoPageApiDTO;
import com.dbj.classpal.books.client.dto.books.BooksInfoProductSalesConfigDTO;
import com.dbj.classpal.books.client.dto.books.BooksInfoRightScopeDTO;
import com.dbj.classpal.books.client.dto.books.BooksRankInfoApiDTO;
import com.dbj.classpal.books.client.dto.books.BooksScanCountDTO;
import com.dbj.classpal.books.client.dto.books.BooksUserCountDTO;
import com.dbj.classpal.books.client.dto.books.ProductInfoApiDTO;
import com.dbj.classpal.books.client.dto.books.ProductInfoDetailApiDTO;
import com.dbj.classpal.books.client.enums.BusinessTypeEnum;
import com.dbj.classpal.books.common.bo.product.ProductSalesConfigSaveBO;
import com.dbj.classpal.books.common.config.books.BookCodeUrlConfig;
import com.dbj.classpal.books.common.constant.AppErrorCode;
import com.dbj.classpal.books.common.dto.product.ProductSalesConfigDTO;
import com.dbj.classpal.books.common.enums.books.ContentsTypeEnum;
import com.dbj.classpal.books.common.enums.books.RankClassifyEnum;
import com.dbj.classpal.books.common.enums.books.TransferTypeEnum;
import com.dbj.classpal.books.common.enums.product.ProductType;
import com.dbj.classpal.books.service.biz.album.IAppAlbumElementsBusinessRefBiz;
import com.dbj.classpal.books.service.biz.books.IBooksCategoryBiz;
import com.dbj.classpal.books.service.biz.books.IBooksCategoryRefBiz;
import com.dbj.classpal.books.service.biz.books.IBooksInfoBiz;
import com.dbj.classpal.books.service.biz.books.IBooksRankClassifyBiz;
import com.dbj.classpal.books.service.biz.books.IBooksRankInCodesContentsBiz;
import com.dbj.classpal.books.service.biz.books.IBooksRankInCodesContentsQuestionBiz;
import com.dbj.classpal.books.service.biz.books.IBooksRankInfoBiz;
import com.dbj.classpal.books.service.biz.books.IBooksScanInfoBiz;
import com.dbj.classpal.books.service.biz.books.IBooksTransferSettingBiz;
import com.dbj.classpal.books.service.biz.books.IBooksUserRefBiz;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBusinessRefBiz;
import com.dbj.classpal.books.service.biz.question.IQuestionCategoryBusinessRefBiz;
import com.dbj.classpal.books.service.biz.shorts.IShortUrlInfoBiz;
import com.dbj.classpal.books.service.entity.album.AppAlbumElementsBusinessRef;
import com.dbj.classpal.books.service.entity.books.BooksCategoryRef;
import com.dbj.classpal.books.service.entity.books.BooksInfo;
import com.dbj.classpal.books.service.entity.books.BooksRankClassify;
import com.dbj.classpal.books.service.entity.books.BooksRankInCodesContents;
import com.dbj.classpal.books.service.entity.books.BooksRankInCodesContentsQuestion;
import com.dbj.classpal.books.service.entity.books.BooksRankInfo;
import com.dbj.classpal.books.service.entity.books.BooksTransferSetting;
import com.dbj.classpal.books.service.entity.books.BooksUserRef;
import com.dbj.classpal.books.service.entity.material.AppMaterialBusinessRef;
import com.dbj.classpal.books.service.entity.question.QuestionCategoryBusinessRef;
import com.dbj.classpal.books.service.entity.shorts.ShortUrlInfo;
import com.dbj.classpal.books.service.openapi.facade.ApiServiceFacade;
import com.dbj.classpal.books.service.remote.vip.VipRightsTagBusinessRemoteService;
import com.dbj.classpal.books.service.service.product.IProductEntitlementScopesService;
import com.dbj.classpal.books.service.service.product.IProductSalesConfigService;
import com.dbj.classpal.books.service.util.ShortUrlGenerator;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import com.dbj.classpal.framework.commons.utils.Assert;
import com.dbj.classpal.promotion.client.dto.vip.VipRightsTagBusinessRefListDTO;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialApi
 * Date:     2025-04-10 11:40:26
 * Description: 表名： ,描述： 表
 */
@RestController
public class AdminBooksApiImpl implements AdminBooksApi {

    @Resource
    private IBooksInfoBiz booksInfoBiz;
    @Resource
    private IBooksRankInfoBiz bookRankInfoBiz;
    @Resource
    private IBooksRankClassifyBiz bookRankClassify;
    @Resource
    private IBooksRankInCodesContentsBiz bookRankInCodesContents;
    @Resource
    private IBooksRankInCodesContentsQuestionBiz bookRankInCodesContentsQuestion;
    @Resource
    private IQuestionCategoryBusinessRefBiz questionCategoryBusinessRefBiz;
    @Resource
    private IAppAlbumElementsBusinessRefBiz appointmentElementsBusinessRefBiz;
    @Resource
    private IAppMaterialBusinessRefBiz appMaterialBusinessRefBiz;
    @Resource
    private BookCodeUrlConfig bizBookCodeUrlConfig;
    @Resource
    private IBooksTransferSettingBiz bookTransferSettingBiz;
    @Resource
    private IBooksCategoryRefBiz bookCategoryRefBiz;
    @Resource
    private IBooksCategoryBiz bookCategoryCategoryBiz;
    @Resource
    private IProductSalesConfigService productSalesConfigService;
    @Resource
    private IProductEntitlementScopesService productEntitlementScopesService;
    @Resource
    private VipRightsTagBusinessRemoteService vipRightsTagBusinessRemoteService;

    @Resource
    private ShortUrlGenerator shortUrlGenerator;
    @Resource
    private IShortUrlInfoBiz shortUrlInfoBiz;
    @Resource
    private IBooksUserRefBiz booksUserRefBiz;
    @Resource
    private IBooksScanInfoBiz booksScanInfoBiz;
    @Resource
    ApiServiceFacade apiServiceFacade;

    @Override
    public RestResponse<Page<BooksInfoPageApiDTO>> pageInfo(PageInfo<BooksInfoPageBO> pageRequest) throws BusinessException {
        Page<BooksInfoPageApiDTO> page = booksInfoBiz.pageInfo(pageRequest);
        //处理已添加和分类名称
        handleBooksInfoPageDTOList(page.getRecords());
        return RestResponse.success(page);
    }

    private void handleBooksInfoPageDTOList(List<BooksInfoPageApiDTO> booksInfoPageDTOList) {
        if(CollectionUtils.isNotEmpty(booksInfoPageDTOList)){
            List<Integer> idList = booksInfoPageDTOList.stream().map(BooksInfoPageApiDTO::getId).collect(Collectors.toList());
            List<BooksCategoryRef> bookCategoryRefList = bookCategoryRefBiz.lambdaQuery().in(BooksCategoryRef::getBooksId,idList).list();
            Map<Integer,List<BooksCategoryRef>> bookCategoryRefMap = new HashMap<>();
            Map<Integer,String> nameTreeMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(bookCategoryRefList)){
                List<Integer> categoryIdList = bookCategoryRefList.stream().map(BooksCategoryRef::getCategoryId).collect(Collectors.toList());
                bookCategoryRefMap = bookCategoryRefList.stream().collect(Collectors.groupingBy(BooksCategoryRef::getBooksId));
                List<BooksCategoryNameTreeApiDTO> booksCategoryNameTreeApiDTOList = bookCategoryCategoryBiz.categoryNameTree(categoryIdList);
                if(CollectionUtils.isNotEmpty(booksCategoryNameTreeApiDTOList)){
                    nameTreeMap = booksCategoryNameTreeApiDTOList.stream().collect(Collectors.toMap(BooksCategoryNameTreeApiDTO::getId,BooksCategoryNameTreeApiDTO::getNameTree));

                }
            }
            List<BooksUserCountDTO> booksUserCountDTOList = booksUserRefBiz.booksUserCount(idList);
            Map<Integer,Long> booksUserCountMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(booksUserCountDTOList)){
                booksUserCountMap = booksUserCountDTOList.stream().collect(Collectors.toMap(BooksUserCountDTO::getBooksId,BooksUserCountDTO::getUserCount));
            }
            List<BooksScanCountDTO> booksScanCountDTOList = booksScanInfoBiz.bookScanCount(idList);
            Map<Integer,Long> booksScanCountMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(booksScanCountDTOList)){
                booksScanCountMap = booksScanCountDTOList.stream().collect(Collectors.toMap(BooksScanCountDTO::getBooksId,BooksScanCountDTO::getScanCount));
            }
            for(BooksInfoPageApiDTO booksInfoPageApiDTO : booksInfoPageDTOList){
                booksInfoPageApiDTO.setAddNum(booksUserCountMap.get(booksInfoPageApiDTO.getId()));
                booksInfoPageApiDTO.setScanCodeNum(booksScanCountMap.get(booksInfoPageApiDTO.getId()));
                List<BooksCategoryRef> refList =bookCategoryRefMap.get(booksInfoPageApiDTO.getId());
                if(CollectionUtils.isNotEmpty(refList)){
                    List<String> categoryTreeNames = new ArrayList<>();
                    Map<Integer, String> finalNameTreeMap = nameTreeMap;
                    refList.forEach(ref ->{
                        if(finalNameTreeMap.containsKey(ref.getCategoryId())){
                            categoryTreeNames.add(finalNameTreeMap.get(ref.getCategoryId()));
                        }
                    });
                    booksInfoPageApiDTO.setCategoryTreeNames(categoryTreeNames);
                }
            }
        }
    }

    @Override
    public RestResponse<Boolean> save(BooksInfoSaveApiBO saveBO) throws BusinessException {
        BooksInfo booksInfo = new BooksInfo();
        BeanUtil.copyProperties(saveBO, booksInfo);
        List<BooksRankInfoSaveApiBO> booksRankInfoSaveApiBOList = saveBO.getBooksRankInfoApiBOList();
        //保存跳转默认设置
        BooksTransferSetting bizSetting = new BooksTransferSetting();
        bizSetting.setBookId(booksInfo.getId());
        bizSetting.setTransferType(TransferTypeEnum.H5.getCode());
        bookTransferSettingBiz.save(bizSetting);
        //保存分类信息
        booksInfoBiz.save(booksInfo);
        List<Integer> categoryIds = saveBO.getCategoryIds();
        if(CollectionUtils.isNotEmpty(categoryIds)){
            List<BooksCategoryRef> registerList = new ArrayList<>();
            for(Integer categoryId : categoryIds){
                BooksCategoryRef bookCategoryRef = new BooksCategoryRef();
                bookCategoryRef.setBooksId(booksInfo.getId());
                bookCategoryRef.setCategoryId(categoryId);
                registerList.add(bookCategoryRef);
            }
            bookCategoryRefBiz.saveBatch(registerList);
        }

        //给图书默认一个强制跳转连接
        if(CollectionUtils.isNotEmpty(booksRankInfoSaveApiBOList)){
            booksInfo.setVolumeNum(booksRankInfoSaveApiBOList.size());
            booksInfo.setPicUrl(booksRankInfoSaveApiBOList.get(0).getPicUrl());
            List<BooksRankInfo> booksRankInfoList = BeanUtil.copyToList(booksRankInfoSaveApiBOList, BooksRankInfo.class);
            Map<String,String> h5PageUrlMap = new HashMap<>();
            Map<String,String> newPrintCodeUrlMap = new HashMap<>();
            String shortUrl =bizBookCodeUrlConfig.getShortUrl();
            BookCodeUrlConfig.Rank rank = bizBookCodeUrlConfig.getRank();
            for(BooksRankInfo booksRankInfo : booksRankInfoList){
                booksRankInfo.setBookId(booksInfo.getId());
                String h5ShortCode = shortUrlGenerator.generate();
                String newPrintCode = shortUrlGenerator.generate();
                String h5PageUrl = shortUrl + h5ShortCode;
                String newPrintCodeUrl = shortUrl + newPrintCode;
                h5PageUrlMap.put(h5PageUrl, h5ShortCode);
                newPrintCodeUrlMap.put(newPrintCodeUrl, newPrintCode);
                booksRankInfo.setH5PageUrl(h5PageUrl);
                booksRankInfo.setNewPrintCodeUrl(newPrintCodeUrl);
            }
            bookRankInfoBiz.saveBatch(booksRankInfoList);

            List<BooksRankClassify> registerList = new ArrayList<>();
            List<ShortUrlInfo> shortUrlInfoList = new ArrayList<>();
            for(BooksRankInfo booksRankInfo : booksRankInfoList){
                String h5ShortCode = h5PageUrlMap.get(booksRankInfo.getH5PageUrl());
                String newPrintCode = newPrintCodeUrlMap.get(booksRankInfo.getNewPrintCodeUrl());
                if(StringUtils.isNotEmpty(h5ShortCode)){
                    ShortUrlInfo shortUrlInfo = new ShortUrlInfo();
                    shortUrlInfo.setShortCode(h5ShortCode);


                    shortUrlInfo.setLongUrl(MessageFormat.format(rank.getH5PageUrl(),booksRankInfo.getBookId(), booksRankInfo.getId()));
                    shortUrlInfoList.add(shortUrlInfo);
                }
                if(StringUtils.isNotEmpty(h5ShortCode)){
                    ShortUrlInfo shortUrlInfo = new ShortUrlInfo();
                    shortUrlInfo.setShortCode(newPrintCode);
                    ;
                    shortUrlInfo.setLongUrl(MessageFormat.format(rank.getNewPrintCodeUrl(),booksRankInfo.getBookId(), booksRankInfo.getId()));
                    shortUrlInfoList.add(shortUrlInfo);
                }

                //这里还需要默认给添加一个书内码的 数据
                BooksRankClassify bizRankClassify = new BooksRankClassify();
                bizRankClassify.setRankId(booksRankInfo.getId());
                bizRankClassify.setType(RankClassifyEnum.BOOK_IN_CODES.getCode());
                bizRankClassify.setName(RankClassifyEnum.BOOK_IN_CODES.getName());
                bizRankClassify.setWeight(999);
                registerList.add(bizRankClassify);
            }
            if(CollectionUtils.isNotEmpty(shortUrlInfoList)){
                shortUrlInfoBiz.batchSaveShortUrlInfo(shortUrlInfoList);
            }
            bookRankClassify.saveBatch(registerList);

        }

        booksInfoBiz.updateById(booksInfo);
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> update(BooksInfoUpdApiBO updBO) throws BusinessException {
        BooksInfo booksInfo = booksInfoBiz.getById(updBO.getId());
        if(booksInfo == null){
            throw new BusinessException(AppErrorCode.BOOK_INFO_NOT_EXIST_CODE,AppErrorCode.BOOK_INFO_NOT_EXIST_MSG);
        }
        booksInfo = new BooksInfo();
        BeanUtil.copyProperties(updBO, booksInfo);

        List<BooksRankInfoUpdApiBO> booksRankInfoApiBOList = updBO.getBooksRankInfoApiBOList();
        if(CollectionUtils.isNotEmpty(booksRankInfoApiBOList)){
            booksInfo.setVolumeNum(booksRankInfoApiBOList.size());
            booksInfo.setPicUrl(booksRankInfoApiBOList.get(0).getPicUrl());
            // 所有被删除的
            List<Integer> bookRankInfoIdList = booksRankInfoApiBOList.stream().filter(d -> d.getId() != null).map(BooksRankInfoUpdApiBO::getId).collect(Collectors.toList());
            List<BooksRankInfo> delBooksRankInfoList = bookRankInfoBiz.lambdaQuery().eq(BooksRankInfo::getBookId, booksInfo.getId())
                    .notIn(CollectionUtils.isNotEmpty(bookRankInfoIdList),BooksRankInfo::getId, bookRankInfoIdList).list();
            delRank(delBooksRankInfoList);
            //新增修改其他的数据
            List<BooksRankInfo> addRankInfoList = new ArrayList<>();
            List<BooksRankInfo> updRankInfoList = new ArrayList<>();
            Map<String,String> h5PageUrlMap = new HashMap<>();
            Map<String,String> newPrintCodeUrlMap = new HashMap<>();
            String shortUrl =bizBookCodeUrlConfig.getShortUrl();
            for(BooksRankInfoUpdApiBO booksRankInfoUpdApiBO : booksRankInfoApiBOList){
                BooksRankInfo booksRankInfo = BeanUtil.copyProperties(booksRankInfoUpdApiBO, BooksRankInfo.class);
                if(booksRankInfoUpdApiBO.getId() == null){
                    booksRankInfo.setBookId(booksInfo.getId());
                    String h5ShortCode = shortUrlGenerator.generate();
                    String newPrintCode = shortUrlGenerator.generate();
                    String h5PageUrl = shortUrl + h5ShortCode;
                    String newPrintCodeUrl = shortUrl + newPrintCode;
                    h5PageUrlMap.put(h5PageUrl, h5ShortCode);
                    newPrintCodeUrlMap.put(newPrintCodeUrl, newPrintCode);

                    booksRankInfo.setH5PageUrl(h5PageUrl);
                    booksRankInfo.setNewPrintCodeUrl(newPrintCodeUrl);
                    addRankInfoList.add(booksRankInfo);
                }else {
                    updRankInfoList.add(booksRankInfo);
                }

            }
            if(CollectionUtils.isNotEmpty(addRankInfoList)){
                bookRankInfoBiz.saveBatch(addRankInfoList);
                List<BooksRankClassify> registerList = new ArrayList<>();
                List<ShortUrlInfo> shortUrlInfoList = new ArrayList<>();
                BookCodeUrlConfig.Rank rank = bizBookCodeUrlConfig.getRank();
                for(BooksRankInfo booksRankInfo : addRankInfoList){
                    //这里还需要默认给添加一个书内码的 数据
                    String h5ShortCode = h5PageUrlMap.get(booksRankInfo.getH5PageUrl());
                    String newPrintCode = newPrintCodeUrlMap.get(booksRankInfo.getNewPrintCodeUrl());

                    if(StringUtils.isNotEmpty(h5ShortCode)){
                        ShortUrlInfo shortUrlInfo = new ShortUrlInfo();
                        shortUrlInfo.setShortCode(h5ShortCode);
                        shortUrlInfo.setLongUrl(MessageFormat.format(rank.getH5PageUrl(),booksRankInfo.getBookId(), booksRankInfo.getId()));
                        shortUrlInfoList.add(shortUrlInfo);
                    }
                    if(StringUtils.isNotEmpty(h5ShortCode)){
                        ShortUrlInfo shortUrlInfo = new ShortUrlInfo();
                        shortUrlInfo.setShortCode(newPrintCode);
                        shortUrlInfo.setLongUrl(MessageFormat.format(rank.getNewPrintCodeUrl(),booksRankInfo.getBookId(), booksRankInfo.getId()));
                        shortUrlInfoList.add(shortUrlInfo);
                    }

                    BooksRankClassify bizRankClassify = new BooksRankClassify();
                    bizRankClassify.setRankId(booksRankInfo.getId());
                    bizRankClassify.setType(RankClassifyEnum.BOOK_IN_CODES.getCode());
                    bizRankClassify.setName(RankClassifyEnum.BOOK_IN_CODES.getName());
                    bizRankClassify.setWeight(999);
                    registerList.add(bizRankClassify);
                }
                if(CollectionUtils.isNotEmpty(shortUrlInfoList)){
                    shortUrlInfoBiz.batchSaveShortUrlInfo(shortUrlInfoList);
                }
                bookRankClassify.saveBatch(registerList);
            }
            if(CollectionUtils.isNotEmpty(updRankInfoList)){
                bookRankInfoBiz.updateBatchById(updRankInfoList);
            }

        }
        booksInfoBiz.updateById(booksInfo);

        //保存分类信息
        bookCategoryRefBiz.lambdaUpdate().eq(BooksCategoryRef::getBooksId, booksInfo.getId()).remove();
        List<Integer> categoryIds = updBO.getCategoryIds();
        if(CollectionUtils.isNotEmpty(categoryIds)){
            List<BooksCategoryRef> booksCategoryRefList = new ArrayList<>();
            for(Integer categoryId : categoryIds){
                BooksCategoryRef bookCategoryRef = new BooksCategoryRef();
                bookCategoryRef.setBooksId(booksInfo.getId());
                bookCategoryRef.setCategoryId(categoryId);
                booksCategoryRefList.add(bookCategoryRef);
            }
            bookCategoryRefBiz.saveBatch(booksCategoryRefList);
        }

        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> delete(Integer id) throws BusinessException {
        BooksInfo bookInfo = booksInfoBiz.getById(id);
        if(bookInfo == null){
            throw new BusinessException(AppErrorCode.BOOK_INFO_NOT_EXIST_CODE,AppErrorCode.BOOK_INFO_NOT_EXIST_MSG);
        }
        //获取册数
        List<BooksRankInfo> booksRankInfoList = bookRankInfoBiz.lambdaQuery().eq(BooksRankInfo::getBookId, id).list();
        delRank(booksRankInfoList);
        booksInfoBiz.removeById(bookInfo.getId());
        bookCategoryRefBiz.lambdaUpdate().eq(BooksCategoryRef::getBooksId, bookInfo.getId()).remove();
        bookTransferSettingBiz.lambdaUpdate().eq(BooksTransferSetting::getBookId, bookInfo.getId()).remove();
        booksUserRefBiz.lambdaUpdate().eq(BooksUserRef::getBooksId, bookInfo.getId()).remove();
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> batchDelete(List<Integer> ids) throws BusinessException {
        List<BooksInfo> booksInfoList = booksInfoBiz.listByIds(ids);
        if(CollectionUtils.isEmpty(booksInfoList)){
            throw new BusinessException(AppErrorCode.BOOK_INFO_NOT_EXIST_CODE,AppErrorCode.BOOK_INFO_NOT_EXIST_MSG);
        }
        List<BooksRankInfo> booksRankInfoList = bookRankInfoBiz.lambdaQuery().in(BooksRankInfo::getBookId, ids).list();
        delRank(booksRankInfoList);
        booksInfoBiz.removeBatchByIds(booksInfoList);
        bookCategoryRefBiz.lambdaUpdate().in(BooksCategoryRef::getBooksId, ids).remove();
        bookTransferSettingBiz.lambdaUpdate().in(BooksTransferSetting::getBookId, ids).remove();
        booksUserRefBiz.lambdaUpdate().in(BooksUserRef::getBooksId, ids).remove();
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<BooksInfoDetailApiDTO> detail(Integer id) throws BusinessException {
        BooksInfo bookInfo = booksInfoBiz.getById(id);
        if(bookInfo == null){
            throw new BusinessException(AppErrorCode.BOOK_INFO_NOT_EXIST_CODE,AppErrorCode.BOOK_INFO_NOT_EXIST_MSG);
        }



        List<BooksRankInfo> booksRankInfoList = bookRankInfoBiz.lambdaQuery().eq(BooksRankInfo::getBookId, id).orderByAsc(BooksRankInfo::getSerialNo).list();
        BooksInfoDetailApiDTO booksInfoDetailApiDTO = new BooksInfoDetailApiDTO();
        BeanUtil.copyProperties(bookInfo, booksInfoDetailApiDTO);

        List<BooksCategoryRef> booksCategoryRefList = bookCategoryRefBiz.lambdaQuery().eq(BooksCategoryRef::getBooksId, id).list();
        if(CollectionUtils.isNotEmpty(booksCategoryRefList)){
            List<Integer> categoryIds = booksCategoryRefList.stream().map(BooksCategoryRef::getCategoryId).collect(Collectors.toList());
            booksInfoDetailApiDTO.setCategoryIds(categoryIds);
        }

        booksInfoDetailApiDTO.setBooksRankInfoApiDTOList(BeanUtil.copyToList(booksRankInfoList, BooksRankInfoApiDTO.class));
        return RestResponse.success(booksInfoDetailApiDTO);
    }

    @Override
    public RestResponse<Boolean> batchHide(BooksBatchHideApiBO batchHideBO) throws BusinessException {
        booksInfoBiz.lambdaUpdate().in(BooksInfo::getId, batchHideBO.getIds()).set(BooksInfo::getIsHide, batchHideBO.getIsHide()).update();
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> batchLaunch(BooksBatchLaunchApiBO batchLaunchBO) throws BusinessException {
        booksInfoBiz.lambdaUpdate().in(BooksInfo::getId, batchLaunchBO.getIds()).set(BooksInfo::getLaunchStatus, batchLaunchBO.getLaunchStatus()).update();
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<List<ProductInfoApiDTO>> queryProductListByCodeOrName(ProductInfoApiBO request) throws BusinessException {
        ProductInfoRequest serviceReq = BeanUtil.copyProperties(request, ProductInfoRequest.class);
        List<ProductInfoResponse> serviceResp = apiServiceFacade.printerRemoteService().queryProductListByCodeOrName(serviceReq);
        if(serviceResp != null) {
            return RestResponse.success(BeanUtil.copyToList(serviceResp, ProductInfoApiDTO.class));
        }
        return null;
    }

    @Override
    public RestResponse<ProductInfoDetailApiDTO> queryProductInfoById(ProductInfoIdApiBO request) throws BusinessException {
        ProductInfoIdRequest serviceReq = BeanUtil.copyProperties(request, ProductInfoIdRequest.class);
        ProductInfoDetailResponse serviceResp = apiServiceFacade.printerRemoteService().queryProductInfoById(serviceReq);
        ProductInfoDetailApiDTO productInfoDetailApiDTO = null;
        if(serviceResp != null){
            productInfoDetailApiDTO = BeanUtil.copyProperties(serviceResp, ProductInfoDetailApiDTO.class);
            productInfoDetailApiDTO.setBookName(serviceResp.getProductName());
            return RestResponse.success(productInfoDetailApiDTO);
        }
        return RestResponse.success(productInfoDetailApiDTO);
    }

    @Override
    public RestResponse<List<BooksInfoPageApiDTO>> list(BooksInfoPageBO bo) throws BusinessException {
        PageInfo<BooksInfoPageBO> pageInfo = new PageInfo<>();
        pageInfo.setPageNum(1);
        pageInfo.setPageSize(500);
        pageInfo.setData(bo);
        Page<BooksInfoPageApiDTO> page = booksInfoBiz.pageInfo(pageInfo);
        return RestResponse.success(page.getRecords());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public RestResponse<Boolean> toggle(BooksInfoToggleBO bo) throws BusinessException {
        Assert.isTrue(booksInfoBiz.lambdaUpdate()
                .set(BooksInfo::getStatus, bo.getStatus())
                .in(BooksInfo::getId, bo.getIds())
                .update(), "处理失败");
        return RestResponse.success();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public RestResponse<Boolean> saveSalesConfig(BooksInfoSaleConfigBO bo) throws BusinessException {
        ProductSalesConfigSaveBO salesConfigBO = BeanUtil.copyProperties(bo, ProductSalesConfigSaveBO.class)
                .setProductId(bo.getBooksId())
                .setProductType(ProductType.BOOKS.getCode());
        Integer configId = productSalesConfigService.save(salesConfigBO);

        // vip权益
        vipRightsTagBusinessRemoteService.bindVipRightsTagBusinessRef(BusinessTypeEnum.BOOKS_SALES_CONFIG_BUSINESS, configId, bo.getVipRightsTagIds());

        // 权益范围
        productEntitlementScopesService.save(configId, bo.getRightScopeList());
        return RestResponse.success();
    }

    @Override
    public RestResponse<BooksInfoProductSalesConfigDTO> getSalesConfig(Integer bookId) throws BusinessException {
        BooksInfoProductSalesConfigDTO salesConfig = new BooksInfoProductSalesConfigDTO();
        ProductSalesConfigDTO dto = productSalesConfigService.getProductSalesConfig(ProductType.BOOKS, bookId);
        BeanUtil.copyProperties(dto, salesConfig);

        // vip权益标签
        List<VipRightsTagBusinessRefListDTO> vipRightsTagList = vipRightsTagBusinessRemoteService.getVipRightsTagList(BusinessTypeEnum.BOOKS_SALES_CONFIG_BUSINESS, Set.of(dto.getId()));
        Set<Integer> tagIds = vipRightsTagList.stream().map(VipRightsTagBusinessRefListDTO::getVipRightsTagId).collect(Collectors.toSet());
        salesConfig.setVipRightsTagIds(tagIds);

        // 权益范围
        List<BooksInfoRightScopeDTO> rightScopeList = productEntitlementScopesService.getScopeList(dto.getId());
        salesConfig.setRightScopeList(rightScopeList);
        return RestResponse.success(salesConfig);
    }


    public void delRank(List<BooksRankInfo> delBooksRankInfoList){
        if(CollectionUtils.isNotEmpty(delBooksRankInfoList)){
            List<Integer> delBookRankInfoIdList = delBooksRankInfoList.stream().map(BooksRankInfo::getId).collect(Collectors.toList());
            // 删除册数 ，删除册数对应的书内码 、目录、关联信息等
            bookRankInfoBiz.removeByIds(delBookRankInfoIdList);
            List<BooksRankClassify> registerList = bookRankClassify.lambdaQuery().eq(BooksRankClassify::getRankId,delBookRankInfoIdList).list();
            if(CollectionUtils.isNotEmpty(registerList)){
                List<Integer> delBookRankClassifyIdList = registerList.stream().map(BooksRankClassify::getId).collect(Collectors.toList());
                bookRankClassify.removeByIds(delBookRankClassifyIdList);
                List<BooksRankInCodesContents> booksRankInCodesContentsList = bookRankInCodesContents.lambdaQuery().in(BooksRankInCodesContents::getRankClassifyId,delBookRankClassifyIdList).list();
                if(CollectionUtils.isNotEmpty(booksRankInCodesContentsList)){
                    List<Integer> delContentsIdList = new ArrayList<>();
                    List<Integer> questionContentsIdList = new ArrayList<>();
                    List<Integer> resourceContentsIdList = new ArrayList<>();
                    List<Integer> audioContentsIdList = new ArrayList<>();
                    List<Integer> videoContentsIdList = new ArrayList<>();
                    booksRankInCodesContentsList.forEach(d -> {
                        ContentsTypeEnum contentsTypeEnum = ContentsTypeEnum.getByCode(d.getType());
                        delContentsIdList.add(d.getId());
                        switch (contentsTypeEnum) {
                            case RESOURCE:
                                resourceContentsIdList.add(d.getId());
                                break;
                            case QUESTION:
                                questionContentsIdList.add(d.getId());
                                break;
                            case AUDIO:
                                audioContentsIdList.add(d.getId());
                                break;
                            case VIDEO:
                                videoContentsIdList.add(d.getId());
                                break;
                            default:
                                break;

                        }
                    });
                    bookRankInCodesContents.removeByIds(delContentsIdList);
                    if(CollectionUtils.isNotEmpty(resourceContentsIdList)){
                        appMaterialBusinessRefBiz.lambdaUpdate().in(AppMaterialBusinessRef::getBusinessId,resourceContentsIdList)
                                .eq(AppMaterialBusinessRef::getBusinessType,BusinessTypeEnum.BOOKS_RESOURCE_BUSINESS.getCode()).remove();
                    }
                    if(CollectionUtils.isNotEmpty(questionContentsIdList)){
                        List<BooksRankInCodesContentsQuestion> booksRankInCodesContentsQuestionList = bookRankInCodesContentsQuestion.lambdaQuery().eq(BooksRankInCodesContentsQuestion::getInCodesContentsId,questionContentsIdList).list();

                        if(CollectionUtils.isNotEmpty(booksRankInCodesContentsQuestionList)){
                            List<Integer> questionIdList = booksRankInCodesContentsQuestionList.stream().map(BooksRankInCodesContentsQuestion::getId).collect(Collectors.toList());
                            bookRankInCodesContentsQuestion.removeByIds(questionIdList);
                            questionCategoryBusinessRefBiz.lambdaUpdate().in(QuestionCategoryBusinessRef::getBusinessId,questionIdList)
                                    .eq(QuestionCategoryBusinessRef::getBusinessType, BusinessTypeEnum.QUESTION_BUSINESS.getCode()).remove();
                        }

                    }
                    if(CollectionUtils.isNotEmpty(audioContentsIdList)){
                        appointmentElementsBusinessRefBiz.lambdaUpdate().in(AppAlbumElementsBusinessRef::getBusinessId,audioContentsIdList)
                                .eq(AppAlbumElementsBusinessRef::getBusinessType,BusinessTypeEnum.BOOKS_AUDIO_BUSINESS.getCode()).remove();
                    }
                    if(CollectionUtils.isNotEmpty(videoContentsIdList)){
                        appointmentElementsBusinessRefBiz.lambdaUpdate().in(AppAlbumElementsBusinessRef::getBusinessId,videoContentsIdList)
                                .eq(AppAlbumElementsBusinessRef::getBusinessType,BusinessTypeEnum.BOOKS_VIDEO_BUSINESS.getCode()).remove();
                    }

                }

            }
        }
    }
}
