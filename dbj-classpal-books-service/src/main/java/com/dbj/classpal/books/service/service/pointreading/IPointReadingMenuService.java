package com.dbj.classpal.books.service.service.pointreading;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingMenuQueryBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingMenuSaveBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingMenuUpdateBO;
import com.dbj.classpal.books.common.dto.pointreading.PointReadingMenuDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;

import com.dbj.classpal.framework.commons.request.PageInfo;
import java.util.List;

/**
 * 点读书目录 服务接口
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface IPointReadingMenuService {

    /**
     * 分页查询点读书目录
     *
     * @param pageInfo 分页参数
     * @return 分页结果
     */
    Page<PointReadingMenuDTO> pageMenu(PageInfo<PointReadingMenuQueryBO> pageInfo) throws BusinessException;

    /**
     * 查询目录详情
     *
     * @param id 目录ID
     * @return 目录详情
     */
    PointReadingMenuDTO detail(Integer id) throws BusinessException;

    /**
     * 保存点读书目录
     *
     * @param saveBO 保存参数
     * @return 目录ID
     */
    Integer save(PointReadingMenuSaveBO saveBO) throws BusinessException;

    /**
     * 更新点读书目录
     *
     * @param updateBO 更新参数
     * @return 是否成功
     */
    Boolean update(PointReadingMenuUpdateBO updateBO) throws BusinessException;

    /**
     * 删除点读书目录
     *
     * @param id 目录ID
     * @return 是否成功
     */
    Boolean delete(Integer id) throws BusinessException;

    /**
     * 批量删除点读书目录
     *
     * @param ids 目录ID列表
     * @return 是否成功
     */
    Boolean deleteBatch(List<Integer> ids) throws BusinessException;

    /**
     * 获取目录树形结构
     *
     * @param bookId 点读书ID
     * @return 目录树
     */
    List<PointReadingMenuDTO> getMenuTree(Integer bookId) throws BusinessException;

    /**
     * 获取子目录列表
     *
     * @param parentId 父级目录ID
     * @return 子目录列表
     */
    List<PointReadingMenuDTO> getChildren(Integer parentId) throws BusinessException;

    /**
     * 更新排序
     *
     * @param id 目录ID
     * @param sortNum 排序号
     * @return 是否成功
     */
    Boolean updateSort(Integer id, Integer sortNum) throws BusinessException;

    /**
     * 启用目录
     *
     * @param id 目录ID
     * @return 是否成功
     */
    Boolean enable(Integer id) throws BusinessException;

    /**
     * 禁用目录
     *
     * @param id 目录ID
     * @return 是否成功
     */
    Boolean disable(Integer id) throws BusinessException;
}
