package com.dbj.classpal.books.service.service.question.impl;

import com.dbj.classpal.books.common.bo.question.QuestionCategoryBusinessSettingsBO;
import com.dbj.classpal.books.common.dto.question.QuestionCategoryBusinessSettingsDTO;
import com.dbj.classpal.books.service.biz.question.IQuestionCategoryBusinessSettingsBiz;
import com.dbj.classpal.books.service.service.question.IQuestionCategoryBusinessSettingsService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import jakarta.annotation.Resource;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 题库业务设置统一管理 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@Service
public class QuestionCategoryBusinessSettingsServiceImpl implements IQuestionCategoryBusinessSettingsService {

    @Resource
    private IQuestionCategoryBusinessSettingsBiz questionCategoryBusinessSettingsBiz;

    @Override
    public boolean createSettings(QuestionCategoryBusinessSettingsBO bo) throws BusinessException {
        return questionCategoryBusinessSettingsBiz.createSettings(bo);
    }

    @Override
    public boolean updateSettings(QuestionCategoryBusinessSettingsBO bo) throws BusinessException {
        return questionCategoryBusinessSettingsBiz.updateSettings(bo);
    }

    @Override
    public QuestionCategoryBusinessSettingsDTO getSettingsByBusiness(Integer businessId, Integer businessType) throws BusinessException {
        return questionCategoryBusinessSettingsBiz.getSettingsByBusiness(businessId, businessType);
    }

    @Override
    public boolean deleteSettingsByBusiness(Integer businessId, Integer businessType) throws BusinessException {
        return questionCategoryBusinessSettingsBiz.deleteSettingsByBusiness(businessId, businessType);
    }

    @Override
    public boolean hasSettings(Integer businessId, Integer businessType) {
        return questionCategoryBusinessSettingsBiz.hasSettings(businessId, businessType);
    }

    @Override
    public boolean createSettingsBatch(List<QuestionCategoryBusinessSettingsBO> settingsList) throws BusinessException {
        return questionCategoryBusinessSettingsBiz.createSettingsBatch(settingsList);
    }

    @Override
    public boolean deleteSettingsBatch(List<Integer> businessIds, Integer businessType) throws BusinessException {
        return questionCategoryBusinessSettingsBiz.deleteSettingsBatch(businessIds, businessType);
    }
}
