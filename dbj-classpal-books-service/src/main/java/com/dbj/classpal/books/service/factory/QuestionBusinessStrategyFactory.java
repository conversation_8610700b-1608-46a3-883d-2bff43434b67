package com.dbj.classpal.books.service.factory;

import com.dbj.classpal.books.service.strategy.question.IPaperBusinessStrategy;
import com.dbj.classpal.books.service.strategy.question.IQuestionBusinessStrategy;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;

/**
 * 业务策略工厂
 */
@Component
public class QuestionBusinessStrategyFactory {

    @Resource
    private List<IQuestionBusinessStrategy> questionStrategies;

    @Resource
    private List<IPaperBusinessStrategy> paperStrategies;

    private final Map<Integer, IQuestionBusinessStrategy> questionStrategyMap = new HashMap<>();
    private final Map<Integer, IPaperBusinessStrategy> paperStrategyMap = new HashMap<>();

    @PostConstruct
    public void init() {
        questionStrategies.forEach(strategy -> questionStrategyMap.put(strategy.getBusinessType(), strategy));
        paperStrategies.forEach(strategy -> paperStrategyMap.put(strategy.getBusinessType(), strategy));
    }

    /**
     * 获取题目业务策略
     *
     * @param businessType 业务类型
     * @return 题目业务策略
     */
    public IQuestionBusinessStrategy getQuestionStrategy(Integer businessType) throws BusinessException {
        if (businessType == null) {
            throw new BusinessException(APP_BUSINESS_TYPE_NOT_NULL_CODE,APP_BUSINESS_TYPE_NOT_NULL_MSG);
        }
        IQuestionBusinessStrategy strategy = questionStrategyMap.get(businessType);
        if (strategy == null) {
            throw new BusinessException(APP_QUESTION_BUSINESS_STRATEGY_NOT_EXIST_CODE,APP_QUESTION_BUSINESS_STRATEGY_NOT_EXIST_MSG);
        }
        return strategy;
    }

    /**
     * 获取试卷业务策略
     *
     * @param businessType 业务类型
     * @return 试卷业务策略
     */
    public IPaperBusinessStrategy getPaperStrategy(Integer businessType) throws BusinessException {
        if (businessType == null) {
            throw new BusinessException(APP_BUSINESS_TYPE_NOT_NULL_CODE,APP_BUSINESS_TYPE_NOT_NULL_MSG);
        }
        IPaperBusinessStrategy strategy = paperStrategyMap.get(businessType);
        if (strategy == null) {
            throw new BusinessException(APP_PAPER_BUSINESS_STRATEGY_NOT_EXIST_CODE,APP_PAPER_BUSINESS_STRATEGY_NOT_EXIST_MSG);
        }
        return strategy;
    }



} 