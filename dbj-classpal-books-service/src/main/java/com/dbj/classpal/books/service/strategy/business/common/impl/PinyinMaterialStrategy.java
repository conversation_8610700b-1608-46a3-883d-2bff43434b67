package com.dbj.classpal.books.service.strategy.business.common.impl;

import com.dbj.classpal.books.service.biz.pinyin.IPinyinBiz;
import com.dbj.classpal.books.service.entity.pinyin.Pinyin;
import com.dbj.classpal.books.service.strategy.business.handler.ICommonBusinessStrategyHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 拼音 媒体引用策略实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Component
public class PinyinMaterialStrategy extends ICommonBusinessStrategyHandler {

    @Resource
    private IPinyinBiz pinyinBiz;

    @Override
    public Map<Integer, String> getBusinessName(List<Integer> businessId) {
        List<Pinyin> pinyinList = pinyinBiz.listByIds(businessId);
        return pinyinList.stream().collect(Collectors.toMap(Pinyin::getId, Pinyin::getTitle));
    }
}
