package com.dbj.classpal.books.service.service.studycenter;

import com.dbj.classpal.books.common.bo.studycenter.AppStudyModuleQuestionExtCreateBO;
import com.dbj.classpal.books.common.dto.studycenter.AppStudyModuleQuestionExtDetailDTO;
import com.dbj.classpal.books.common.dto.studycenter.AppStudyModuleQuestionExtUpdateBO;

public interface IAppStudyModuleQuestionExtService {
    boolean create(AppStudyModuleQuestionExtCreateBO bo);
    boolean update(AppStudyModuleQuestionExtUpdateBO bo);
    boolean delete(Integer id);
    AppStudyModuleQuestionExtDetailDTO detail(Integer id);
}