package com.dbj.classpal.books.service.service.config;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.common.bo.config.BasicConfigBO;
import com.dbj.classpal.books.common.bo.config.BasicConfigIdBO;
import com.dbj.classpal.books.common.bo.config.BasicConfigIdsBO;
import com.dbj.classpal.books.common.bo.config.BasicConfigQueryBO;
import com.dbj.classpal.books.common.dto.config.BasicConfigDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;

import java.util.List;

public interface BasicConfigService {

    /**
     * 创建
     */
    Integer create(BasicConfigBO bo) throws BusinessException;

    /**
     * 更新
     */
    void update(BasicConfigBO bo) throws BusinessException;

    /**
     * 删除
     */
    void batchDelete(BasicConfigIdsBO idsBO) throws BusinessException;

    /**
     * 获取详情
     */
    BasicConfigDTO detail(BasicConfigIdBO idBO);

    /**
     * 获取列表
     */
    Page<BasicConfigDTO> pageList(PageInfo<BasicConfigQueryBO> queryBO);
    /**
     * 更新排序
     */
    void updateSort(List<BasicConfigBO> boList);

    List<BasicConfigDTO> list(BasicConfigQueryBO serviceBo);
} 