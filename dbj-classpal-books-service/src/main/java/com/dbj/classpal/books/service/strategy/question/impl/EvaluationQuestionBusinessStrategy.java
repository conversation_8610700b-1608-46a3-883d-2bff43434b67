package com.dbj.classpal.books.service.strategy.question.impl;

import com.dbj.classpal.app.client.bo.user.UserIdApiBO;
import com.dbj.classpal.app.client.dto.user.CurrentUserApiDTO;
import com.dbj.classpal.books.common.bo.evaluation.app.AppUserPaperEvaluationSaveBO;
import com.dbj.classpal.books.common.bo.paper.*;
import com.dbj.classpal.books.common.constant.AppErrorCode;
import com.dbj.classpal.books.common.dto.evaluation.app.AppUserPaperEvaluationSaveDTO;
import com.dbj.classpal.books.common.dto.paper.PaperQuestionDTO;
import com.dbj.classpal.books.client.enums.BusinessTypeEnum;
import com.dbj.classpal.books.common.enums.question.QuestionTypeEnum;
import com.dbj.classpal.books.service.biz.evaluation.IAppEvaluationNodeBiz;
import com.dbj.classpal.books.service.biz.evaluation.IAppUserPaperEvaluationAnalysisBiz;
import com.dbj.classpal.books.service.biz.evaluation.IAppUserPaperEvaluationBiz;
import com.dbj.classpal.books.service.biz.question.QuestionBiz;
import com.dbj.classpal.books.service.biz.question.QuestionBusinessRefBiz;
import com.dbj.classpal.books.service.entity.evaluation.AppEvaluationNode;
import com.dbj.classpal.books.service.entity.evaluation.AppUserPaperEvaluationAnalysis;
import com.dbj.classpal.books.service.entity.question.Question;
import com.dbj.classpal.books.service.entity.question.QuestionBusinessRef;
import com.dbj.classpal.books.service.remote.sys.SysAppUserRemoteService;
import com.dbj.classpal.books.service.strategy.question.AbstractQuestionBusinessStrategy;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;
import static com.dbj.classpal.books.common.enums.question.QuestionAnswerResultEnum.ANSWER_CORRECT;
import static com.dbj.classpal.books.common.enums.question.QuestionAnswerResultEnum.ANSWER_WRONG;

/**
 * 图书题目业务策略实现
 */
@Slf4j
@Component
public class EvaluationQuestionBusinessStrategy extends AbstractQuestionBusinessStrategy{
    @Resource
    private QuestionBiz questionBiz;
    @Resource
    private SysAppUserRemoteService sysAppUserRemoteService;
    @Resource
    private IAppUserPaperEvaluationAnalysisBiz appUserPaperEvaluationAnalysisBiz;
    @Resource
    private IAppUserPaperEvaluationBiz appUserPaperEvaluationBiz;
    @Resource
    private IAppEvaluationNodeBiz appEvaluationNodeBiz;
    @Resource
    private QuestionBusinessRefBiz questionBusinessRefBiz;

    @Override
    public Integer getBusinessType() {
        return BusinessTypeEnum.EVALUATION_BUSINESS.getCode();
    }


    @Override
    public List<PaperQuestionDTO> getPaperQuestions(QueryPaperQuestionsBO queryBO) throws BusinessException {
        super.validateQueryParams(queryBO);
        List<QuestionBusinessRef> questionBusinessRefList = questionBusinessRefBiz.lambdaQuery().orderByDesc(QuestionBusinessRef::getOrderNum).orderByAsc(QuestionBusinessRef::getId).eq(QuestionBusinessRef::getBusinessId, queryBO.getBusinessId()).eq(QuestionBusinessRef::getBusinessType, queryBO.getBusinessType()).list();
        if (CollectionUtils.isEmpty(questionBusinessRefList)){
            throw new BusinessException(AppErrorCode.APP_QUESTION_SETTING_NOT_EXIST_CODE, AppErrorCode.APP_QUESTION_SETTING_NOT_EXIST_MSG);
        }
        Set<Integer> questionIdSet = questionBusinessRefList.stream().map(QuestionBusinessRef::getQuestionId).collect(Collectors.toSet());
        Map<Integer, Integer> questionOrderMap = new HashMap<>(questionIdSet.size());
        for (int i = 0; i < questionBusinessRefList.size(); i++) {
            questionOrderMap.put(questionBusinessRefList.get(i).getQuestionId(), i);
        }
        List<Question> questions = questionBiz.listByIds(questionIdSet);
        if (CollectionUtils.isEmpty(questions)){
            throw new BusinessException(AppErrorCode.APP_QUESTION_SETTING_NOT_EXIST_CODE, AppErrorCode.APP_QUESTION_SETTING_NOT_EXIST_MSG);
        }
        Map<Integer, Question> questionMap = questions.stream().collect(Collectors.toMap(Question::getId, Function.identity()));

     // 按原始关联顺序构建结果集
        List<Question> sortQuestion = questionBusinessRefList.stream()
                .map(ref -> questionMap.get(ref.getQuestionId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return buildPaperQuestionDTOs(sortQuestion, questionOrderMap);
    }



    @Override
    public void saveEvaluation(SubmitPaperBO submitBO, Integer paperId) throws BusinessException {
        //如果是评测模块的考试，还需要新增评测报告和每个评测项的评测记录
        if (submitBO.getBusinessId() == null){
            throw new BusinessException(APP_EVALUATION_NODE_NO_ID_CODE,APP_EVALUATION_NODE_NO_ID_MSG);
        }
        AppEvaluationNode node = appEvaluationNodeBiz.getById(submitBO.getBusinessId());
        if (ObjectUtils.isEmpty(node)){
            throw new BusinessException(APP_EVALUATION_NODE_NO_ID_CODE,APP_EVALUATION_NODE_NO_ID_MSG);
        }
        //查询答题用户信息
        UserIdApiBO userIdApiBO = new UserIdApiBO();
        userIdApiBO.setUserId(submitBO.getAppUserId());
        CurrentUserApiDTO currentUser = sysAppUserRemoteService.getCurrentUser(userIdApiBO);

        //1. 新增评测报告并返回新增结果,如果评测表存在该用户的未生成的报告，则返回最近的一条报告记录
        AppUserPaperEvaluationSaveBO evaluationSaveBO = new AppUserPaperEvaluationSaveBO();
        evaluationSaveBO.setAppUserId(submitBO.getAppUserId());
        evaluationSaveBO.setAppEvaluationId(submitBO.getAppEvaluationId());
        evaluationSaveBO.setGradeName(currentUser.getGradeName());
        evaluationSaveBO.setGradeId(currentUser.getGradeId());
        evaluationSaveBO.setRegion(currentUser.getIpCity());
        AppUserPaperEvaluationSaveDTO saveEvaluationReport = appUserPaperEvaluationBiz.saveEvaluationReport(evaluationSaveBO);


        AppUserPaperEvaluationAnalysis saveOrUpdateAnalysis = new AppUserPaperEvaluationAnalysis();
        //查询是否存在该评测表下评测项的评测报告分析，如果存在则修改正确错误题目数量,否则新增
        List<AppUserPaperEvaluationAnalysis> analysisList = appUserPaperEvaluationAnalysisBiz.lambdaQuery()
                .orderByDesc(AppUserPaperEvaluationAnalysis::getCreateTime)
                .eq(AppUserPaperEvaluationAnalysis::getAppUserPaperInfoId,paperId)
                .eq(AppUserPaperEvaluationAnalysis::getAppEvaluationNodeId, submitBO.getBusinessId())
                .eq(AppUserPaperEvaluationAnalysis::getAppUserPaperEvaluationId, saveEvaluationReport.getId())
                .list();
        if (!CollectionUtils.isEmpty(analysisList)){
            saveOrUpdateAnalysis = analysisList.get(0);
            saveOrUpdateAnalysis.setUpdateTime(LocalDateTime.now());
        }else{
            saveOrUpdateAnalysis.setAppUserPaperEvaluationId(saveEvaluationReport.getId());
            saveOrUpdateAnalysis.setAppEvaluationNodeId(submitBO.getBusinessId());
            saveOrUpdateAnalysis.setAppEvaluationNodeName(node.getAppEvaluationNodeName());
            saveOrUpdateAnalysis.setAppUserPaperInfoId(paperId);
        }

        //解析答题结果，正确几题，错误几题
        List<SubmitQuestionResultBO> questionResults = submitBO.getQuestionResults();
        List<SubmitBlankResultBO> blankResults = submitBO.getBlankResults();

        long questionCorrect = questionResults.stream().filter(d -> d.getResult().equals(ANSWER_CORRECT.getValue())).count();
        Map<Integer, List<SubmitBlankResultBO>> blankResultMap = blankResults.stream().collect(Collectors.groupingBy(SubmitBlankResultBO::getQuestionId));
        int blankCorrect = 0;
        int blankWrong = 0;
        for (Integer questionId : blankResultMap.keySet()) {
            List<SubmitBlankResultBO> submitBlankResultBOS = blankResultMap.get(questionId);
            long correctCount = submitBlankResultBOS.stream().filter(d -> d.getResult().equals(ANSWER_CORRECT.getValue())).count();
            if (correctCount == submitBlankResultBOS.size()) {
                blankCorrect++;
            }else{
                blankWrong++;
            }
        }
        long questionWrong = questionResults.stream().filter(d -> d.getResult().equals(ANSWER_WRONG.getValue())).count();
        int rightCount = (int)questionCorrect+blankCorrect;
        int wrongCount = (int)questionWrong+blankWrong;
        saveOrUpdateAnalysis.setRightCount(rightCount);
        saveOrUpdateAnalysis.setErrorCount(wrongCount);
        saveOrUpdateAnalysis.setTotalCount(rightCount+wrongCount);

        boolean saveOrUpdateFlag = appUserPaperEvaluationAnalysisBiz.saveOrUpdate(saveOrUpdateAnalysis);
        if (!saveOrUpdateFlag){
            throw new BusinessException(APP_EVALUATION_ANALYSIS_SAVE_FAIL_CODE,APP_EVALUATION_ANALYSIS_SAVE_FAIL_MSG);
        }
    }


    @Override
    protected List<Question> getQuestionsByBusinessId(Integer businessId, Integer businessType) throws BusinessException {
        if (businessId == null) {
            throw new BusinessException(APP_BUSINESS_ID_NOT_NULL_CODE,APP_BUSINESS_ID_NOT_NULL_MSG);
        }
        if (businessType == null) {
            throw new BusinessException(APP_BUSINESS_TYPE_NOT_NULL_CODE,APP_BUSINESS_TYPE_NOT_NULL_MSG);
        }

        try {
            List<QuestionBusinessRef> questionBusinessRefList = questionBusinessRefBiz.lambdaQuery().orderByAsc(QuestionBusinessRef::getOrderNum)
                    .eq(QuestionBusinessRef::getBusinessId,businessId)
                    .eq(QuestionBusinessRef::getBusinessType, businessType).list();
            if (CollectionUtils.isEmpty(questionBusinessRefList)){
                return Collections.emptyList();
            }
            Set<Integer> questionIdSet = questionBusinessRefList.stream().map(QuestionBusinessRef::getQuestionId).collect(Collectors.toSet());
            //查询所有题目信息
            List<Question> allQuestions = questionBiz.listByIds(questionIdSet);
            if (CollectionUtils.isEmpty(allQuestions)) {
                return Collections.emptyList();
            }
            allQuestions.sort((q1, q2) -> {
                int type1Order = QuestionTypeEnum.getOrderByValue(q1.getType());
                int type2Order = QuestionTypeEnum.getOrderByValue(q2.getType());
                return Integer.compare(type1Order, type2Order);
            });
            return allQuestions;
        }catch (Exception e) {
            log.error("根据业务信息获取题目失败", e);
            throw new BusinessException(APP_GET_QUESTION_FAIL_CODE,APP_GET_QUESTION_FAIL_MSG);
        }
    }

} 