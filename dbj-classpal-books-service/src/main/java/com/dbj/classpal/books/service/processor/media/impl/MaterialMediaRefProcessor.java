package com.dbj.classpal.books.service.processor.media.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dbj.classpal.books.client.enums.BusinessTypeEnum;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingHotspotMediaBO;
import com.dbj.classpal.books.common.dto.material.AppMaterialBusinessRefDTO;
import com.dbj.classpal.books.common.dto.pointreading.PointReadingHotspotMediaDTO;
import com.dbj.classpal.books.common.enums.MediaSourceEnum;
import com.dbj.classpal.books.common.enums.PointReadingMediaTypeEnum;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBusinessRefBiz;
import com.dbj.classpal.books.service.entity.material.AppMaterial;
import com.dbj.classpal.books.service.entity.material.AppMaterialBusinessRef;
import com.dbj.classpal.books.service.mapper.material.AppMaterialMapper;
import com.dbj.classpal.books.service.processor.media.MediaRefProcessor;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 素材中心媒体引用处理器
 * MediaSourceEnum = MATERIAL_CENTER
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@Component
public class MaterialMediaRefProcessor implements MediaRefProcessor {

    @Resource
    private IAppMaterialBusinessRefBiz appMaterialBusinessRefBiz;

    @Resource
    private AppMaterialMapper appMaterialMapper;

    @Override
    public MediaSourceEnum getSupportedMediaSource() {
        return MediaSourceEnum.MATERIAL_CENTER;
    }

    @Override
    public void saveMediaRefs(Integer hotspotId, List<PointReadingHotspotMediaBO> mediaList) throws Exception {
        log.info("保存素材中心媒体引用，hotspotId：{}，数量：{}", hotspotId, mediaList.size());
        
        // 先删除现有关联
        deleteMediaRefs(hotspotId);
        
        // 转换并保存新关联
        List<AppMaterialBusinessRef> refList = new ArrayList<>();
        for (PointReadingHotspotMediaBO media : mediaList) {
            AppMaterialBusinessRef ref = new AppMaterialBusinessRef();
            ref.setAppMaterialId(Integer.valueOf(media.getMediaId()));
            ref.setBusinessId(hotspotId);
            ref.setBusinessType(BusinessTypeEnum.POINT_READING_HOTSPOT_MEDIA_BUSINESS.getCode());
            ref.setBusinessName(BusinessTypeEnum.POINT_READING_HOTSPOT_MEDIA_BUSINESS.getName());
            ref.setOrderNum(media.getSortNum());
            refList.add(ref);
        }
        
        if (CollUtil.isNotEmpty(refList)) {
            appMaterialBusinessRefBiz.saveBatch(refList);
        }
        
        log.debug("素材中心媒体引用保存完成，hotspotId：{}", hotspotId);
    }

    @Override
    public List<PointReadingHotspotMediaDTO> getMediaRefs(Integer hotspotId) throws Exception {
        log.debug("查询素材中心媒体引用，hotspotId：{}", hotspotId);
        
        List<AppMaterialBusinessRefDTO> refList = appMaterialBusinessRefBiz.getBusinessList(
                List.of(hotspotId), 
                BusinessTypeEnum.POINT_READING_HOTSPOT_MEDIA_BUSINESS.getCode(),
                null // 查询所有类型的素材
        );
        
        List<PointReadingHotspotMediaDTO> mediaList = new ArrayList<>();
        if (CollUtil.isNotEmpty(refList)) {
            for (AppMaterialBusinessRefDTO ref : refList) {
                PointReadingHotspotMediaDTO media = new PointReadingHotspotMediaDTO();
                media.setMediaSource(MediaSourceEnum.MATERIAL_CENTER.getCode());
                media.setMediaSourceDesc(MediaSourceEnum.MATERIAL_CENTER.getDescription());
                media.setMediaId(ref.getAppMaterialId().toString());

                // 查询素材中心原始文件信息作为实时数据
                AppMaterial material = appMaterialMapper.selectById(ref.getAppMaterialId());
                if (material != null) {
                    media.setCurrentMediaUrl(material.getMaterialPath());
                    media.setCurrentMediaName(material.getMaterialName());
                    media.setMediaType(PointReadingMediaTypeEnum.AUDIO.getCode());
                }

                media.setMediaTypeDesc(PointReadingMediaTypeEnum.AUDIO.getDesc());
                mediaList.add(media);
            }
        }
        
        log.debug("素材中心媒体引用查询完成，hotspotId：{}，数量：{}", hotspotId, mediaList.size());
        return mediaList;
    }

    @Override
    public void deleteMediaRefs(Integer hotspotId) throws Exception {
        log.info("删除素材中心媒体引用，hotspotId：{}", hotspotId);
        
        LambdaQueryWrapper<AppMaterialBusinessRef> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppMaterialBusinessRef::getBusinessId, hotspotId)
                .eq(AppMaterialBusinessRef::getBusinessType, BusinessTypeEnum.POINT_READING_HOTSPOT_MEDIA_BUSINESS.getCode());
        
        appMaterialBusinessRefBiz.remove(wrapper);
        
        log.debug("素材中心媒体引用删除完成，hotspotId：{}", hotspotId);
    }

    @Override
    public void batchDeleteMediaRefs(List<Integer> hotspotIds) throws Exception {
        log.info("批量删除素材中心媒体引用，hotspotIds：{}", hotspotIds);
        
        if (CollUtil.isNotEmpty(hotspotIds)) {
            LambdaQueryWrapper<AppMaterialBusinessRef> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(AppMaterialBusinessRef::getBusinessId, hotspotIds)
                    .eq(AppMaterialBusinessRef::getBusinessType, BusinessTypeEnum.POINT_READING_HOTSPOT_MEDIA_BUSINESS.getCode());
            
            appMaterialBusinessRefBiz.remove(wrapper);
        }
        
        log.debug("素材中心媒体引用批量删除完成，hotspotIds：{}", hotspotIds);
    }

    @Override
    public int getPriority() {
        return 20;
    }
}
