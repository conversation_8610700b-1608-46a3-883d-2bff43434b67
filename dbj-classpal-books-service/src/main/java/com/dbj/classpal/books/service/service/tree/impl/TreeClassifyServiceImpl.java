package com.dbj.classpal.books.service.service.tree.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.tree.TreeClassifyAddBO;
import com.dbj.classpal.books.client.bo.tree.TreeClassifyEditBO;
import com.dbj.classpal.books.client.bo.tree.TreeClassifyListBO;
import com.dbj.classpal.books.client.bo.tree.TreeClassifyMoveBO;
import com.dbj.classpal.books.client.dto.tree.TreeClassifyDTO;
import com.dbj.classpal.books.common.enums.tree.TreeClassifyEnum;
import com.dbj.classpal.books.service.biz.tree.ITreeClassifyBiz;
import com.dbj.classpal.books.service.entity.tree.TreeClassify;
import com.dbj.classpal.books.service.mapper.tree.TreeClassifyMapper;
import com.dbj.classpal.books.service.service.tree.ITreeClassifyService;
import com.dbj.classpal.books.service.strategy.tree.ITreeClassifyRefStrategy;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.utils.util.SpringUtils;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Deque;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.TREE_CLASSIFY_VERIFICATION_FAIL_CODE;


/**
 * <p>
 * 树形目录 业务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Service
public class TreeClassifyServiceImpl extends ServiceImpl<TreeClassifyMapper, TreeClassify> implements ITreeClassifyService {

    @Resource
    private ITreeClassifyBiz treeClassifyBiz;

    @Override
    public List<TreeClassifyDTO> getTree(TreeClassifyListBO bo) {
        //1. 查询对应业务所有菜单
        List<TreeClassify> classifyList = treeClassifyBiz.lambdaQuery()
                .eq(TreeClassify::getType, bo.getType())
                .eq(StringUtils.isNotBlank(bo.getName()), TreeClassify::getName, bo.getName())
                .orderByAsc(TreeClassify::getSort)
                .list();
        //2. 转换为 DTO 列表
        List<TreeClassifyDTO> classifyDTOs = BeanUtil.copyToList(classifyList, TreeClassifyDTO.class);
        //3. 设置单个菜单的业务数据关联数
        Set<Integer> classifyIds = classifyList.stream().map(TreeClassify::getId).collect(Collectors.toSet());
        Map<Integer, Long> refNumMap = getRefNumMap(classifyIds, bo.getType());
        for (TreeClassifyDTO menuDTO : classifyDTOs) {
            menuDTO.setRefNum(refNumMap.getOrDefault(menuDTO.getId(), 0L));
        }
        //4. 构建树形结构
        List<TreeClassifyDTO> tree = buildTree(classifyDTOs);
        return tree;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean addNode(TreeClassifyAddBO bo) throws BusinessException {
        //1. 查询父节点是否存在
        TreeClassify parentNode = treeClassifyBiz.getById(bo.getParentId());
        Assert.notNull(parentNode, () -> new BusinessException(TREE_CLASSIFY_VERIFICATION_FAIL_CODE, "父节点不存在"));
        //2. 判断深度
        Assert.isTrue(parentNode.getLevel() < 5, () -> new BusinessException(TREE_CLASSIFY_VERIFICATION_FAIL_CODE, "不能添加超过5级的节点"));
        //3. 获取同级最大 sort 值并 +1
        TreeClassify treeClassify = treeClassifyBiz.lambdaQuery()
                .eq(TreeClassify::getParentId, parentNode.getId())
                .orderByAsc(TreeClassify::getSort).list()
                .stream().findFirst().orElse(null);
        //4. 构造新节点并保存
        TreeClassify newChild = BeanUtil.copyProperties(bo, TreeClassify.class);
        newChild.setLevel(parentNode.getLevel() + 1);
        newChild.setType(parentNode.getType());
        newChild.setSort(treeClassify == null ? 1 : treeClassify.getSort() + 1);
        return treeClassifyBiz.save(newChild);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean renameNode(TreeClassifyEditBO bo) throws BusinessException {
        //1. 查询父节点是否存在
        TreeClassify currentNode = treeClassifyBiz.getById(bo.getId());
        Assert.notNull(currentNode, () -> new BusinessException(TREE_CLASSIFY_VERIFICATION_FAIL_CODE, "节点不存在"));
        Assert.isTrue(currentNode.getParentId() != 0, () -> new BusinessException(TREE_CLASSIFY_VERIFICATION_FAIL_CODE, "根节点不能重命名"));
        return treeClassifyBiz.updateById(BeanUtil.copyProperties(bo, TreeClassify.class));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteNode(CommonIdApiBO bo) throws BusinessException {
        TreeClassify treeClassify = treeClassifyBiz.getById(bo.getId());
        if (treeClassify == null) {
            return true;
        }
        Assert.isTrue(treeClassify.getParentId() != 0, () -> new BusinessException(TREE_CLASSIFY_VERIFICATION_FAIL_CODE, "根节点不能删除"));
        Map<Integer, Long> refNumMap = getRefNumMap(Collections.singleton(bo.getId()), treeClassify.getType());
        Assert.isTrue(refNumMap.getOrDefault(bo.getId(), 0L) <= 0,
                () -> new BusinessException(TREE_CLASSIFY_VERIFICATION_FAIL_CODE, "该节点下有数据，请先删除数据"));
        return treeClassifyBiz.removeById(bo.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean moveNode(TreeClassifyMoveBO bo) throws BusinessException {
        //1. 查询要移动的节点
        TreeClassify movingNode = treeClassifyBiz.getById(bo.getId());
        Assert.notNull(movingNode, () -> new BusinessException(TREE_CLASSIFY_VERIFICATION_FAIL_CODE, "节点不存在"));

        //2. 查询目标节点
        TreeClassify targetNode = treeClassifyBiz.getById(bo.getTargetId());
        Assert.notNull(targetNode, () -> new BusinessException(TREE_CLASSIFY_VERIFICATION_FAIL_CODE, "目标节点不存在"));

        //3. ====== 判断是排序、插入子节点还是跨层移动 ======
        if (bo.getMoveToBefore() == null) {
            //作为子节点插入
            return handleAsChildNode(movingNode, targetNode);
        } else {
            if (movingNode.getParentId() != null && movingNode.getParentId().equals(bo.getTargetId())) {
                //同级排序
                return updateNodeSortWithinSameLevel(movingNode, bo.getTargetId(), bo.getMoveToBefore());
            } else {
                //跨层级移动
                List<TreeClassify> allSubNodes = getAllSubNodes(movingNode.getId());
                //跨层级移动 → 插入到 targetNode 的同级中
                moveToSameLevelAsTarget(movingNode, targetNode, allSubNodes, bo.getMoveToBefore());
            }
        }
        return true;
    }

    private static Map<Integer, Long> getRefNumMap(Collection<Integer> classifyIds, Integer businessType) {
        TreeClassifyEnum classifyEnum = TreeClassifyEnum.getByType(businessType);
        ITreeClassifyRefStrategy strategy = SpringUtils.getBean(classifyEnum.getStrategy());
        return strategy.getRefNumMap(classifyIds);
    }

    private Boolean updateNodeSortWithinSameLevel(TreeClassify movingNode, Integer targetId, Boolean moveToBefore) {
        Integer parentId = movingNode.getParentId();
        //获取同级节点
        List<TreeClassify> siblings = treeClassifyBiz.list(Wrappers.<TreeClassify>lambdaQuery()
                .eq(TreeClassify::getParentId, parentId)
                .orderByAsc(TreeClassify::getSort));
        List<Integer> ids = siblings.stream().map(TreeClassify::getId).toList();
        int sourceIndex = ids.indexOf(movingNode.getId());
        int targetIndex = ids.indexOf(targetId);
        //源节点不存在或目标节点不存在或源节点和目标节点相同
        if (sourceIndex == -1 || targetIndex == -1 || sourceIndex == targetIndex) {
            return false;
        }

        //移除源节点
        ids.remove(sourceIndex);

        //插入到目标位置
        int insertPos = moveToBefore != null && moveToBefore ? targetIndex : targetIndex + 1;
        ids.add(insertPos, movingNode.getId());

        //更新排序值
        for (int i = 0; i < ids.size(); i++) {
            TreeClassify node = new TreeClassify();
            node.setId(ids.get(i));
            //从1开始计数
            node.setSort(i + 1);
            treeClassifyBiz.updateById(node);
        }

        return true;
    }

    private Boolean handleAsChildNode(TreeClassify movingNode, TreeClassify targetNode) throws BusinessException {
        //判断层级是否超限
        Assert.isTrue(targetNode.getLevel() < 5,
                () -> new BusinessException(TREE_CLASSIFY_VERIFICATION_FAIL_CODE, "不能添加超过5级的节点"));

        //获取当前父节点下的最大 sort 值
        Integer maxSort = treeClassifyBiz.list(Wrappers.<TreeClassify>lambdaQuery()
                        .eq(TreeClassify::getParentId, targetNode.getId())
                        .orderByDesc(TreeClassify::getSort))
                .stream()
                .findFirst()
                .map(TreeClassify::getSort)
                .orElse(0);

        //更新当前节点的父节点和排序
        movingNode.setParentId(targetNode.getId());
        movingNode.setLevel(targetNode.getLevel() + 1);
        movingNode.setSort(maxSort + 1);
        return treeClassifyBiz.updateById(movingNode);
    }

    /**
     * 将节点移动到目标节点的同级位置，并更新排序
     *
     * @param movingNode 要移动的节点
     * @param targetNode 目标节点
     * @param allSubNodes 所有子节点（包括自己）
     * @param moveToBefore 是否插入到目标前
     */
    private void moveToSameLevelAsTarget(TreeClassify movingNode,
                                         TreeClassify targetNode,
                                         List<TreeClassify> allSubNodes,
                                         boolean moveToBefore) throws BusinessException {
        Integer newParentId = targetNode.getParentId();
        Assert.notNull(newParentId, () -> new BusinessException(TREE_CLASSIFY_VERIFICATION_FAIL_CODE, "目标节点没有父节点"));

        //检查是否形成循环依赖（不能把节点移到自己的子树中）
        List<Integer> subTreeIds = treeClassifyBiz.getSubTreeIds(movingNode.getId(), false);
        Assert.isTrue(!subTreeIds.contains(targetNode.getId()),
                () -> new BusinessException(TREE_CLASSIFY_VERIFICATION_FAIL_CODE, "不能将节点移动到自己的子节点下"));

        //计算层级差值
        TreeClassify newParent = treeClassifyBiz.getById(newParentId);
        int levelDiff = newParent.getLevel() + 1 - movingNode.getLevel();

        //更新 movingNode 及其所有子节点的 parentId 和 level
        for (TreeClassify node : allSubNodes) {
            node.setParentId(newParentId);
            node.setLevel(node.getLevel() + levelDiff);
            treeClassifyBiz.updateById(node);
        }

        //获取当前同级节点并更新排序
        List<TreeClassify> siblings = treeClassifyBiz.list(Wrappers.<TreeClassify>lambdaQuery()
                .eq(TreeClassify::getParentId, newParentId)
                .orderByAsc(TreeClassify::getSort));

        List<Integer> ids = siblings.stream()
                .map(TreeClassify::getId)
                .collect(Collectors.toCollection(ArrayList::new));

        int sourceIndex = ids.indexOf(movingNode.getId());
        int targetIndex = ids.indexOf(targetNode.getId());

        if (sourceIndex != -1) {
            //先移除旧位置
            ids.remove(sourceIndex);
        } else {
            //如果不在当前列表中，说明是刚移动过来的节点
            ids.removeIf(id -> id.equals(movingNode.getId()));
            //加入末尾
            ids.add(movingNode.getId());
        }

        //插入到目标位置
        int insertPos = moveToBefore ? targetIndex : targetIndex + 1;
        ids.add(insertPos, movingNode.getId());

        //更新排序值
        for (int i = 0; i < ids.size(); i++) {
            TreeClassify node = new TreeClassify();
            node.setId(ids.get(i));
            //从1开始计数
            node.setSort(i + 1);
            treeClassifyBiz.updateById(node);
        }
    }


    private List<TreeClassifyDTO> buildTree(List<TreeClassifyDTO> allNodes) {
        //1. 使用 parentId 建立父子关系
        Map<Integer, TreeClassifyDTO> nodeMap = allNodes.stream()
                .collect(Collectors.toMap(TreeClassifyDTO::getId, n -> n));
        List<TreeClassifyDTO> rootNodes = new ArrayList<>();

        //2. 按 level 排序，保证父节点在子节点前处理
        List<TreeClassifyDTO> sortedNodes = allNodes.stream()
                .sorted(Comparator.comparingInt(TreeClassifyDTO::getLevel)).toList();
        //3. 构建根节点和完整路径
        for (TreeClassifyDTO node : sortedNodes) {
            Integer parentId = node.getParentId();
            if (parentId == null || parentId == 0) {
                //根节点路径为自己名称
                node.setFullPath(node.getName());
                //根节点
//                node.setLevel(1);
                rootNodes.add(node);
            } else {
                //找到父节点并添加子节点
                TreeClassifyDTO parentNode = nodeMap.get(parentId);
                if (parentNode != null) {
                    //拼接完整路径
                    node.setFullPath(parentNode.getFullPath() + "/" + node.getName());
                    if (parentNode.getChildren() == null) {
                        parentNode.setChildren(new ArrayList<>());
                    }
//                    node.setLevel(parentNode.getLevel() + 1);
                    parentNode.getChildren().add(node);
                }
            }
        }

        //2. 递归计算 refNum
        for (TreeClassifyDTO rootNode : rootNodes) {
            calculateRefNum(rootNode);
        }
        return rootNodes;
    }

    /**
     * 递归计算节点及其所有子节点的 refNum 总和
     *
     * @param node 当前节点
     * @return 返回当前节点的 refNum（包含所有子节点的 refNum）
     */
    private long calculateRefNum(TreeClassifyDTO node) {
        //初始值为自身refNum
        long totalRefNum = node.getRefNum();
        if (node.getChildren() != null && !node.getChildren().isEmpty()) {
            for (TreeClassifyDTO child : node.getChildren()) {
                //递归计算子节点
                totalRefNum += calculateRefNum(child);
            }
            //累加后更新父节点的refNum
            node.setRefNum(totalRefNum);
        }
        return node.getRefNum();
    }

    /**
     * 获取节点自身及所有子节点
     * @param nodeId 节点id
     * @return 节点自身及所有子节点
     */
    private List<TreeClassify> getAllSubNodes(Integer nodeId) {
        List<TreeClassify> result = new ArrayList<>();
        Deque<Integer> queue = new ArrayDeque<>();
        queue.offer(nodeId);

        while (!queue.isEmpty()) {
            Integer currentId = queue.poll();
            TreeClassify currentNode = treeClassifyBiz.getById(currentId);
            if (currentNode != null) {
                //包含当前节点本身
                result.add(currentNode);
                List<TreeClassify> children = treeClassifyBiz.list(Wrappers.<TreeClassify>lambdaQuery()
                        .eq(TreeClassify::getParentId, currentId));
                for (TreeClassify child : children) {
                    queue.offer(child.getId());
                }
            }
        }

        return result;
    }
}
