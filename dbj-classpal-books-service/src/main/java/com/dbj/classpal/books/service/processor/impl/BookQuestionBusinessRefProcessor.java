package com.dbj.classpal.books.service.processor.impl;

import com.dbj.classpal.books.client.enums.BusinessTypeEnum;
import com.dbj.classpal.books.common.dto.question.QuestionCategoryRefDTO;
import com.dbj.classpal.books.service.biz.books.IBooksInfoBiz;
import com.dbj.classpal.books.service.biz.books.IBooksRankInCodesContentsBiz;
import com.dbj.classpal.books.service.entity.books.BooksInfo;
import com.dbj.classpal.books.service.entity.books.BooksRankInCodesContents;
import com.dbj.classpal.books.service.entity.question.QuestionCategoryBusinessRef;
import com.dbj.classpal.books.service.processor.BusinessRefProcessor;
import jakarta.annotation.Resource;
import java.math.BigInteger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * 图书管理-题库业务引用处理器
 * BusinessTypeEnum = 4
 */
@Slf4j
@Component
public class BookQuestionBusinessRefProcessor implements BusinessRefProcessor {

    @Resource
    private IBooksRankInCodesContentsBiz booksRankInCodesContentsBiz;

    @Resource
    private IBooksInfoBiz booksInfoBiz;

    @Override
    public BusinessTypeEnum getSupportedBusinessType() {
        return BusinessTypeEnum.QUESTION_BUSINESS;
    }

    @Override
    public void processBusinessInfo(QuestionCategoryRefDTO dto, QuestionCategoryBusinessRef businessRef) throws Exception {
        log.info("处理图书管理-题库业务引用, businessId: {}", businessRef.getBusinessId());
        
        BooksRankInCodesContents codesContents = booksRankInCodesContentsBiz.getById(businessRef.getBusinessId());
        if (codesContents == null) {
            log.warn("书内码信息不存在, businessId: {}", businessRef.getBusinessId());
            dto.setBusinessName(null);
            return;
        }

        BooksInfo booksInfo = booksInfoBiz.getById(codesContents.getBooksId());
        if (booksInfo == null) {
            log.warn("图书信息不存在, booksId: {}", codesContents.getBooksId());
            dto.setBusinessName(null);
            return;
        }

        dto.setBusinessName(booksInfo.getBookName());
        dto.setBooksId(codesContents.getBooksId());
        dto.setBookName(booksInfo.getBookName());
        dto.setRankId(codesContents.getRankId());
        dto.setContentsId(codesContents.getId());
        dto.setRankClassifyId(codesContents.getRankClassifyId());

        log.debug("成功处理图书管理-题库业务引用, bookName: {}", booksInfo.getBookName());
    }

    @Override
    public int getPriority() {
        return BigInteger.TEN.intValue();
    }
}
