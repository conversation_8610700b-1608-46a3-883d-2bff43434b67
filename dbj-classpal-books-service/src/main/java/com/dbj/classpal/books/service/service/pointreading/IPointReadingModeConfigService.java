package com.dbj.classpal.books.service.service.pointreading;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingModeConfigQueryBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingModeConfigSaveBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingModeConfigUpdateBO;
import com.dbj.classpal.books.common.dto.pointreading.PointReadingModeConfigDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;

import com.dbj.classpal.framework.commons.request.PageInfo;
import java.util.List;

/**
 * 点读书模式配置 服务接口
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface IPointReadingModeConfigService {

    /**
     * 分页查询点读书模式配置
     *
     * @param pageInfo 分页参数
     * @return 分页结果
     */
    Page<PointReadingModeConfigDTO> pageModeConfig(PageInfo<PointReadingModeConfigQueryBO> pageInfo) throws BusinessException;

    /**
     * 查询模式配置详情
     *
     * @param id 配置ID
     * @return 配置详情
     */
    PointReadingModeConfigDTO detail(Integer id) throws BusinessException;

    /**
     * 保存点读书模式配置
     *
     * @param saveBO 保存参数
     * @return 配置ID
     */
    Integer save(PointReadingModeConfigSaveBO saveBO) throws BusinessException;

    /**
     * 更新点读书模式配置
     *
     * @param updateBO 更新参数
     * @return 是否成功
     */
    Boolean update(PointReadingModeConfigUpdateBO updateBO) throws BusinessException;

    /**
     * 删除点读书模式配置
     *
     * @param id 配置ID
     * @return 是否成功
     */
    Boolean delete(Integer id) throws BusinessException;

    /**
     * 批量删除点读书模式配置
     *
     * @param ids 配置ID列表
     * @return 是否成功
     */
    Boolean deleteBatch(List<Integer> ids) throws BusinessException;

    /**
     * 查询点读书下的模式配置列表
     *
     * @param bookId 点读书ID
     * @return 配置列表
     */
    List<PointReadingModeConfigDTO> getModeConfigsByBook(Integer bookId) throws BusinessException;

    /**
     * 更新排序
     *
     * @param id 配置ID
     * @param sortNum 排序号
     * @return 是否成功
     */
    Boolean updateSort(Integer id, Integer sortNum) throws BusinessException;

    /**
     * 启用配置
     *
     * @param id 配置ID
     * @return 是否成功
     */
    Boolean enable(Integer id) throws BusinessException;

    /**
     * 禁用配置
     *
     * @param id 配置ID
     * @return 是否成功
     */
    Boolean disable(Integer id) throws BusinessException;

    /**
     * 批量启用配置
     *
     * @param ids 配置ID列表
     * @return 是否成功
     */
    Boolean enableBatch(List<Integer> ids) throws BusinessException;

    /**
     * 批量禁用配置
     *
     * @param ids 配置ID列表
     * @return 是否成功
     */
    Boolean disableBatch(List<Integer> ids) throws BusinessException;

    /**
     * 更新处理状态
     *
     * @param id 配置ID
     * @param processStatus 处理状态
     * @return 是否成功
     */
    Boolean updateProcessStatus(Integer id, Integer processStatus) throws BusinessException;
}
