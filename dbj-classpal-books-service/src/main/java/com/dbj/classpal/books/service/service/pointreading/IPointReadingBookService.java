package com.dbj.classpal.books.service.service.pointreading;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingBookCopyApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingBookMoveApiBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingBookQueryBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingBookSaveBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingBookUpdateBO;
import com.dbj.classpal.books.common.dto.pointreading.PointReadingBookDTO;
import com.dbj.classpal.books.service.entity.pointreading.PointReadingBook;
import com.dbj.classpal.framework.commons.exception.BusinessException;

import com.dbj.classpal.framework.commons.request.PageInfo;
import java.util.List;

/**
 * 点读书 服务接口
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
public interface IPointReadingBookService extends IService<PointReadingBook> {

    /**
     * 分页查询点读书列表
     *
     * @param pageRequest 分页查询参数
     * @return 点读书分页数据
     */
    Page<PointReadingBookDTO> page(PageInfo<PointReadingBookQueryBO> pageRequest) throws BusinessException;

    /**
     * 查询点读书详情
     *
     * @param id 点读书ID
     * @return 点读书详情数据
     */
    PointReadingBookDTO detail(Integer id) throws BusinessException;

    /**
     * 新增点读书
     *
     * @param saveBO 点读书保存参数
     * @return 新增点读书ID
     */
    Integer save(PointReadingBookSaveBO saveBO) throws BusinessException;

    /**
     * 更新点读书
     *
     * @param updateBO 点读书更新参数
     * @return 更新结果
     */
    boolean update(PointReadingBookUpdateBO updateBO) throws BusinessException;

    /**
     * 删除点读书
     *
     * @param id 点读书ID
     * @return 删除结果
     */
    boolean delete(Integer id) throws BusinessException;

    /**
     * 批量删除点读书
     *
     * @param ids ID列表
     * @return 批量删除结果
     */
    boolean deleteBatch(List<Integer> ids) throws BusinessException;

    /**
     * 批量启用点读书
     *
     * @param ids ID列表
     * @return 批量启用结果
     */
    boolean enableBatch(List<Integer> ids) throws BusinessException;

    /**
     * 批量禁用点读书
     *
     * @param ids ID列表
     * @return 批量禁用结果
     */
    boolean disableBatch(List<Integer> ids) throws BusinessException;

    /**
     * 批量上线点读书
     *
     * @param ids ID列表
     * @return 批量上线结果
     */
    boolean launchBatch(List<Integer> ids) throws BusinessException;

    /**
     * 批量下线点读书
     *
     * @param ids ID列表
     * @return 批量下线结果
     */
    boolean offlineBatch(List<Integer> ids) throws BusinessException;

    /**
     * 根据MD5查询点读书
     *
     * @param md5 文件MD5
     * @return 点读书信息
     */
    PointReadingBookDTO getByMd5(String md5) throws BusinessException;

    /**
     * 更新解析状态
     *
     * @param id 点读书ID
     * @param parseStatus 解析状态
     * @return 更新结果
     */
    boolean updateParseStatus(Integer id, Integer parseStatus) throws BusinessException;

    /**
     * 批量复制点读书
     *
     * @param copyBO 批量复制参数
     * @return 是否成功
     */
    Boolean copyBatch(PointReadingBookCopyApiBO copyBO) throws BusinessException;

    /**
     * 批量移动点读书
     *
     * @param moveBO 批量移动参数
     * @return 是否成功
     */
    Boolean moveBatch(PointReadingBookMoveApiBO moveBO) throws BusinessException;
}
