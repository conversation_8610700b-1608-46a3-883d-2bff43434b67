package com.dbj.classpal.books.service.biz.books;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.client.bo.books.BooksInfoPageBO;
import com.dbj.classpal.books.client.dto.books.BooksInfoPageApiDTO;
import com.dbj.classpal.books.service.entity.books.BooksInfo;
import com.dbj.classpal.framework.commons.request.PageInfo;

/**
 * <p>
 * 图书表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
public interface IBooksInfoBiz extends IService<BooksInfo> {

    /**
     * 分页查询
     * @param pageRequest
     * @return
     */
    Page<BooksInfoPageApiDTO> pageInfo(PageInfo<BooksInfoPageBO> pageRequest);
}
