package com.dbj.classpal.books.service.service.evaluation;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.evaluation.AdminEvaluationUpdateOpenApiBO;
import com.dbj.classpal.books.common.bo.common.CommonIdBO;
import com.dbj.classpal.books.common.bo.evaluation.AdminEvaluationQueryBO;
import com.dbj.classpal.books.common.dto.evaluation.AdminEvaluationDetailQueryDTO;
import com.dbj.classpal.books.common.dto.evaluation.AdminEvaluationQueryDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppEvaluationService
 * Date:     2025-05-16 14:59:38
 * Description: 表名： ,描述： 表
 */
@Mapper
public interface IAppEvaluationService {

    /**
     * 分页查询评测模板列表
     * @param pageRequest
     * @return
     */
    Page<AdminEvaluationQueryDTO> pageInfo(PageInfo<AdminEvaluationQueryBO> pageRequest);


    /**
     * 查询评测表详情
      * @param bo
     * @return
     */
    AdminEvaluationDetailQueryDTO getDetail(CommonIdBO bo);

    /**
     * 启用|禁用评测表
     * @param bo
     * @return
     */
    Boolean updateOpen(AdminEvaluationUpdateOpenApiBO bo) throws BusinessException;
}
