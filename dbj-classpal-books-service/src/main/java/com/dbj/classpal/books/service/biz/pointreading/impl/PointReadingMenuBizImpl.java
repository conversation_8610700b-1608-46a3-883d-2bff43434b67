package com.dbj.classpal.books.service.biz.pointreading.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.client.enums.StatusEnum;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingMenuQueryBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingMenuSaveBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingMenuUpdateBO;
import com.dbj.classpal.books.common.constant.AppErrorCode;
import com.dbj.classpal.books.common.dto.pointreading.PointReadingMenuDTO;
import com.dbj.classpal.books.service.biz.pointreading.IPointReadingBookBiz;
import com.dbj.classpal.books.service.biz.pointreading.IPointReadingMenuBiz;
import com.dbj.classpal.books.service.entity.pointreading.PointReadingBook;
import com.dbj.classpal.books.service.entity.pointreading.PointReadingMenu;
import com.dbj.classpal.books.service.mapper.pointreading.PointReadingMenuMapper;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 点读书目录 业务实现类
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
@Service
public class PointReadingMenuBizImpl extends ServiceImpl<PointReadingMenuMapper, PointReadingMenu> implements IPointReadingMenuBiz {

    /**
     * 默认父级目录ID
     */
    private static final Integer DEFAULT_PARENT_ID = 0;

    @Resource
    private IPointReadingBookBiz pointReadingBookBiz;

    @Override
    public Page<PointReadingMenuDTO> pageMenu(PageInfo<PointReadingMenuQueryBO> pageInfo) throws BusinessException {

        if (pageInfo.getData() == null) {
            pageInfo.setData(new PointReadingMenuQueryBO());
        }

        Page<PointReadingMenu> menuPage = this.getBaseMapper().selectPage(pageInfo.getPage(), pageInfo.getData());

        Page<PointReadingMenuDTO> dtoPage = new Page<>();
        BeanUtil.copyProperties(menuPage, dtoPage);
        
        List<PointReadingMenuDTO> dtoList = new ArrayList<>();
        if (CollUtil.isNotEmpty(menuPage.getRecords())) {
            // 获取点读书信息
            List<Integer> bookIds = menuPage.getRecords().stream()
                    .map(PointReadingMenu::getBookId)
                    .distinct()
                    .collect(Collectors.toList());
            
            Map<Integer, PointReadingBook> bookMap = pointReadingBookBiz.listByIds(bookIds)
                    .stream()
                    .collect(Collectors.toMap(PointReadingBook::getId, book -> book));
            
            for (PointReadingMenu menu : menuPage.getRecords()) {
                PointReadingMenuDTO dto = convertToDTO(menu);
                
                // 设置点读书名称
                PointReadingBook book = bookMap.get(menu.getBookId());
                if (book != null) {
                    dto.setBookName(book.getName());
                }
                
                dtoList.add(dto);
            }
        }
        
        dtoPage.setRecords(dtoList);
        return dtoPage;
    }

    @Override
    public PointReadingMenuDTO detail(Integer id) throws BusinessException {
        PointReadingMenu menu = this.getById(id);
        if (menu == null) {
            throw new BusinessException(AppErrorCode.POINT_READING_MENU_NOT_EXIST_CODE, AppErrorCode.POINT_READING_MENU_NOT_EXIST_MSG);
        }
        
        PointReadingMenuDTO dto = convertToDTO(menu);
        
        // 设置点读书名称
        PointReadingBook book = pointReadingBookBiz.getById(menu.getBookId());
        if (book != null) {
            dto.setBookName(book.getName());
        }
        
        // 设置父级目录名称
        if (menu.getParentId() != null && !menu.getParentId().equals(DEFAULT_PARENT_ID)) {
            PointReadingMenu parentMenu = this.getById(menu.getParentId());
            if (parentMenu != null) {
                dto.setParentName(parentMenu.getName());
            }
        }
        
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer saveMenu(PointReadingMenuSaveBO saveBO) throws BusinessException {
        // 验证点读书是否存在
        PointReadingBook book = pointReadingBookBiz.getById(saveBO.getBookId());
        if (book == null) {
            throw new BusinessException(AppErrorCode.POINT_READING_BOOK_NOT_EXIST_CODE, AppErrorCode.POINT_READING_BOOK_NOT_EXIST_MSG);
        }
        
        // 验证父级目录是否存在
        if (saveBO.getParentId() != null && !saveBO.getParentId().equals(DEFAULT_PARENT_ID)) {
            PointReadingMenu parentMenu = this.getById(saveBO.getParentId());
            if (parentMenu == null) {
                throw new BusinessException(AppErrorCode.POINT_READING_MENU_PARENT_NOT_EXIST_CODE, AppErrorCode.POINT_READING_MENU_PARENT_NOT_EXIST_MSG);
            }
            
            // 验证父级目录是否属于同一本点读书
            if (!parentMenu.getBookId().equals(saveBO.getBookId())) {
                throw new BusinessException(AppErrorCode.POINT_READING_MENU_PARENT_BOOK_MISMATCH_CODE, AppErrorCode.POINT_READING_MENU_PARENT_BOOK_MISMATCH_MSG);
            }
        }
        
        // 验证同级目录名称是否重复
        LambdaQueryWrapper<PointReadingMenu> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PointReadingMenu::getBookId, saveBO.getBookId())
                .eq(PointReadingMenu::getName, saveBO.getName())
                .eq(PointReadingMenu::getParentId, saveBO.getParentId() == null ? DEFAULT_PARENT_ID : saveBO.getParentId());
        
        if (this.count(wrapper) > 0) {
            throw new BusinessException(AppErrorCode.POINT_READING_MENU_NAME_EXIST_CODE, AppErrorCode.POINT_READING_MENU_NAME_EXIST_MSG);
        }
        
        // 构建实体对象
        PointReadingMenu menu = new PointReadingMenu();
        BeanUtil.copyProperties(saveBO, menu);
        
        // 设置默认值
        if (menu.getParentId() == null) {
            menu.setParentId(DEFAULT_PARENT_ID);
        }
        if (menu.getLevel() == null) {
            menu.setLevel(calculateLevel(menu.getParentId()));
        }
        if (menu.getSortNum() == null) {
            menu.setSortNum(getNextSortNum(saveBO.getBookId(), menu.getParentId()));
        }
        if (menu.getStatus() == null) {
            menu.setStatus(YesOrNoEnum.YES.getCode());
        }
        
        // 保存目录
        this.save(menu);
        
        return menu.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMenu(PointReadingMenuUpdateBO updateBO) throws BusinessException {
        // 验证目录是否存在
        PointReadingMenu existMenu = this.getById(updateBO.getId());
        if (existMenu == null) {
            throw new BusinessException(AppErrorCode.POINT_READING_MENU_NOT_EXIST_CODE, AppErrorCode.POINT_READING_MENU_NOT_EXIST_MSG);
        }
        
        // 验证点读书是否存在
        PointReadingBook book = pointReadingBookBiz.getById(updateBO.getBookId());
        if (book == null) {
            throw new BusinessException(AppErrorCode.POINT_READING_BOOK_NOT_EXIST_CODE, AppErrorCode.POINT_READING_BOOK_NOT_EXIST_MSG);
        }
        
        // 验证父级目录是否存在
        if (updateBO.getParentId() != null && !updateBO.getParentId().equals(DEFAULT_PARENT_ID)) {
            PointReadingMenu parentMenu = this.getById(updateBO.getParentId());
            if (parentMenu == null) {
                throw new BusinessException(AppErrorCode.POINT_READING_MENU_PARENT_NOT_EXIST_CODE, AppErrorCode.POINT_READING_MENU_PARENT_NOT_EXIST_MSG);
            }
            
            // 验证父级目录是否属于同一本点读书
            if (!parentMenu.getBookId().equals(updateBO.getBookId())) {
                throw new BusinessException(AppErrorCode.POINT_READING_MENU_PARENT_BOOK_MISMATCH_CODE, AppErrorCode.POINT_READING_MENU_PARENT_BOOK_MISMATCH_MSG);
            }
            
            // 验证不能设置自己为父级目录
            if (parentMenu.getId().equals(updateBO.getId())) {
                throw new BusinessException(AppErrorCode.POINT_READING_MENU_SELF_PARENT_CODE, AppErrorCode.POINT_READING_MENU_SELF_PARENT_MSG);
            }
        }
        
        // 验证同级目录名称是否重复
        LambdaQueryWrapper<PointReadingMenu> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PointReadingMenu::getBookId, updateBO.getBookId())
                .eq(PointReadingMenu::getName, updateBO.getName())
                .eq(PointReadingMenu::getParentId, updateBO.getParentId() == null ? DEFAULT_PARENT_ID : updateBO.getParentId())
                .ne(PointReadingMenu::getId, updateBO.getId());
        
        if (this.count(wrapper) > 0) {
            throw new BusinessException(AppErrorCode.POINT_READING_MENU_NAME_EXIST_CODE, AppErrorCode.POINT_READING_MENU_NAME_EXIST_MSG);
        }
        
        // 构建更新对象
        PointReadingMenu menu = new PointReadingMenu();
        BeanUtil.copyProperties(updateBO, menu);
        
        // 设置默认值
        if (menu.getParentId() == null) {
            menu.setParentId(DEFAULT_PARENT_ID);
        }
        if (menu.getLevel() == null) {
            menu.setLevel(calculateLevel(menu.getParentId()));
        }
        
        return this.updateById(menu);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMenu(Integer id) throws BusinessException {
        // 验证目录是否存在
        PointReadingMenu menu = this.getById(id);
        if (menu == null) {
            throw new BusinessException(AppErrorCode.POINT_READING_MENU_NOT_EXIST_CODE, AppErrorCode.POINT_READING_MENU_NOT_EXIST_MSG);
        }
        
        // 检查是否有子目录
        int childrenCount = this.getBaseMapper().countChildren(id);
        if (childrenCount > 0) {
            throw new BusinessException(AppErrorCode.POINT_READING_MENU_HAS_CHILDREN_CODE, AppErrorCode.POINT_READING_MENU_HAS_CHILDREN_MSG);
        }
        
        return this.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBatch(List<Integer> ids) throws BusinessException {
        if (CollUtil.isEmpty(ids)) {
            return true;
        }
        
        // 验证是否有子目录
        for (Integer id : ids) {
            int childrenCount = this.getBaseMapper().countChildren(id);
            if (childrenCount > 0) {
                PointReadingMenu menu = this.getById(id);
                throw new BusinessException(AppErrorCode.POINT_READING_MENU_HAS_CHILDREN_CODE, 
                    "目录【" + (menu != null ? menu.getName() : id) + "】存在子目录，无法删除");
            }
        }
        
        return this.removeByIds(ids);
    }

    /**
     * 转换为DTO
     */
    private PointReadingMenuDTO convertToDTO(PointReadingMenu menu) {
        PointReadingMenuDTO dto = new PointReadingMenuDTO();
        BeanUtil.copyProperties(menu, dto);
        
        // 设置状态描述
        if (menu.getStatus() != null) {
            dto.setStatusDesc(StatusEnum.getDescByCode(menu.getStatus()));
        }
        
        return dto;
    }

    /**
     * 计算目录层级
     */
    private Integer calculateLevel(Integer parentId) {
        if (parentId == null || parentId.equals(DEFAULT_PARENT_ID)) {
            return 1;
        }
        
        PointReadingMenu parentMenu = this.getById(parentId);
        if (parentMenu == null) {
            return 1;
        }
        
        return parentMenu.getLevel() + 1;
    }

    /**
     * 获取下一个排序号
     */
    private Integer getNextSortNum(Integer bookId, Integer parentId) {
        LambdaQueryWrapper<PointReadingMenu> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PointReadingMenu::getBookId, bookId)
                .eq(PointReadingMenu::getParentId, parentId == null ? DEFAULT_PARENT_ID : parentId)
                .orderByDesc(PointReadingMenu::getSortNum)
                .last("LIMIT 1");
        
        PointReadingMenu lastMenu = this.getOne(wrapper);
        if (lastMenu == null || lastMenu.getSortNum() == null) {
            return 1;
        }
        
        return lastMenu.getSortNum() + 1;
    }

    @Override
    public List<PointReadingMenuDTO> getMenuTree(Integer bookId) throws BusinessException {
        // 验证点读书是否存在
        PointReadingBook book = pointReadingBookBiz.getById(bookId);
        if (book == null) {
            throw new BusinessException(AppErrorCode.POINT_READING_BOOK_NOT_EXIST_CODE, AppErrorCode.POINT_READING_BOOK_NOT_EXIST_MSG);
        }

        // 查询所有目录
        PointReadingMenuQueryBO queryBO = new PointReadingMenuQueryBO();
        queryBO.setBookId(bookId);
        queryBO.setStatus(YesOrNoEnum.YES.getCode());

        List<PointReadingMenu> allMenus = this.getBaseMapper().selectAllForTree(queryBO);
        if (CollUtil.isEmpty(allMenus)) {
            return new ArrayList<>();
        }

        // 转换为DTO
        List<PointReadingMenuDTO> allDTOs = allMenus.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());

        // 构建树形结构
        return buildTree(allDTOs, DEFAULT_PARENT_ID);
    }

    @Override
    public List<PointReadingMenuDTO> getChildren(Integer parentId) throws BusinessException {
        LambdaQueryWrapper<PointReadingMenu> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PointReadingMenu::getParentId, parentId == null ? DEFAULT_PARENT_ID : parentId)
                .eq(PointReadingMenu::getStatus, YesOrNoEnum.YES.getCode())
                .orderByAsc(PointReadingMenu::getSortNum)
                .orderByAsc(PointReadingMenu::getId);

        List<PointReadingMenu> children = this.list(wrapper);
        return children.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSort(Integer id, Integer sortNum) throws BusinessException {
        // 验证目录是否存在
        PointReadingMenu menu = this.getById(id);
        if (menu == null) {
            throw new BusinessException(AppErrorCode.POINT_READING_MENU_NOT_EXIST_CODE, AppErrorCode.POINT_READING_MENU_NOT_EXIST_MSG);
        }

        LambdaUpdateWrapper<PointReadingMenu> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(PointReadingMenu::getId, id)
                .set(PointReadingMenu::getSortNum, sortNum);

        return this.update(wrapper);
    }


    /**
     * 构建树形结构
     */
    private List<PointReadingMenuDTO> buildTree(List<PointReadingMenuDTO> allMenus, Integer parentId) {
        List<PointReadingMenuDTO> result = new ArrayList<>();

        for (PointReadingMenuDTO menu : allMenus) {
            if (parentId.equals(menu.getParentId())) {
                // 递归查找子节点
                List<PointReadingMenuDTO> children = buildTree(allMenus, menu.getId());
                menu.setChildren(children);
                result.add(menu);
            }
        }

        return result;
    }
}
