package com.dbj.classpal.books.service.service.advertisement.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.client.bo.advertisement.AdvertisementResourceBO;
import com.dbj.classpal.books.client.dto.advertisement.AdvertisementResourceAreaDTO;
import com.dbj.classpal.books.client.dto.advertisement.AdvertisementResourceDTO;
import com.dbj.classpal.books.service.biz.advertisement.impl.IAdvertisementResourceAreaBiz;
import com.dbj.classpal.books.service.biz.advertisement.impl.IAdvertisementResourceBiz;
import com.dbj.classpal.books.service.entity.advertisement.AdvertisementResource;
import com.dbj.classpal.books.service.entity.advertisement.AdvertisementResourceArea;
import com.dbj.classpal.books.service.mapper.advertisement.AdvertisementResourceMapper;
import com.dbj.classpal.books.service.service.advertisement.IAdvertisementResourceService;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 广告页面表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Service
public class AdvertisementResourceServiceImpl extends ServiceImpl<AdvertisementResourceMapper, AdvertisementResource> implements IAdvertisementResourceService {

    @Resource
    private IAdvertisementResourceBiz advertisementResourceBiz;

    @Resource
    private IAdvertisementResourceAreaBiz advertisementResourceAreaBiz;

    @Override
    public List<AdvertisementResourceDTO> getAdvertisementResourceList(AdvertisementResourceBO bo) {
        List<AdvertisementResource> advertisementResources = advertisementResourceBiz.list();
        Set<Integer> resourceIds = advertisementResources.stream().map(AdvertisementResource::getId).collect(Collectors.toSet());
        List<AdvertisementResourceArea> advertisementResourceAreas = advertisementResourceAreaBiz.lambdaQuery()
                .in(AdvertisementResourceArea::getResourceId, resourceIds)
                .eq(StringUtils.isNotBlank(bo.getAreaName()), AdvertisementResourceArea::getName, bo.getAreaName())
                .orderByDesc(AdvertisementResourceArea::getSort)
                .list();

        Map<Integer, List<AdvertisementResourceAreaDTO>> group = advertisementResourceAreas.stream()
                .map(area -> BeanUtil.copyProperties(area, AdvertisementResourceAreaDTO.class))
                .collect(Collectors.groupingBy(AdvertisementResourceAreaDTO::getResourceId));
        List<AdvertisementResourceDTO> resourceDTOList = advertisementResources.stream().map(resource -> {
            AdvertisementResourceDTO advertisementResourceDTO = BeanUtil.copyProperties(resource, AdvertisementResourceDTO.class);
            advertisementResourceDTO.setAdvertisementResourceAreas(group.get(resource.getId()));
            return advertisementResourceDTO;
        }).collect(Collectors.toList());
        return resourceDTOList;
    }
}
