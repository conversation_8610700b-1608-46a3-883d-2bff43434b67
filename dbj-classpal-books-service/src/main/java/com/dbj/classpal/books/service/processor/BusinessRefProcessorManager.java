package com.dbj.classpal.books.service.processor;

import com.dbj.classpal.books.client.enums.BusinessTypeEnum;
import com.dbj.classpal.books.common.dto.question.QuestionCategoryRefDTO;
import com.dbj.classpal.books.service.entity.question.QuestionCategoryBusinessRef;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 业务引用处理器管理器
 * 负责管理和调度不同的业务引用处理器
 */
@Slf4j
@Component
public class BusinessRefProcessorManager {

    @Resource
    private List<BusinessRefProcessor> processors;

    private final Map<BusinessTypeEnum, BusinessRefProcessor> processorMap = new HashMap<>();
    private BusinessRefProcessor defaultProcessor;

    @PostConstruct
    public void init() {
        // 按优先级排序处理器
        processors.sort(Comparator.comparingInt(BusinessRefProcessor::getPriority));
        
        for (BusinessRefProcessor processor : processors) {
            BusinessTypeEnum supportedType = processor.getSupportedBusinessType();
            if (supportedType == null) {
                if (defaultProcessor == null) {
                    defaultProcessor = processor;
                    log.info("注册默认业务引用处理器: {}", processor.getClass().getSimpleName());
                }
            } else {
                processorMap.put(supportedType, processor);
                log.info("注册业务引用处理器: {} -> {}", 
                    supportedType.getName(), processor.getClass().getSimpleName());
            }
        }
        
        log.info("业务引用处理器管理器初始化完成，共注册 {} 个处理器", processors.size());
    }

    /**
     * 处理业务引用信息
     * 
     * @param dto 要填充的DTO对象
     * @param businessRef 业务引用实体
     * @throws Exception 处理异常
     */
    public void processBusinessInfo(QuestionCategoryRefDTO dto, QuestionCategoryBusinessRef businessRef) throws Exception {
        BusinessTypeEnum typeEnum = BusinessTypeEnum.getByCode(businessRef.getBusinessType());
        
        BusinessRefProcessor processor = null;
        if (typeEnum != null) {
            processor = processorMap.get(typeEnum);
        }
        
        if (processor == null) {
            processor = defaultProcessor;
            if (processor == null) {
                throw new IllegalStateException("未找到合适的业务引用处理器，且默认处理器未配置");
            }
        }
        
        try {
            processor.processBusinessInfo(dto, businessRef);
        } catch (Exception e) {
            log.error("业务引用处理失败, processor: {}, businessType: {}, businessId: {}", 
                processor.getClass().getSimpleName(), businessRef.getBusinessType(), businessRef.getBusinessId(), e);
            throw e;
        }
    }

    /**
     * 获取支持的业务类型列表
     * 
     * @return 支持的业务类型列表
     */
    public List<BusinessTypeEnum> getSupportedBusinessTypes() {
        return List.copyOf(processorMap.keySet());
    }

    /**
     * 检查是否支持指定的业务类型
     * 
     * @param businessType 业务类型
     * @return 是否支持
     */
    public boolean isSupported(BusinessTypeEnum businessType) {
        return processorMap.containsKey(businessType) || defaultProcessor != null;
    }
}
