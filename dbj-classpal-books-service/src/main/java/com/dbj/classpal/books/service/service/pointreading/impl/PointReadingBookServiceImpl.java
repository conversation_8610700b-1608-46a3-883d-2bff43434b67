package com.dbj.classpal.books.service.service.pointreading.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingBookCopyApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingBookMoveApiBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingBookQueryBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingBookSaveBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingBookUpdateBO;
import com.dbj.classpal.books.common.dto.pointreading.PointReadingBookDTO;
import com.dbj.classpal.books.service.biz.pointreading.IPointReadingBookBiz;
import com.dbj.classpal.books.service.entity.pointreading.PointReadingBook;
import com.dbj.classpal.books.service.mapper.pointreading.PointReadingBookMapper;
import com.dbj.classpal.books.service.service.pointreading.IPointReadingBookService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 点读书 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PointReadingBookServiceImpl extends ServiceImpl<PointReadingBookMapper, PointReadingBook> implements IPointReadingBookService {

    private final IPointReadingBookBiz pointReadingBookBiz;

    @Override
    public Page<PointReadingBookDTO> page(PageInfo<PointReadingBookQueryBO> pageRequest) throws BusinessException {
        return pointReadingBookBiz.page(pageRequest);
    }

    @Override
    public PointReadingBookDTO detail(Integer id) throws BusinessException {
        return pointReadingBookBiz.detail(id);
    }

    @Override
    public Integer save(PointReadingBookSaveBO saveBO) throws BusinessException {
        return pointReadingBookBiz.save(saveBO);
    }

    @Override
    public boolean update(PointReadingBookUpdateBO updateBO) throws BusinessException {
        return pointReadingBookBiz.update(updateBO);
    }

    @Override
    public boolean delete(Integer id) throws BusinessException {
        return pointReadingBookBiz.delete(id);
    }

    @Override
    public boolean deleteBatch(List<Integer> ids) throws BusinessException {
        return pointReadingBookBiz.deleteBatch(ids);
    }

    @Override
    public boolean enableBatch(List<Integer> ids) throws BusinessException {
        return pointReadingBookBiz.enableBatch(ids);
    }

    @Override
    public boolean disableBatch(List<Integer> ids) throws BusinessException {
        return pointReadingBookBiz.disableBatch(ids);
    }

    @Override
    public boolean launchBatch(List<Integer> ids) throws BusinessException {
        return pointReadingBookBiz.launchBatch(ids);
    }

    @Override
    public boolean offlineBatch(List<Integer> ids) throws BusinessException {
        return pointReadingBookBiz.offlineBatch(ids);
    }

    @Override
    public PointReadingBookDTO getByMd5(String md5) throws BusinessException {
        return pointReadingBookBiz.getByMd5(md5);
    }

    @Override
    public boolean updateParseStatus(Integer id, Integer parseStatus) throws BusinessException {
        return pointReadingBookBiz.updateParseStatus(id, parseStatus);
    }

    @Override
    public Boolean copyBatch(PointReadingBookCopyApiBO copyBO) throws BusinessException {
        return pointReadingBookBiz.copyBatch(copyBO.getIds(), copyBO.getTargetCategoryId());
    }

    @Override
    public Boolean moveBatch(PointReadingBookMoveApiBO moveBO) throws BusinessException {
        return pointReadingBookBiz.moveBatch(moveBO.getIds(), moveBO.getTargetCategoryId());
    }
}
