package com.dbj.classpal.books.service.service.album;

import com.dbj.classpal.books.client.bo.album.AppAlbumElementsBusinessRefRemoveApiBO;
import com.dbj.classpal.books.common.bo.album.AppAlbumElementsBusinessRefQueryBO;
import com.dbj.classpal.books.common.bo.album.AppAlbumElementsBusinessRefQueryCommonBO;
import com.dbj.classpal.books.common.bo.album.AppAlbumElementsBusinessRefSaveBO;
import com.dbj.classpal.books.common.bo.common.CommonIdBO;
import com.dbj.classpal.books.common.dto.album.AppAlbumElementsBusinessRefMaterialQueryDTO;
import com.dbj.classpal.books.common.dto.album.AppAlbumElementsBusinessRefQueryDTO;
import com.dbj.classpal.books.common.dto.books.BooksRefDirectDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppElementsBusinessRefService
 * Date:     2025-04-15 10:18:47
 * Description: 表名： ,描述： 表
 */
@Mapper
public interface IAppElementsBusinessRefService {

    /**
     * 判断专辑分类的专辑是否存在业务关联引用
     * @param id
     * @return
     */
    Integer checkMenuRefCount(@Param("id")Integer id);


    /**
     * 查询专辑引用列表
     * @param bo
     * @return
     */
    List<AppAlbumElementsBusinessRefQueryDTO> getRefBusinessList(AppAlbumElementsBusinessRefQueryCommonBO bo);

    /**
     * 查询专辑关联资源列表
     * @param bo
     * @return
     */
    AppAlbumElementsBusinessRefMaterialQueryDTO getElementsBusinessRefMaterialRef(AppAlbumElementsBusinessRefQueryBO bo);


    /**
     * 新增专辑业务关联
     * @param bo
     * @return
     */
    Boolean saveOrUpdateElementsBusinessRefMaterialRef(AppAlbumElementsBusinessRefSaveBO bo);

    /**
     * 素材关联图书管理-图书资源引用跳转页面所需参数
     */
    BooksRefDirectDTO getAppAlbumElementsBusinessRefBooks(CommonIdBO bo) throws BusinessException;

    /**
     * 删除专辑业务关联
     * @param bo
     * @return
     */
    Boolean removeAlbumElementsBusinessRef(AppAlbumElementsBusinessRefRemoveApiBO bo);

    /**
     * 专辑关联业务列表
     * @param bo
     * @return
     */
    List<AppAlbumElementsBusinessRefQueryDTO> getAlbumElementsBusiness(AppAlbumElementsBusinessRefQueryCommonBO bo);
}
