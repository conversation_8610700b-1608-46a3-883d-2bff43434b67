package com.dbj.classpal.books.service.mapper.advertisement;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dbj.classpal.books.client.dto.advertisement.AdvertisementLevelConditionDTO;
import com.dbj.classpal.books.service.entity.advertisement.AdvertisementLevelCondition;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 广告条件层级表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
public interface AdvertisementLevelConditionMapper extends BaseMapper<AdvertisementLevelCondition> {

    /**
     * 根据广告id获取广告条件层级表
     * @param advertisementIds 广告id集合
     * @return 广告条件层级表
     */
    List<AdvertisementLevelConditionDTO> getByAdvertisementIds(@Param("advertisementIds") Collection<Integer> advertisementIds);
}
