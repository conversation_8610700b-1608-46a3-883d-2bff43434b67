package com.dbj.classpal.books.service.service.product;

import com.dbj.classpal.books.common.bo.product.ProductSalesConfigSaveBO;
import com.dbj.classpal.books.common.dto.product.ProductSalesConfigDTO;
import com.dbj.classpal.books.common.enums.product.ProductType;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/7/31 17:19
 */
public interface IProductSalesConfigService {

    Boolean save(ProductSalesConfigSaveBO bo);

    List<ProductSalesConfigDTO> listByProductIds(ProductType productType, Collection<Integer> productIds);

    ProductSalesConfigDTO getProductSalesConfig(ProductType productType, Integer productId);
}
