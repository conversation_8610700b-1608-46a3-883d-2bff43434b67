package com.dbj.classpal.books.service.service.product.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import com.dbj.classpal.books.common.bo.product.ProductSalesConfigSaveBO;
import com.dbj.classpal.books.common.dto.product.ProductSalesConfigDTO;
import com.dbj.classpal.books.common.enums.product.ProductType;
import com.dbj.classpal.books.service.biz.product.IProductSalesConfigBiz;
import com.dbj.classpal.books.service.entity.product.ProductSalesConfig;
import com.dbj.classpal.books.service.service.product.IProductSalesConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/7/31 17:20
 */
@Service
@RequiredArgsConstructor
public class ProductSalesConfigServiceImpl implements IProductSalesConfigService {

    private final IProductSalesConfigBiz productSalesConfigBiz;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer save(ProductSalesConfigSaveBO bo) {
        ProductSalesConfig config = productSalesConfigBiz.lambdaQuery()
                .eq(ProductSalesConfig::getProductId, bo.getProductId())
                .eq(ProductSalesConfig::getProductType, bo.getProductType())
                .one();
        if (config == null) {
            config = new ProductSalesConfig();
        }
        BeanUtil.copyProperties(bo, config);
        Assert.isTrue(productSalesConfigBiz.saveOrUpdate(config));
        return config.getId();
    }

    @Override
    public List<ProductSalesConfigDTO> listByProductIds(ProductType productType, Collection<Integer> productIds) {
        List<ProductSalesConfig> productSalesConfigList = productSalesConfigBiz.lambdaQuery()
                .eq(ProductSalesConfig::getProductType, productType)
                .in(ProductSalesConfig::getProductId, productIds)
                .list();
        return BeanUtil.copyToList(productSalesConfigList, ProductSalesConfigDTO.class);
    }

    @Override
    public ProductSalesConfigDTO getProductSalesConfig(ProductType productType, Integer productId) {
        ProductSalesConfig config = productSalesConfigBiz.lambdaQuery()
                .eq(ProductSalesConfig::getProductId, productId)
                .eq(ProductSalesConfig::getProductType, productType)
                .one();
        return BeanUtil.copyProperties(config, ProductSalesConfigDTO.class);
    }
}
