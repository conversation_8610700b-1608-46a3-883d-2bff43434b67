package com.dbj.classpal.books.service.mapper.question;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dbj.classpal.books.service.entity.question.QuestionCategory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 题目分类 Mapper
 */
@Mapper
public interface QuestionCategoryMapper extends BaseMapper<QuestionCategory> {
    
    /**
     * 统计分类下的题目数量（包含子分类）
     */
    Long countQuestionsByCategoryId(@Param("categoryId") Integer categoryId);

    /**
     * 获取同级分类中的最大排序号
     *
     * @param fatherId 父级ID
     * @return 最大排序号
     */
    Integer selectMaxSortNum(@Param("fatherId") Integer fatherId);
} 