package com.dbj.classpal.books.service.service.ebooks.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigWatermarkTemplateEditApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigWatermarkTemplateQueryApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigWatermarkTemplateSaveApiBO;
import com.dbj.classpal.books.client.dto.ebooks.AppEbooksConfigWatermarkTemplateQueryApiDTO;
import com.dbj.classpal.books.common.bo.ebooks.AppEbooksConfigWatermarkTemplateQueryBO;
import com.dbj.classpal.books.common.dto.ebooks.AppEbooksConfigWatermarkTemplateQueryDTO;
import com.dbj.classpal.books.service.biz.ebooks.IAppEbooksConfigWatermarkTemplateBiz;
import com.dbj.classpal.books.service.entity.ebooks.AppEbooksConfigWatermarkTemplate;
import com.dbj.classpal.books.service.mapper.ebooks.AppEbooksConfigWatermarkTemplateMapper;
import com.dbj.classpal.books.service.service.ebooks.IAppEbooksConfigWatermarkTemplateService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;
import static com.dbj.classpal.books.common.constant.AppErrorCode.ID_LIST_NOT_EMPTY_MSG;

@Service
public class AppEbooksConfigWatermarkTemplateServiceImpl implements IAppEbooksConfigWatermarkTemplateService {

    @Resource
    private AppEbooksConfigWatermarkTemplateMapper mapper;
    @Resource
    private IAppEbooksConfigWatermarkTemplateBiz watermarkTemplateBiz;

    @Override
    public Page<AppEbooksConfigWatermarkTemplateQueryApiDTO> pageInfo(PageInfo<AppEbooksConfigWatermarkTemplateQueryApiBO> pageInfo) {
        AppEbooksConfigWatermarkTemplateQueryBO queryBO = new AppEbooksConfigWatermarkTemplateQueryBO();
        BeanUtil.copyProperties(pageInfo.getData(), queryBO);
        Page<AppEbooksConfigWatermarkTemplateQueryDTO> page = mapper.pageInfo(pageInfo.getPage(), queryBO);
        // 2. 转换为VO
        return (Page<AppEbooksConfigWatermarkTemplateQueryApiDTO>) page.convert(this::convertToDTO);
    }

    @Override
    public List<AppEbooksConfigWatermarkTemplateQueryApiDTO> getAll() {
        return watermarkTemplateBiz.list().stream().map(d -> {
            AppEbooksConfigWatermarkTemplateQueryApiDTO apiDTO = new AppEbooksConfigWatermarkTemplateQueryApiDTO();
            BeanUtil.copyProperties(d, apiDTO);
            return apiDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public Boolean saveEbooksConfigWatermarkTemplate(AppEbooksConfigWatermarkTemplateSaveApiBO bo) throws BusinessException {
        List<AppEbooksConfigWatermarkTemplate> templateList = watermarkTemplateBiz.lambdaQuery().eq(AppEbooksConfigWatermarkTemplate::getTemplateName, bo.getTemplateName()).list();
        if (CollectionUtils.isNotEmpty(templateList)) {
            throw new BusinessException(APP_EBOOKS_CONFIG_WATERMARK_TEMPLATE_SAME_NAME_FAILED_CODE,APP_EBOOKS_CONFIG_WATERMARK_TEMPLATE_SAME_NAME_FAILED_MSG);
        }
        AppEbooksConfigWatermarkTemplate template = new AppEbooksConfigWatermarkTemplate();
        BeanUtil.copyProperties(bo, template);
        return watermarkTemplateBiz.save(template);
    }

    @Override
    public Boolean updateEbooksConfigWatermarkTemplate(AppEbooksConfigWatermarkTemplateEditApiBO bo) throws BusinessException {
        List<AppEbooksConfigWatermarkTemplate> templateList = watermarkTemplateBiz.lambdaQuery().ne(AppEbooksConfigWatermarkTemplate::getId,bo.getId()).eq(AppEbooksConfigWatermarkTemplate::getTemplateName, bo.getTemplateName()).list();
        if (CollectionUtils.isNotEmpty(templateList)) {
            throw new BusinessException(APP_EBOOKS_CONFIG_WATERMARK_TEMPLATE_SAME_NAME_FAILED_CODE,APP_EBOOKS_CONFIG_WATERMARK_TEMPLATE_SAME_NAME_FAILED_MSG);
        }
        AppEbooksConfigWatermarkTemplate template = new AppEbooksConfigWatermarkTemplate();
        BeanUtil.copyProperties(bo, template);
        return watermarkTemplateBiz.updateById(template);
    }

    @Override
    public Boolean deleteEbooksConfigWatermarkTemplate(CommonIdsApiBO bo) throws BusinessException {
        if (bo == null || CollectionUtils.isEmpty(bo.getIds())) {
            throw new BusinessException(ID_LIST_NOT_EMPTY_CODE,ID_LIST_NOT_EMPTY_MSG);
        }
        return watermarkTemplateBiz.removeBatchByIds(bo.getIds());
    }

    private AppEbooksConfigWatermarkTemplateQueryApiDTO convertToDTO(AppEbooksConfigWatermarkTemplateQueryDTO dto) {
        AppEbooksConfigWatermarkTemplateQueryApiDTO vo = new AppEbooksConfigWatermarkTemplateQueryApiDTO();
        BeanUtil.copyProperties(dto, vo);
        return vo;
    }
}
