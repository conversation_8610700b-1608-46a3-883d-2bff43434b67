package com.dbj.classpal.books.service.remote.sys;

import com.alibaba.nacos.client.config.utils.ContentUtils;
import com.dbj.classpal.admin.client.bo.app.dict.DictItemApiQueryBo;
import com.dbj.classpal.admin.client.dto.sys.dict.SysDictItemApiDTO;
import com.dbj.classpal.app.client.api.user.AppUserClientApi;
import com.dbj.classpal.app.client.bo.user.UserIdApiBO;
import com.dbj.classpal.app.client.bo.user.UserIdsApiBO;
import com.dbj.classpal.app.client.dto.user.CurrentUserApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import com.dbj.classpal.framework.utils.util.ContextAppUtil;
import com.dbj.classpal.framework.utils.util.ContextUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: SysAppUserRemoteService
 * Date:     2025-05-19 16:45:39
 * Description: 表名： ,描述： 表
 */
@Component
public class SysAppUserRemoteService {
    @Resource
    private AppUserClientApi appUserClientApi;

    /**
     * 通过id列表查询用户列表信息
     * @param ids
     * @return
     * @throws BusinessException
     */
    public List<CurrentUserApiDTO> listById(List<Integer>ids) throws BusinessException {
        UserIdsApiBO userIdsApiBO = new UserIdsApiBO();
        userIdsApiBO.setUserIds(ids);
        RestResponse<List<CurrentUserApiDTO>> result = appUserClientApi.listById(userIdsApiBO);
        return result.returnProcess(result);
    }

    public CurrentUserApiDTO getCurrentUser(UserIdApiBO userIdBO) throws BusinessException {
        RestResponse<CurrentUserApiDTO> result =  appUserClientApi.getCurrentUser(userIdBO);
        return result.returnProcess(result);
    }

    public CurrentUserApiDTO getCurrentUser() throws BusinessException {
        UserIdApiBO userIdApiBO = new UserIdApiBO();
        userIdApiBO.setUserId(ContextAppUtil.getAppUserIdInt());
        RestResponse<CurrentUserApiDTO> result =  appUserClientApi.getCurrentUser(userIdApiBO);
        return result.returnProcess(result);
    }


}
