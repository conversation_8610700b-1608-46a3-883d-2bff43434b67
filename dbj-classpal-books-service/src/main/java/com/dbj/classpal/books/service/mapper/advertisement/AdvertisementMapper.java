package com.dbj.classpal.books.service.mapper.advertisement;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.advertisement.AdvertisementPageBO;
import com.dbj.classpal.books.client.dto.advertisement.AdvertisementDTO;
import com.dbj.classpal.books.service.entity.advertisement.Advertisement;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 广告信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
public interface AdvertisementMapper extends BaseMapper<Advertisement> {

    /**
     * 获取广告信息分页列表
     *
     * @param page
     * @param bo
     * @return
     */
    Page<AdvertisementDTO> getAdvertisementPageList(Page page, @Param("bo") AdvertisementPageBO bo);
}
