package com.dbj.classpal.books.service.biz.books.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.client.bo.books.BooksInfoPageBO;
import com.dbj.classpal.books.client.dto.books.BooksInfoPageApiDTO;
import com.dbj.classpal.books.service.biz.books.IBooksInfoBiz;
import com.dbj.classpal.books.service.entity.books.BooksInfo;
import com.dbj.classpal.books.service.mapper.books.BooksInfoMapper;
import com.dbj.classpal.framework.commons.request.PageInfo;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 图书表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class BooksInfoBizImpl extends ServiceImpl<BooksInfoMapper, BooksInfo> implements IBooksInfoBiz {

    @Override
    public Page<BooksInfoPageApiDTO> pageInfo(PageInfo<BooksInfoPageBO> pageRequest) {
        return baseMapper.pageInfo(pageRequest.getPage(), pageRequest.getData());
    }
}
