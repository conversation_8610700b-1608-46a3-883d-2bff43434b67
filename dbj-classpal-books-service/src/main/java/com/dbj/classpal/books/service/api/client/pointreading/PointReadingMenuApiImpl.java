package com.dbj.classpal.books.service.api.client.pointreading;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.pointreading.PointReadingMenuApi;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingMenuBatchApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingMenuQueryApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingMenuSaveApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingMenuSortApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingMenuUpdateApiBO;
import com.dbj.classpal.books.client.dto.pointreading.PointReadingMenuApiDTO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingMenuQueryBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingMenuSaveBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingMenuUpdateBO;
import com.dbj.classpal.books.common.dto.pointreading.PointReadingMenuDTO;
import com.dbj.classpal.books.service.service.pointreading.IPointReadingMenuService;
import com.dbj.classpal.framework.commons.converter.PageInfoConverter;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

/**
 * 点读书目录 API实现类
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
@RestController
public class PointReadingMenuApiImpl implements PointReadingMenuApi {

    @Resource
    private IPointReadingMenuService pointReadingMenuService;

    @Override
    public RestResponse<Page<PointReadingMenuApiDTO>> pageMenu(PageInfo<PointReadingMenuQueryApiBO> pageInfo) throws BusinessException {
        log.info("分页查询点读书目录 入参：{}", JSON.toJSONString(pageInfo));


        PageInfo<PointReadingMenuQueryBO> servicePageRequest = PageInfoConverter.convertPageInfo(pageInfo, PointReadingMenuQueryBO.class);
        Page<PointReadingMenuDTO> servicePage = pointReadingMenuService.pageMenu(servicePageRequest);
        Page<PointReadingMenuApiDTO> apiPage = convertApiPage(servicePage);

        log.info("分页查询点读书目录 结果：{}", JSON.toJSONString(apiPage));
        return RestResponse.success(apiPage);
    }

    @Override
    public RestResponse<PointReadingMenuApiDTO> detail(Integer id) throws BusinessException {
        log.info("查询目录详情 入参：{}", id);
        
        PointReadingMenuDTO dto = pointReadingMenuService.detail(id);
        PointReadingMenuApiDTO apiDTO = convertToApiDTO(dto);
        
        log.info("查询目录详情 结果：{}", JSON.toJSONString(apiDTO));
        return RestResponse.success(apiDTO);
    }

    @Override
    public RestResponse<Integer> save(PointReadingMenuSaveApiBO saveBO) throws BusinessException {
        log.info("保存点读书目录 入参：{}", JSON.toJSONString(saveBO));
        
        PointReadingMenuSaveBO saveDTO = new PointReadingMenuSaveBO();
        BeanUtil.copyProperties(saveBO, saveDTO);
        
        Integer id = pointReadingMenuService.save(saveDTO);
        
        log.info("保存点读书目录 结果：{}", id);
        return RestResponse.success(id);
    }

    @Override
    public RestResponse<Boolean> update(PointReadingMenuUpdateApiBO updateBO) throws BusinessException {
        log.info("更新点读书目录 入参：{}", JSON.toJSONString(updateBO));
        
        PointReadingMenuUpdateBO updateDTO = new PointReadingMenuUpdateBO();
        BeanUtil.copyProperties(updateBO, updateDTO);
        
        Boolean result = pointReadingMenuService.update(updateDTO);
        
        log.info("更新点读书目录 结果：{}", result);
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<Boolean> delete(Integer id) throws BusinessException {
        log.info("删除点读书目录 入参：{}", id);
        
        Boolean result = pointReadingMenuService.delete(id);
        
        log.info("删除点读书目录 结果：{}", result);
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<Boolean> deleteBatch(PointReadingMenuBatchApiBO batchBO) throws BusinessException {
        log.info("批量删除点读书目录 入参：{}", JSON.toJSONString(batchBO));
        
        Boolean result = pointReadingMenuService.deleteBatch(batchBO.getIds());
        
        log.info("批量删除点读书目录 结果：{}", result);
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<List<PointReadingMenuApiDTO>> getMenuTree(Integer bookId) throws BusinessException {
        log.info("获取目录树形结构 入参：{}", bookId);
        
        List<PointReadingMenuDTO> menuTree = pointReadingMenuService.getMenuTree(bookId);
        List<PointReadingMenuApiDTO> apiTree = menuTree.stream()
                .map(this::convertToApiDTOWithChildren)
                .collect(Collectors.toList());
        
        log.info("获取目录树形结构 结果：{}", JSON.toJSONString(apiTree));
        return RestResponse.success(apiTree);
    }

    @Override
    public RestResponse<List<PointReadingMenuApiDTO>> getChildren(Integer parentId) throws BusinessException {
        log.info("获取子目录列表 入参：{}", parentId);
        
        List<PointReadingMenuDTO> children = pointReadingMenuService.getChildren(parentId);
        List<PointReadingMenuApiDTO> apiChildren = children.stream()
                .map(this::convertToApiDTO)
                .collect(Collectors.toList());
        
        log.info("获取子目录列表 结果：{}", JSON.toJSONString(apiChildren));
        return RestResponse.success(apiChildren);
    }

    @Override
    public RestResponse<Boolean> updateSort(PointReadingMenuSortApiBO sortBO) throws BusinessException {
        log.info("更新排序 入参：{}", JSON.toJSONString(sortBO));
        
        Boolean result = pointReadingMenuService.updateSort(sortBO.getId(), sortBO.getSortNum());
        
        log.info("更新排序 结果：{}", result);
        return RestResponse.success(result);
    }

    /**
     * 转换API分页
     */
    private Page<PointReadingMenuApiDTO> convertApiPage(Page<PointReadingMenuDTO> servicePage) {
        Page<PointReadingMenuApiDTO> apiPage = new Page<>(servicePage.getCurrent(), servicePage.getSize(), servicePage.getTotal());
        List<PointReadingMenuApiDTO> apiList = servicePage.getRecords().stream()
                .map(dto -> BeanUtil.copyProperties(dto, PointReadingMenuApiDTO.class))
                .collect(Collectors.toList());
        apiPage.setRecords(apiList);
        return apiPage;
    }
    /**
     * 转换为API DTO
     */
    private PointReadingMenuApiDTO convertToApiDTO(PointReadingMenuDTO dto) {
        PointReadingMenuApiDTO apiDTO = new PointReadingMenuApiDTO();
        BeanUtil.copyProperties(dto, apiDTO);
        return apiDTO;
    }

    /**
     * 转换为API DTO（包含子节点）
     */
    private PointReadingMenuApiDTO convertToApiDTOWithChildren(PointReadingMenuDTO dto) {
        PointReadingMenuApiDTO apiDTO = convertToApiDTO(dto);
        
        if (dto.getChildren() != null) {
            List<PointReadingMenuApiDTO> apiChildren = dto.getChildren().stream()
                    .map(this::convertToApiDTOWithChildren)
                    .collect(Collectors.toList());
            apiDTO.setChildren(apiChildren);
        }
        
        return apiDTO;
    }
}
