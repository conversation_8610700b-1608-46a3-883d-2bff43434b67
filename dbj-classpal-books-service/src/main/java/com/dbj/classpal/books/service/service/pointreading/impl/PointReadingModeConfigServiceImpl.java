package com.dbj.classpal.books.service.service.pointreading.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingModeConfigQueryBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingModeConfigSaveBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingModeConfigUpdateBO;
import com.dbj.classpal.books.common.dto.pointreading.PointReadingModeConfigDTO;
import com.dbj.classpal.books.service.biz.pointreading.IPointReadingModeConfigBiz;
import com.dbj.classpal.books.service.entity.pointreading.PointReadingModeConfig;
import com.dbj.classpal.books.service.service.pointreading.IPointReadingModeConfigService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 点读书模式配置 服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
@Service
public class PointReadingModeConfigServiceImpl implements IPointReadingModeConfigService {

    @Resource
    private IPointReadingModeConfigBiz pointReadingModeConfigBiz;

    @Override
    public Page<PointReadingModeConfigDTO> pageModeConfig(PageInfo<PointReadingModeConfigQueryBO> pageInfo) throws BusinessException {
        Page<PointReadingModeConfig> page = new Page<>(pageInfo.getPageNum(), pageInfo.getPageSize());
        return pointReadingModeConfigBiz.pageModeConfig(page, pageInfo.getData());
    }

    @Override
    public PointReadingModeConfigDTO detail(Integer id) throws BusinessException {
        return pointReadingModeConfigBiz.detail(id);
    }

    @Override
    public Integer save(PointReadingModeConfigSaveBO saveBO) throws BusinessException {
        return pointReadingModeConfigBiz.saveModeConfig(saveBO);
    }

    @Override
    public Boolean update(PointReadingModeConfigUpdateBO updateBO) throws BusinessException {
        return pointReadingModeConfigBiz.updateModeConfig(updateBO);
    }

    @Override
    public Boolean delete(Integer id) throws BusinessException {
        return pointReadingModeConfigBiz.deleteModeConfig(id);
    }

    @Override
    public Boolean deleteBatch(List<Integer> ids) throws BusinessException {
        return pointReadingModeConfigBiz.deleteBatch(ids);
    }

    @Override
    public List<PointReadingModeConfigDTO> getModeConfigsByBook(Integer bookId) throws BusinessException {
        return pointReadingModeConfigBiz.getModeConfigsByBook(bookId);
    }

    @Override
    public Boolean updateSort(Integer id, Integer sortNum) throws BusinessException {
        return pointReadingModeConfigBiz.updateSort(id, sortNum);
    }

    @Override
    public Boolean enable(Integer id) throws BusinessException {
        return pointReadingModeConfigBiz.enable(id);
    }

    @Override
    public Boolean disable(Integer id) throws BusinessException {
        return pointReadingModeConfigBiz.disable(id);
    }

    @Override
    public Boolean enableBatch(List<Integer> ids) throws BusinessException {
        return pointReadingModeConfigBiz.enableBatch(ids);
    }

    @Override
    public Boolean disableBatch(List<Integer> ids) throws BusinessException {
        return pointReadingModeConfigBiz.disableBatch(ids);
    }

    @Override
    public Boolean updateProcessStatus(Integer id, Integer processStatus) throws BusinessException {
        return pointReadingModeConfigBiz.updateProcessStatus(id, processStatus);
    }
}
