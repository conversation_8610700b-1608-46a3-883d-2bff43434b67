package com.dbj.classpal.books.service.service.studycenter;

import com.dbj.classpal.books.common.bo.studycenter.*;
import com.dbj.classpal.books.common.dto.studycenter.*;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;

public interface IAppStudyModuleResourceRelService {
    Boolean create(AppStudyModuleResourceRelCreateBO bo) throws BusinessException;
    Boolean update(AppStudyModuleResourceRelUpdateBO bo) throws BusinessException;
    Boolean delete(List<Integer> ids) throws BusinessException;
    AppStudyModuleResourceRelDetailDTO detail(Integer id) throws BusinessException;
} 