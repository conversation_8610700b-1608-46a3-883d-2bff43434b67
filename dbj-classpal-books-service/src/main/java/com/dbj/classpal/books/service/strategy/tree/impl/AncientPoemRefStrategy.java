package com.dbj.classpal.books.service.strategy.tree.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dbj.classpal.books.service.biz.poem.IAncientPoemBiz;
import com.dbj.classpal.books.service.entity.poem.AncientPoem;
import com.dbj.classpal.books.service.strategy.tree.ITreeClassifyRefStrategy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 分类树古诗背诵引用数量 策略实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Component
public class AncientPoemRefStrategy implements ITreeClassifyRefStrategy {


    @Resource
    private IAncientPoemBiz ancientPoemBiz;

    @Override
    public Map<Integer, Long> getRefNumMap(Collection<Integer> classifyIds) {
        if(CollUtil.isEmpty(classifyIds)) {
            return Collections.emptyMap();
        }
        String groupByColumn = "classify_id";
        QueryWrapper<AncientPoem> wrapper =  Wrappers.query();
        List<Map<String, Object>> maps = ancientPoemBiz.listMaps(wrapper.select(groupByColumn, "COUNT(*) AS count")
                .in(groupByColumn, classifyIds)
                .groupBy(groupByColumn));
        return maps.stream()
                .collect(Collectors.toMap(
                        map -> (Integer)map.get(groupByColumn),
                        map -> (Long)map.get("count")));
    }
}
