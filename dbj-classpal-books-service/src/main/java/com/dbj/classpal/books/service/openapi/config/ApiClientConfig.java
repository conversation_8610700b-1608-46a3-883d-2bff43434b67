package com.dbj.classpal.books.service.openapi.config;

import com.dbj.framework.openapi.factory.DbjClientFactory;
import com.dbj.framework.openapi.request.ApiRequest;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

@Configuration
@EnableConfigurationProperties
@EnableScheduling
public class ApiClientConfig {
    
    @Bean
    public DbjClientFactory dbjClientFactory() {
        return new DbjClientFactory();
    }
    
    @Bean
    public ApiRequest noAuthApiRequest(
            @Value("${dbj.api-client.base-url}") String baseUrl,
            DbjClientFactory dbjClientFactory) {
        return dbjClientFactory.createNoAuthRequest(baseUrl);
    }
}