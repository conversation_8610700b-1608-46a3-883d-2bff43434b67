package com.dbj.classpal.books.service.service.ebooks;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookshelfQueryBO;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookshelfH5QueryBO;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookshelfSaveBO;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookshelfUpdateBO;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookshelfDTO;
import com.dbj.classpal.books.service.entity.product.AppEBookshelf;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 书架 服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13
 */
public interface IAppEBookshelfService extends IService<AppEBookshelf> {

    /**
     * 分页查询书架列表
     *
     * @param pageRequest 分页查询参数
     * @return 书架分页数据
     */
    Page<AppEBookshelfDTO> page(PageInfo<AppEBookshelfQueryBO> pageRequest) throws BusinessException;

    /**
     * H5分页查询书架列表（支持书城关联查询）
     *
     * @param pageRequest 分页查询参数
     * @return 书架分页数据
     */
    Page<AppEBookshelfDTO> pageForH5(PageInfo<AppEBookshelfH5QueryBO> pageRequest) throws BusinessException;

    /**
     * 查询书架详情
     * 
     * @param id 书架ID
     * @return 书架详情数据
     */
    AppEBookshelfDTO detail(Integer id) throws BusinessException;

    /**
     * 新增书架
     * 
     * @param saveBO 书架保存参数
     * @return 新增书架ID
     */
    Integer save(AppEBookshelfSaveBO saveBO) throws BusinessException;

    /**
     * 更新书架
     * 
     * @param updateBO 书架更新参数
     * @return 更新结果
     */
    boolean update(AppEBookshelfUpdateBO updateBO) throws BusinessException;

    /**
     * 删除书架
     * 
     * @param id 书架ID
     * @return 删除结果
     */
    boolean delete(Integer id) throws BusinessException;

    /**
     * 批量删除书架
     * 
     * @param ids ID列表
     * @return 批量删除结果
     */
    boolean deleteBatch(List<Integer> ids) throws BusinessException;

    /**
     * 批量启用书架
     * 
     * @param ids ID列表
     * @return 批量启用结果
     */
    boolean enableBatch(List<Integer> ids) throws BusinessException;

    /**
     * 批量禁用书架
     * 
     * @param ids ID列表
     * @return 批量禁用结果
     */
    boolean disableBatch(List<Integer> ids) throws BusinessException;
    
    /**
     * 设置书架封面
     * 
     * @param id 书架ID
     * @param bookId 单书ID，用于获取封面，可为null
     * @param coverUrl 自定义封面URL，优先级高于bookId
     * @return 设置结果
     */
    boolean setCover(Integer id, Integer bookId, String coverUrl) throws BusinessException;
    
    /**
     * 向书架添加单书
     * 
     * @param shelfId 书架ID
     * @param bookIds 单书ID列表
     * @return 添加结果
     */
    boolean addBooks(Integer shelfId, List<Integer> bookIds) throws BusinessException;
    
    /**
     * 从书架移除单书
     * 
     * @param shelfId 书架ID
     * @param bookIds 单书ID列表
     * @return 移除结果
     */
    boolean removeBooks(Integer shelfId, List<Integer> bookIds) throws BusinessException;
    
    /**
     * 调整书架单书排序
     *
     * @param shelfId     书架ID
     * @param bookIds
     * @param bookSortMap 单书排序映射，key为单书ID，value为排序序号
     * @return 排序结果
     */
    boolean sortBooks(Integer shelfId,List<Integer> bookIds, Map<Integer, Integer> bookSortMap) throws BusinessException;
    /**
     * 批量允许下载
     *
     * @param ids ID列表
     * @return 批量允许下载结果
     */
    boolean allowDownloadBatch(List<Integer> ids) throws BusinessException;

    /**
     * 批量关闭下载
     *
     * @param ids ID列表
     * @return 批量关闭下载结果
     */
    boolean disableDownloadBatch(List<Integer> ids) throws BusinessException;
}