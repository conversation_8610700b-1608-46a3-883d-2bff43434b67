package com.dbj.classpal.books.service.remote.file.imports;

import com.dbj.classpal.admin.client.api.file.importfile.ExcelFileImportApi;
import com.dbj.classpal.admin.client.bo.file.importfile.ExcelFileImportQueryApiBO;
import com.dbj.classpal.admin.client.bo.file.importfile.ExcelFileImportUpdateApiBO;
import com.dbj.classpal.admin.client.dto.file.importfile.ExcelFileImportQueryApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AlbumFileImportApiService
 * Date:     2025-04-08 11:57:24
 * Description: 表名： ,描述： 表
 */
@Component
public class MaterialFileImportRemoteService {

    @Resource
    private ExcelFileImportApi fileImportApi;


    public ExcelFileImportQueryApiDTO getExcelFileImportById(ExcelFileImportQueryApiBO bo) throws BusinessException {
        RestResponse<ExcelFileImportQueryApiDTO> result = fileImportApi.getExcelFileImportById(bo);
        return result.returnProcess(result);
    }


    public Boolean updateExcelFileImportById(ExcelFileImportUpdateApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = fileImportApi.updateExcelFileImportById(bo);
        return result.returnProcess(result);
    }


    public List<ExcelFileImportQueryApiDTO> getExcelFileImportListByStatus(ExcelFileImportQueryApiBO bo) throws BusinessException {
        RestResponse<List<ExcelFileImportQueryApiDTO>> result = fileImportApi.getExcelFileImportListByStatus(bo);
        return result.returnProcess(result);
    }

    public ExcelFileImportQueryApiDTO getByAnalysisJobId(ExcelFileImportQueryApiBO bo) throws BusinessException {
        RestResponse<ExcelFileImportQueryApiDTO> result = fileImportApi.getByAnalysisJobId(bo);
        return result.returnProcess(result);
    }

    public ExcelFileImportQueryApiDTO getByTransCodeJobId(ExcelFileImportQueryApiBO bo) throws BusinessException {
        RestResponse<ExcelFileImportQueryApiDTO> result = fileImportApi.getByTransCodeJobId(bo);
        return result.returnProcess(result);
    }
}
