package com.dbj.classpal.books.service.strategy.business.common.impl;

import com.dbj.classpal.books.service.biz.album.IAppAlbumElementsBiz;
import com.dbj.classpal.books.service.entity.album.AppAlbumElements;
import com.dbj.classpal.books.service.strategy.business.handler.ICommonBusinessStrategyHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppAlbumRefMaterialStrategy
 * Date:     2025-05-09 13:07:37
 * Description: 表名： ,描述： 表
 */
@Service("appAlbumRefMaterialStrategy")
public class AppAlbumRefMaterialStrategy extends ICommonBusinessStrategyHandler {

    @Resource
    private IAppAlbumElementsBiz elementsBiz;


    @Override
    public Map<Integer, String> getBusinessName(List<Integer> businessId) {
        Map<Integer, String> map = new HashMap<>();
        List<AppAlbumElements> appAlbumElements = elementsBiz.listByIds(businessId);
        if(CollectionUtils.isNotEmpty(appAlbumElements)){
            for (AppAlbumElements appAlbumElement : appAlbumElements) {
                map.put(appAlbumElement.getId(),appAlbumElement.getAlbumTitle());
            }
        }
        return map;
    }
}
