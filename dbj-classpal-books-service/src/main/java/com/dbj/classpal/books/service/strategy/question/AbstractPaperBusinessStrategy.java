package com.dbj.classpal.books.service.strategy.question;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dbj.classpal.books.common.bo.paper.*;
import com.dbj.classpal.books.common.dto.paper.UserPaperCheckSubmitDTO;
import com.dbj.classpal.books.common.dto.paper.UserPaperDTO;
import com.dbj.classpal.books.service.biz.paper.IAppUserPaperInfoBiz;
import com.dbj.classpal.books.service.biz.paper.IAppUserPaperQuestionBlankResultBiz;
import com.dbj.classpal.books.service.biz.paper.IAppUserPaperQuestionResultBiz;
import com.dbj.classpal.books.service.entity.paper.AppUserPaperInfo;
import com.dbj.classpal.books.service.entity.paper.AppUserPaperQuestionBlankResult;
import com.dbj.classpal.books.service.entity.paper.AppUserPaperQuestionResult;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import com.dbj.classpal.framework.redisson.config.RedissonRedisUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.List;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;

/**
 * 试卷业务策略抽象基类
 */
@Slf4j
public abstract class AbstractPaperBusinessStrategy implements IPaperBusinessStrategy {

    @Resource
    protected IAppUserPaperInfoBiz paperInfoBiz;

    @Resource
    protected IAppUserPaperQuestionResultBiz paperQuestionResultBiz;

    @Resource
    protected IAppUserPaperQuestionBlankResultBiz paperQuestionBlankResultBiz;

    @Resource
    private RedissonRedisUtils redissonRedisUtils;

    @Override
    public UserPaperDTO createPaper(CreateUserPaperBO createBO) throws BusinessException {
        validateCreateParams(createBO);
        try {
            return paperInfoBiz.createPaper(createBO);
        } catch (BusinessException e) {
            log.error("创建试卷失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("创建试卷发生异常", e);
            throw new BusinessException(APP_CREATE_PAPER_FAIL_CODE,APP_CREATE_PAPER_FAIL_MSG);
        }
    }

    @Override
    public UserPaperCheckSubmitDTO checkSubmit(CheckSubmitBO serviceBO) throws BusinessException {
        List<AppUserPaperInfo> existingPapers = paperInfoBiz.getBaseMapper().selectList(
                new LambdaQueryWrapper<AppUserPaperInfo>()
                        .eq(AppUserPaperInfo::getAppUserId, serviceBO.getAppUserId())
                        .eq(AppUserPaperInfo::getBusinessId, serviceBO.getBusinessId())
                        .eq(AppUserPaperInfo::getBusinessType, serviceBO.getBusinessType())
                        .orderByDesc(AppUserPaperInfo::getCreateTime)
        );

        if (CollectionUtils.isEmpty(existingPapers)) {
            return UserPaperCheckSubmitDTO.builder().isSubmit(Boolean.FALSE).paperId(null).build();
        } else {
            return UserPaperCheckSubmitDTO.builder().isSubmit(Boolean.TRUE).paperId(existingPapers.get(0).getId()).build();
        }
    }

    @Override
    public UserPaperDTO getPaperInfo(Integer paperId) throws BusinessException {
        if (paperId == null) {
            throw new BusinessException(APP_PAPER_ID_NOT_NULL_CODE,APP_PAPER_ID_NOT_NULL_MSG);
        }
        try {
            return paperInfoBiz.getPaperInfo(paperId);
        } catch (BusinessException e) {
            log.error("获取试卷信息失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("获取试卷信息发生异常", e);
            throw new BusinessException(APP_GET_PAPER_INFO_ERROR_CODE,APP_GET_PAPER_INFO_ERROR_MSG);
        }
    }


    /**
     * 基础的重考实现，子类可以扩展
     */
    protected void doRetakePaper(RetakePaperBO retakePaperBO) throws BusinessException {
        try {
            AppUserPaperInfo paperInfo = paperInfoBiz.getBaseMapper().selectById(retakePaperBO.getPaperId());
            if (paperInfo == null) {
                throw new BusinessException(APP_PAPER_NOT_EXIST_CODE,APP_PAPER_NOT_EXIST_MSG);
            }
            paperInfoBiz.remove(new LambdaQueryWrapper<AppUserPaperInfo>().eq(AppUserPaperInfo::getId,retakePaperBO.getPaperId()));

            paperQuestionResultBiz.getBaseMapper().delete(
                new LambdaQueryWrapper<AppUserPaperQuestionResult>()
                    .eq(AppUserPaperQuestionResult::getPaperId, retakePaperBO.getPaperId())
                    .eq(AppUserPaperQuestionResult::getAppUserId, retakePaperBO.getAppUserId())
            );

            paperQuestionBlankResultBiz.getBaseMapper().delete(
                new LambdaQueryWrapper<AppUserPaperQuestionBlankResult>()
                    .eq(AppUserPaperQuestionBlankResult::getPaperId, retakePaperBO.getPaperId())
                    .eq(AppUserPaperQuestionBlankResult::getAppUserId, retakePaperBO.getAppUserId())
            );
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("重新考试失败", e);
            throw new BusinessException(APP_RE_EXAM_FAIL_CODE,APP_RE_EXAM_FAIL_MSG);
        }
    }

    /**
     * 校验创建参数
     */
    protected void validateCreateParams(CreateUserPaperBO createBO) throws BusinessException {
        if (createBO == null) {
            throw new BusinessException(APP_SUBMIT_PARAM_NOT_NULL_CODE,APP_SUBMIT_PARAM_NOT_NULL_MSG);
        }
        if (createBO.getAppUserId() == null) {
            throw new BusinessException(APP_USER_ID_NOT_NULL_CODE,APP_USER_ID_NOT_NULL_MSG);
        }
        if (createBO.getBusinessId() == null) {
            throw new BusinessException(APP_BUSINESS_ID_NOT_NULL_CODE,APP_BUSINESS_ID_NOT_NULL_MSG);
        }
        if (createBO.getBusinessType() == null) {
            throw new BusinessException(APP_BUSINESS_TYPE_NOT_NULL_CODE,APP_BUSINESS_TYPE_NOT_NULL_MSG);
        }
    }

    @Override
    public void validateQueryParams(QueryPaperQuestionsBO queryBO) throws BusinessException {
        if (queryBO == null) {
            throw new BusinessException(APP_QUERY_PARAM_NOT_NULL_CODE,APP_QUERY_PARAM_NOT_NULL_MSG);
        }
        if (queryBO.getBusinessId() == null) {
            throw new BusinessException(APP_BUSINESS_ID_NOT_NULL_CODE,APP_BUSINESS_ID_NOT_NULL_MSG);
        }
        if (queryBO.getBusinessType() == null) {
            throw new BusinessException(APP_BUSINESS_TYPE_NOT_NULL_CODE,APP_BUSINESS_TYPE_NOT_NULL_MSG);
        }
    }

    protected UserPaperDTO doSubmitParer(SubmitPaperBO submitBO,String parerName) throws BusinessException {
        try {
            if (submitBO == null) {
                throw new BusinessException(APP_SUBMIT_PARAM_NOT_NULL_CODE,APP_SUBMIT_PARAM_NOT_NULL_MSG);
            }
            if (submitBO.getAppUserId() == null) {
                throw new BusinessException(APP_USER_ID_NOT_NULL_CODE,APP_USER_ID_NOT_NULL_MSG);
            }
            if (submitBO.getBusinessId() == null) {
                throw new BusinessException(APP_BUSINESS_ID_NOT_NULL_CODE,APP_BUSINESS_ID_NOT_NULL_MSG);
            }
            if (submitBO.getBusinessType() == null) {
                throw new BusinessException(APP_BUSINESS_TYPE_NOT_NULL_CODE,APP_BUSINESS_TYPE_NOT_NULL_MSG);
            }

            if (CollectionUtils.isEmpty(submitBO.getQuestionResults()) && CollectionUtils.isEmpty(submitBO.getBlankResults())) {
                throw new BusinessException(APP_PAPER_NO_ANSWER_CODE,APP_PAPER_NO_ANSWER_MSG);
            }

            // 创建试卷
            CreateUserPaperBO createBO = new CreateUserPaperBO();
            createBO.setAppUserId(submitBO.getAppUserId());
            createBO.setBusinessId(submitBO.getBusinessId());
            createBO.setBusinessType(submitBO.getBusinessType());
            String redisKey = "paper_question_order:" + submitBO.getAppUserId() + ":" + submitBO.getBusinessId() + ":" + submitBO.getBusinessType();
            String orders =  redissonRedisUtils.getValue(redisKey);
            log.info("试卷缓存中获得的题目列表顺序：{}", JSON.toJSONString(orders));
            createBO.setQuestionOrder(redissonRedisUtils.getValue(redisKey));
            createBO.setPaperName(parerName);
            createBO.setIsSubmit(YesOrNoEnum.YES.getCode());
            UserPaperDTO paperInfo = createPaper(createBO);
            return BeanUtil.copyProperties(paperInfo,UserPaperDTO.class);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("保存试卷结果失败", e);
            throw new BusinessException(APP_SAVE_PAPER_RESULT_FAIL_CODE,APP_SAVE_PAPER_RESULT_FAIL_MSG);
        }
    }
} 