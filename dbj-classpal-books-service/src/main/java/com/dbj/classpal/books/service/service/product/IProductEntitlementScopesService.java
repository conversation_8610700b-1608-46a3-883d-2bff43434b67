package com.dbj.classpal.books.service.service.product;

import com.dbj.classpal.books.client.bo.books.BooksInfoSaleConfigBO.RightScopeBO;
import com.dbj.classpal.books.client.dto.books.BooksInfoRightScopeDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/7/31 17:20
 */
public interface IProductEntitlementScopesService {

    void save(Integer configId, List<RightScopeBO> rightScopeList) throws BusinessException;

    List<BooksInfoRightScopeDTO> getScopeList(Integer configId);
}
