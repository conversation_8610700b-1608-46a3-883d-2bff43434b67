package com.dbj.classpal.books.service.remote.file;

import com.dbj.classpal.admin.client.api.file.importfile.ExcelFileImportApi;
import com.dbj.classpal.admin.client.bo.file.importfile.ExcelFileImportQueryApiBO;
import com.dbj.classpal.admin.client.bo.file.importfile.ExcelFileImportSaveApiBO;
import com.dbj.classpal.admin.client.dto.file.importfile.ExcelFileImportQueryApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className ExcelFileRemoteService
 * @description
 * @date 2025-04-17 13:42
 **/
@Component
public class ExcelFileRemoteService {
    @Resource
    private ExcelFileImportApi fileImportApi;
    /**
     * 新增一条导入数据
     * @param bo
     * @return
     * @throws BusinessException
     */
    public Boolean saveExcelFileImportById(ExcelFileImportSaveApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = fileImportApi.saveExcelFileImportById(bo);
        return result.returnProcess(result);
    }

    /**
     * 判断是否存在正在处理中的md5文件
     * @param bo
     * @return
     * @throws BusinessException
     */
    public Boolean checkProcessMd5File(ExcelFileImportQueryApiBO bo) throws BusinessException {
        RestResponse<Boolean> response = fileImportApi.checkProcessMd5File(bo);
        return response.returnProcess(response);
    }
}
