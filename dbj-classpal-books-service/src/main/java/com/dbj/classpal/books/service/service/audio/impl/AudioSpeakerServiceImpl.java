package com.dbj.classpal.books.service.service.audio.impl;


import com.dbj.classpal.books.service.biz.audio.IAudioSpeakerBiz;
import com.dbj.classpal.books.service.entity.audio.AudioSpeaker;
import com.dbj.classpal.books.service.service.audio.IAudioSpeakerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 发音人配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Service
public class AudioSpeakerServiceImpl implements IAudioSpeakerService {

    @Autowired
    private IAudioSpeakerBiz audioSpeakerBiz;

    @Override
    public List<AudioSpeaker> getAllList() {
        return audioSpeakerBiz.lambdaQuery().list();
    }

    @Override
    public int updateBatch(List<AudioSpeaker> updateList) {
        boolean flag = audioSpeakerBiz.updateBatchById(updateList);
        return flag ? updateList.size() : 0;
    }
}
