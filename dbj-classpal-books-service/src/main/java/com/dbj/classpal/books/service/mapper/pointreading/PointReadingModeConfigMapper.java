package com.dbj.classpal.books.service.mapper.pointreading;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingModeConfigQueryBO;
import com.dbj.classpal.books.service.entity.pointreading.PointReadingModeConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 点读书模式配置 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Mapper
public interface PointReadingModeConfigMapper extends BaseMapper<PointReadingModeConfig> {

    /**
     * 分页查询点读书模式配置
     *
     * @param page 分页参数
     * @param query 查询条件
     * @return 分页结果
     */
    Page<PointReadingModeConfig> selectPage(Page<PointReadingModeConfig> page, @Param("query") PointReadingModeConfigQueryBO query);

    /**
     * 查询点读书下的模式配置列表
     *
     * @param bookId 点读书ID
     * @return 模式配置列表
     */
    List<PointReadingModeConfig> selectByBookId(@Param("bookId") Integer bookId);

    /**
     * 查询点读书下的模式配置数量
     *
     * @param bookId 点读书ID
     * @return 模式配置数量
     */
    int countByBook(@Param("bookId") Integer bookId);

    /**
     * 根据字典ID和点读书ID查询配置
     *
     * @param configDictId 配置字典ID
     * @param bookId 点读书ID
     * @return 模式配置
     */
    PointReadingModeConfig selectByDictAndBook(@Param("configDictId") Integer configDictId, @Param("bookId") Integer bookId);
}
