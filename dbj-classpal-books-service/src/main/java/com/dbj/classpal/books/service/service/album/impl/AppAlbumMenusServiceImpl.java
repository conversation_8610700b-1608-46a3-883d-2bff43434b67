package com.dbj.classpal.books.service.service.album.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dbj.classpal.books.common.bo.album.*;
import com.dbj.classpal.books.common.dto.album.AppAlbumMenusInfoDTO;
import com.dbj.classpal.books.common.dto.album.AppAlbumMenusTreeDTO;
import com.dbj.classpal.books.common.enums.AlbumMenusOrderEnum;
import com.dbj.classpal.books.common.enums.IsRootEnum;
import com.dbj.classpal.books.service.biz.album.IAppAlbumElementsBiz;
import com.dbj.classpal.books.service.biz.album.IAppAlbumMenusBiz;
import com.dbj.classpal.books.service.entity.album.AppAlbumElements;
import com.dbj.classpal.books.service.entity.album.AppAlbumMenus;
import com.dbj.classpal.books.service.service.album.IAppAlbumMenusService;
import com.dbj.classpal.books.service.service.album.IAppElementsBusinessRefService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppAlbumMenusServiceImpl
 * Date:     2025-04-14 17:08:42
 * Description: 表名： ,描述： 表
 */
@Service
public class AppAlbumMenusServiceImpl implements IAppAlbumMenusService {

    @Resource
    private IAppAlbumMenusBiz biz;
    @Resource
    private IAppAlbumElementsBiz elementBiz;
    @Resource
    private IAppElementsBusinessRefService refService;

    @Override
    public AppAlbumMenusTreeDTO getAllAlbumMenusTree(AppAlbumMenusQueryBO bo) {
        LambdaQueryWrapper<AppAlbumMenus> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppAlbumMenus::getIsRoot, IsRootEnum.IS_ROOT_YES.getCode());
        queryWrapper.eq(AppAlbumMenus::getAlbumType, bo.getAlbumType());
        //1. 查询根节点数据
        AppAlbumMenus one =  biz.getOne(queryWrapper);
        if (ObjectUtils.isEmpty(one)){
            return null;
        }
        AppAlbumMenusTreeDTO rootBean = new AppAlbumMenusTreeDTO();
        BeanUtil.copyProperties(one, rootBean);


        List<AppAlbumMenusInfoDTO> albumMenusInfoDTOList = biz.getAppAlbumMenusInfoByDeptIdGroup(bo);
        Map<Integer,Integer> albumMenusGroupMap = new HashMap<>(16);
        if(CollectionUtils.isNotEmpty(albumMenusInfoDTOList)){
            albumMenusGroupMap = albumMenusInfoDTOList.stream().collect(Collectors.toMap(AppAlbumMenusInfoDTO::getMenusId,AppAlbumMenusInfoDTO::getNum));
        }

        //2.查询所有分类列表
        Map<Integer, Integer> finalAlbumMenusGroupMap = albumMenusGroupMap;
        List<AppAlbumMenusTreeDTO> dirList = biz.lambdaQuery().eq(AppAlbumMenus::getAlbumType, bo.getAlbumType()).eq(AppAlbumMenus::getIsRoot,IsRootEnum.IS_ROOT_NO.getCode()).list().stream().map(d -> {
            AppAlbumMenusTreeDTO nodeBean = new AppAlbumMenusTreeDTO();
            BeanUtil.copyProperties(d, nodeBean);
            nodeBean.setAlbumCount(finalAlbumMenusGroupMap.getOrDefault(d.getId(),0));
            return nodeBean;
        }).collect(Collectors.toList());

        //3. 查询根节点下所有分类列表
        List<AppAlbumMenusTreeDTO> rootNodes = biz.lambdaQuery().orderByAsc(AppAlbumMenus::getOrderNum).eq(AppAlbumMenus::getAlbumType, bo.getAlbumType()).eq(AppAlbumMenus::getParentId, rootBean.getId()).list().stream().map(d -> {
            AppAlbumMenusTreeDTO nodeBean = new AppAlbumMenusTreeDTO();
            BeanUtil.copyProperties(d, nodeBean);
            nodeBean.setAlbumCount(finalAlbumMenusGroupMap.getOrDefault(d.getId(),0));
            return nodeBean;
        }).collect(Collectors.toList());

        // 3. 递归设置子节点
        rootNodes.forEach(root -> buildTree(root, dirList));
        rootBean.setChildren(rootNodes);
        rootBean.setAlbumCount(finalAlbumMenusGroupMap.getOrDefault(rootBean.getId(),0));
        return rootBean;
    }

    @Override
    public Boolean reNameAlbumMenus(AppAlbumMenusReNameBO bo) {
        return biz.lambdaUpdate().eq(AppAlbumMenus::getId, bo.getId()).set(AppAlbumMenus::getAlbumMenuName,bo.getAlbumMenuName()).update();
    }

    @Override
    public Boolean saveAlbumMenus(AppAlbumMenusSaveBO bo) {
        AppAlbumMenus appAlbumMenus = new AppAlbumMenus();
        BeanUtil.copyProperties(bo, appAlbumMenus);
        appAlbumMenus.setOrderNum(getMaxOrderNum(bo.getParentId())+1);
        return biz.save(appAlbumMenus);
    }

    @Override
    public Boolean deleteAlbumMenus(AppAlbumMenusDeleteBO bo) throws BusinessException {
        int childCount = biz.lambdaQuery().eq(AppAlbumMenus::getParentId, bo.getId()).count().intValue();
        if (childCount > 0) {
            throw new BusinessException(APP_ALBUM_MENUS_HAS_CHILD_DELETE_REF_FAIL_CODE,APP_ALBUM_MENUS_HAS_CHILD_DELETE_REF_FAIL_MSG);
        }
        int refCount = elementBiz.lambdaQuery().eq(AppAlbumElements::getAppAlbumMenuId, bo.getId()).count().intValue();
        if (refCount > 0) {
            throw new BusinessException(APP_ALBUM_MENUS_DELETE_REF_FAIL_CODE,APP_ALBUM_MENUS_DELETE_REF_FAIL_MSG);
        }
        Set<Integer> elementsIdSet = elementBiz.lambdaQuery().eq(AppAlbumElements::getAppAlbumMenuId, bo.getId()).list().stream().map(AppAlbumElements::getId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(elementsIdSet)) {
            elementBiz.removeBatchByIds(elementsIdSet);
        }
        AppAlbumMenus appAlbumMenus = new AppAlbumMenus();
        BeanUtil.copyProperties(bo, appAlbumMenus);
        return biz.removeById(appAlbumMenus);
    }

    @Override
    public Boolean resetAlbumMenusOrderNum(AppAlbumMenusBatchMoveBO bo) throws BusinessException {
        //当前分类下最大层级数+目标分类层级到根节点最大层级数<=5
        Integer childrenDepth = getChildrenDepth(bo.getId());
        Integer rootDepth = getRootDepth(bo.getParentId());
        if (childrenDepth+rootDepth>5) {
            throw new BusinessException(APP_ALBUM_MENUS_ORDER_COVER_MAX_FAIL_CODE,APP_ALBUM_MENUS_ORDER_COVER_MAX_FAIL_MSG);
        }
        LambdaQueryWrapper<AppAlbumMenus>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppAlbumMenus::getParentId, bo.getParentId());
        wrapper.orderByAsc(AppAlbumMenus::getOrderNum);
        //目标分类下子分类列表
        List<AppAlbumMenus> aimChildrenList = new LinkedList<>(biz.list(wrapper));
        List<Integer> idList = aimChildrenList.stream().map(AppAlbumMenus::getId).collect(Collectors.toList());
        //查询当前分类
        AppAlbumMenus byId = biz.getById(bo.getId());
        byId.setParentId(bo.getParentId());
        //如果目标分类下子分类列表为空
        if (CollectionUtils.isEmpty(aimChildrenList)) {
            byId.setOrderNum(1);
            return biz.updateById(byId);
        }else{
            if (idList.contains(byId.getId())) {
                aimChildrenList.remove(byId);
            }
            ListIterator<AppAlbumMenus> iterator = aimChildrenList.listIterator();
            while (iterator.hasNext()) {
                AppAlbumMenus current = iterator.next();
                if (current.getId().equals(bo.getAimId())) {
                    if (bo.getOrder().equals(AlbumMenusOrderEnum.ORDER_BEFORE.getCode())){
                        if (iterator.hasPrevious()){
                            iterator.previous(); // 回退到 "Bob" 前
                            iterator.add(byId); // 插入新对象
                        }else{
                            aimChildrenList.add(0,byId);
                        }
                    }else{
                        if (iterator.hasNext()){
                            iterator.next(); // 回退到 "Bob" 后
                            iterator.add(byId); // 插入新对象
                        }else{
                            aimChildrenList.add(byId);
                        }
                    }
                    break;
                }
            }
            //重新排序赋值
            for (int i = 0; i < aimChildrenList.size(); i++) {
                aimChildrenList.get(i).setOrderNum(i+1);
            }
            return biz.updateBatchById(aimChildrenList);
        }
    }

    @Override
    public Integer getChildrenDepth(Integer id) {
        return biz.getChildrenDepth(id);
    }

    @Override
    public Integer getRootDepth(Integer id) {
        return biz.getRootDepth(id);
    }

    @Override
    public Integer getMaxOrderNum(Integer id) {
        return biz.getMaxOrderNum(id);
    }

    private void buildTree(AppAlbumMenusTreeDTO parentNode, List<AppAlbumMenusTreeDTO> allNodes) {
        List<AppAlbumMenusTreeDTO> children = allNodes.stream().sorted(Comparator.comparing(AppAlbumMenusTreeDTO::getOrderNum))
                .filter(node ->  node.getParentId() != null && node.getParentId().equals(parentNode.getId()))
                .collect(Collectors.toList());
        if (!children.isEmpty()) {
            parentNode.setChildren(children);
            children.forEach(child -> buildTree(child, allNodes));
        }
    }
}
