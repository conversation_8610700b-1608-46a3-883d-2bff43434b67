package com.dbj.classpal.books.service.mapper.poem;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.poem.app.AncientPoemReciteAppUserAssessmentScorePageBO;
import com.dbj.classpal.books.client.dto.poem.app.AncientPoemReciteAppUserAssessmentScorePageDTO;
import com.dbj.classpal.books.service.entity.poem.AncientPoemReciteAppUserAssessmentScores;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dbj.classpal.framework.commons.request.PageInfo;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 古诗背诵评测得分 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
public interface AncientPoemReciteAppUserAssessmentScoresMapper extends BaseMapper<AncientPoemReciteAppUserAssessmentScores> {


    Page<AncientPoemReciteAppUserAssessmentScorePageDTO> pageAncientPoemReciteAppUserAssessmentScore(Page page,@Param("bo") AncientPoemReciteAppUserAssessmentScorePageBO ancientPoemReciteAppUserAssessmentScorePageBO);
}
