package com.dbj.classpal.books.service.service.evaluation.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.evaluation.AdminEvaluationUpdateOpenApiBO;
import com.dbj.classpal.books.common.bo.common.CommonIdBO;
import com.dbj.classpal.books.common.bo.evaluation.AdminEvaluationQueryBO;
import com.dbj.classpal.books.common.dto.evaluation.AdminEvaluationDetailQueryDTO;
import com.dbj.classpal.books.common.dto.evaluation.AdminEvaluationNodeQueryDTO;
import com.dbj.classpal.books.common.dto.evaluation.AdminEvaluationQueryDTO;
import com.dbj.classpal.books.common.enums.evaluation.EnvaluationOpenEnum;
import com.dbj.classpal.books.common.enums.studycenter.StudyModuleResourceTypeEnum;
import com.dbj.classpal.books.service.biz.evaluation.IAppEvaluationBiz;
import com.dbj.classpal.books.service.biz.evaluation.IAppEvaluationNodeBiz;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBiz;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBusinessRefBiz;
import com.dbj.classpal.books.service.biz.studycenter.IAppStudyModuleResourceRelBiz;
import com.dbj.classpal.books.service.entity.evaluation.AppEvaluation;
import com.dbj.classpal.books.service.entity.evaluation.AppEvaluationNode;
import com.dbj.classpal.books.service.entity.studycenter.AppStudyModuleResourceRel;
import com.dbj.classpal.books.service.service.evaluation.IAppEvaluationService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppEvaluationServiceImpl
 * Date:     2025-05-16 15:00:23
 * Description: 表名： ,描述： 表
 */
@Service
public class AppEvaluationServiceImpl implements IAppEvaluationService {

    @Resource
    private IAppEvaluationBiz biz;
    @Resource
    private IAppEvaluationNodeBiz nodeBiz;
    @Resource
    private IAppMaterialBusinessRefBiz materialBusinessRefBiz;
    @Resource
    private IAppMaterialBiz appMaterialBiz;
    @Resource
    private IAppStudyModuleResourceRelBiz appStudyModuleResourceRelBiz;


    @Override
    public Page<AdminEvaluationQueryDTO> pageInfo(PageInfo<AdminEvaluationQueryBO> pageRequest) {
        // 1. 查询分页数据
        Page<AppEvaluation> page = biz.pageInfo(pageRequest);
        // 2. 转换为VO
        return (Page<AdminEvaluationQueryDTO>) page.convert(this::convertToDTO);
    }

    @Override
    public AdminEvaluationDetailQueryDTO getDetail(CommonIdBO bo) {
        AppEvaluation evaluation = biz.getById(bo.getId());
        AdminEvaluationDetailQueryDTO vo = new AdminEvaluationDetailQueryDTO();
        BeanUtil.copyProperties(evaluation, vo);
        List<AppEvaluationNode> nodeList = nodeBiz.lambdaQuery().eq(AppEvaluationNode::getAppEvaluationId, evaluation.getId()).list();
        List<AdminEvaluationNodeQueryDTO> evaluationNodeList = nodeList.stream().map(d -> {
            AdminEvaluationNodeQueryDTO appEvaluationQueryDTO = new AdminEvaluationNodeQueryDTO();
            BeanUtil.copyProperties(d, appEvaluationQueryDTO);
            return appEvaluationQueryDTO;
        }).sorted(Comparator.comparingInt(AdminEvaluationNodeQueryDTO::getAppEvaluationOrder)).collect(Collectors.toList());
        vo.setEvaluationNodeList(evaluationNodeList);
        return vo;
    }

    @Override
    public Boolean updateOpen(AdminEvaluationUpdateOpenApiBO bo) throws BusinessException {
        boolean openFlag = true;
        Map<Integer, List<AppEvaluationNode>> refMap = nodeBiz.lambdaQuery().in(AppEvaluationNode::getAppEvaluationId, bo.getIds()).list().stream().collect(Collectors.groupingBy(AppEvaluationNode::getAppEvaluationId));
        if (refMap.isEmpty()) {
            openFlag = false;
        }
        for (Map.Entry<Integer, List<AppEvaluationNode>> entry : refMap.entrySet()) {
            if ((!bo.getIds().contains(entry.getKey())) || entry.getValue().size() < 3 || entry.getValue().size() > 6) {
                openFlag = false;
                break;
            }
        }
        if (!openFlag && bo.getIsOpen().equals(EnvaluationOpenEnum.EVALUATION_OPEN_YES.getCode())) {
            throw new BusinessException(APP_EVALUATION_OPEN_LIMIT_CODE,APP_EVALUATION_OPEN_LIMIT_MSG);
        }

        if (bo.getIsOpen().equals(EnvaluationOpenEnum.EVALUATION_OPEN_NO.getCode())){
            List<AppStudyModuleResourceRel> relList = appStudyModuleResourceRelBiz.lambdaQuery().eq(AppStudyModuleResourceRel::getResourceType, StudyModuleResourceTypeEnum.EVALUATION.getId()).in(AppStudyModuleResourceRel::getResourceId, bo.getIds()).list();
            if (!(CollectionUtils.isEmpty(relList))) {
                throw new BusinessException(APP_EVALUATION_CLOSE_REF_FAIL_CODE,APP_EVALUATION_CLOSE_REF_FAIL_MSG);
            }
        }
        return biz.lambdaUpdate().in(AppEvaluation::getId,bo.getIds()).set(AppEvaluation::getEvaluationOpen,bo.getIsOpen()).update();
    }

    private AdminEvaluationQueryDTO convertToDTO(AppEvaluation evaluation) {
        AdminEvaluationQueryDTO vo = new AdminEvaluationQueryDTO();
        BeanUtil.copyProperties(evaluation, vo);
        return vo;
    }
}
