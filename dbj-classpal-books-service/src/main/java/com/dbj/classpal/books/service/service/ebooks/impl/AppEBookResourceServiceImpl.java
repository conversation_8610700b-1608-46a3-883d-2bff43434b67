package com.dbj.classpal.books.service.service.ebooks.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookResourceBatchQueryBO;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookResourceQueryBO;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookResourceSaveBO;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookResourceDTO;
import com.dbj.classpal.books.service.biz.ebooks.IAppEBookResourceBiz;
import com.dbj.classpal.books.service.entity.product.AppEBookResource;
import com.dbj.classpal.books.service.mapper.ebooks.AppEBookResourceMapper;
import com.dbj.classpal.books.service.service.ebooks.IAppEBookResourceService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 单书资源 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-10
 */
@Slf4j
@Service
public class AppEBookResourceServiceImpl extends ServiceImpl<AppEBookResourceMapper, AppEBookResource> implements IAppEBookResourceService {

    @Resource
    private IAppEBookResourceBiz resourceBiz;

    @Override
    public Page<AppEBookResourceDTO> page(PageInfo<AppEBookResourceQueryBO> pageRequest) throws BusinessException {
        return resourceBiz.page(pageRequest);
    }

    @Override
    public Integer save(AppEBookResourceSaveBO saveBO) throws BusinessException {
        return resourceBiz.save(saveBO);
    }

    @Override
    public boolean saveBatch(List<AppEBookResourceSaveBO> saveBOList) throws BusinessException {
        return resourceBiz.saveBatch(saveBOList);
    }

    @Override
    public boolean delete(Integer id) throws BusinessException {
        return resourceBiz.delete(id);
    }

    @Override
    public boolean deleteByBookId(Integer bookId) throws BusinessException {
        return resourceBiz.deleteByBookId(bookId);
    }


    @Override
    public Page<AppEBookResourceDTO> pageImages(PageInfo<AppEBookResourceBatchQueryBO> pageRequest) throws BusinessException {
        return resourceBiz.pageImages(pageRequest);
    }
} 