package com.dbj.classpal.books.service.mapper.pointreading;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dbj.classpal.books.service.entity.pointreading.PointReadingBookBusinessRef;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 点读书业务关联 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Mapper
public interface PointReadingBookBusinessRefMapper extends BaseMapper<PointReadingBookBusinessRef> {

    /**
     * 根据点读书ID查询业务关联列表
     *
     * @param bookId 点读书ID
     * @return 业务关联列表
     */
    List<PointReadingBookBusinessRef> selectByBookId(@Param("bookId") Integer bookId);

    /**
     * 根据业务类型和业务ID查询关联的点读书列表
     *
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 业务关联列表
     */
    List<PointReadingBookBusinessRef> selectByBusiness(@Param("businessType") String businessType, @Param("businessId") Integer businessId);

    /**
     * 根据业务类型查询关联的点读书列表
     *
     * @param businessType 业务类型
     * @return 业务关联列表
     */
    List<PointReadingBookBusinessRef> selectByBusinessType(@Param("businessType") String businessType);

    /**
     * 查询业务关联的点读书数量
     *
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 关联数量
     */
    int countByBusiness(@Param("businessType") String businessType, @Param("businessId") Integer businessId);

    /**
     * 删除点读书的所有业务关联
     *
     * @param bookId 点读书ID
     * @return 删除数量
     */
    int deleteByBookId(@Param("bookId") Integer bookId);

    /**
     * 删除业务的所有点读书关联
     *
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 删除数量
     */
    int deleteByBusiness(@Param("businessType") String businessType, @Param("businessId") Integer businessId);
}
