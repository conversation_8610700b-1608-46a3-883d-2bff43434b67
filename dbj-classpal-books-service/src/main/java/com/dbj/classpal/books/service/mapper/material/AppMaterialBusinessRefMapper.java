package com.dbj.classpal.books.service.mapper.material;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.dto.material.AppCommonMediaDTO;
import com.dbj.classpal.books.client.dto.material.AppMaterialPathDTO;
import com.dbj.classpal.books.common.bo.common.CommonIdBO;
import com.dbj.classpal.books.common.bo.material.AppMaterialBusinessRefQueryBO;
import com.dbj.classpal.books.common.bo.material.AppMaterialBusinessRefQueryCommonBO;
import com.dbj.classpal.books.common.dto.material.AppMaterialBusinessRefDTO;
import com.dbj.classpal.books.common.dto.material.AppMaterialBusinessRefTypeCountDTO;
import com.dbj.classpal.books.service.entity.material.AppMaterialBusinessRef;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialMapper
 * Date:     2025-04-08 16:20:15
 * Description: 表名： ,描述： 表
 */
public interface AppMaterialBusinessRefMapper extends BaseMapper<AppMaterialBusinessRef> {
    /**
     * 查询关联引用列表
     * @param bo
     * @return
     */
    List<AppMaterialBusinessRefDTO> refBusinessList(AppMaterialBusinessRefQueryCommonBO bo);


    List<AppMaterialBusinessRefDTO> getBusinessList(@Param("businessIds") Collection<Integer> businessIds,
                                                    @Param("businessType")Integer businessType,
                                                    @Param("appMaterialType")List<Integer> appMaterialTypes);


    /**
     * 查询关联引用素材类型关联数量
     * @param bo
     * @return
     */
    List<AppMaterialBusinessRefTypeCountDTO> refBusinessTypeCount(@Param("bo")CommonIdBO bo);



    /**
     * 分页素材中心查询关联
     * @param bo
     * @return
     */
    Page<AppMaterialBusinessRefDTO> pageInfo(Page page,@Param("bo") AppMaterialBusinessRefQueryBO bo);

    /**
     * 获取通用的素材信息展示
     * @param businessIds
     * @param code
     * @return
     */
    List<AppCommonMediaDTO> getCommonBusinessList(@Param("businessIds") Collection<Integer> businessIds,@Param("businessType") int code);

}
