package com.dbj.classpal.books.service.biz.pointreading.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.client.enums.StatusEnum;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingChapterQueryBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingChapterSaveBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingChapterUpdateBO;
import com.dbj.classpal.books.common.constant.AppErrorCode;
import com.dbj.classpal.books.common.dto.pointreading.PointReadingChapterDTO;
import com.dbj.classpal.books.common.enums.PointReadingImageSourceTypeEnum;
import com.dbj.classpal.books.service.biz.pointreading.IPointReadingChapterBiz;
import com.dbj.classpal.books.service.biz.pointreading.IPointReadingMenuBiz;
import com.dbj.classpal.books.service.entity.pointreading.PointReadingChapter;
import com.dbj.classpal.books.service.entity.pointreading.PointReadingMenu;
import com.dbj.classpal.books.service.mapper.pointreading.PointReadingChapterMapper;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 点读书章节 业务实现类
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
@Service
public class PointReadingChapterBizImpl extends ServiceImpl<PointReadingChapterMapper, PointReadingChapter> implements IPointReadingChapterBiz {
    

    @Resource
    private IPointReadingMenuBiz pointReadingMenuBiz;

    @Override
    public Page<PointReadingChapterDTO> pageChapter(Page<PointReadingChapter> page, PointReadingChapterQueryBO queryBO) throws BusinessException {
        Page<PointReadingChapter> pagePage = this.getBaseMapper().selectPage(page, queryBO);
        
        Page<PointReadingChapterDTO> dtoPage = new Page<>();
        BeanUtil.copyProperties(pagePage, dtoPage);
        
        List<PointReadingChapterDTO> dtoList = new ArrayList<>();
        if (CollUtil.isNotEmpty(pagePage.getRecords())) {
            // 获取目录信息
            List<Integer> menuIds = pagePage.getRecords().stream()
                    .map(PointReadingChapter::getMenuId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            
            Map<Integer, PointReadingMenu> menuMap = CollUtil.isEmpty(menuIds) ? 
                    Map.of() : 
                    pointReadingMenuBiz.listByIds(menuIds)
                            .stream()
                            .collect(Collectors.toMap(PointReadingMenu::getId, menu -> menu));
            
            for (PointReadingChapter page1 : pagePage.getRecords()) {
                PointReadingChapterDTO dto = convertToDTO(page1);
                
                // 设置目录名称
                if (page1.getMenuId() != null) {
                    PointReadingMenu menu = menuMap.get(page1.getMenuId());
                    if (menu != null) {
                        dto.setMenuName(menu.getName());
                    }
                }
                
                dtoList.add(dto);
            }
        }
        
        dtoPage.setRecords(dtoList);
        return dtoPage;
    }

    @Override
    public PointReadingChapterDTO detail(Integer id) throws BusinessException {
        PointReadingChapter page = this.getById(id);
        if (page == null) {
            throw new BusinessException(AppErrorCode.POINT_READING_CHAPTER_NOT_EXIST_CODE, AppErrorCode.POINT_READING_CHAPTER_NOT_EXIST_MSG);
        }
        
        PointReadingChapterDTO dto = convertToDTO(page);
        
        // 设置目录名称
        if (page.getMenuId() != null) {
            PointReadingMenu menu = pointReadingMenuBiz.getById(page.getMenuId());
            if (menu != null) {
                dto.setMenuName(menu.getName());
            }
        }
        
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer savePage(PointReadingChapterSaveBO saveBO) throws BusinessException {
        // 验证目录是否存在
        PointReadingMenu menu = pointReadingMenuBiz.getById(saveBO.getMenuId());
        if (menu == null) {
            throw new BusinessException(AppErrorCode.POINT_READING_MENU_NOT_EXIST_CODE, AppErrorCode.POINT_READING_MENU_NOT_EXIST_MSG);
        }

        // 验证同目录下章节名称是否重复
//        LambdaQueryWrapper<PointReadingPage> wrapper = new LambdaQueryWrapper<>();
//        wrapper.eq(PointReadingPage::getMenuId, saveBO.getMenuId())
//                .eq(PointReadingPage::getName, saveBO.getName());
//
//        if (this.count(wrapper) > 0) {
//            throw new BusinessException(AppErrorCode.POINT_READING_CHAPTER_NAME_EXIST_CODE, "同目录下章节名称已存在");
//        }
        
        // 构建实体对象
        PointReadingChapter page = new PointReadingChapter();
        BeanUtil.copyProperties(saveBO, page);
        
        // 设置默认值
        if (page.getSortNum() == null) {
            page.setSortNum(getNextSortNum(saveBO.getMenuId()));
        }
        if (page.getStatus() == null) {
            page.setStatus(YesOrNoEnum.YES.getCode());
        }
        if (page.getImageSourceType() == null) {
            page.setImageSourceType(PointReadingImageSourceTypeEnum.POINT_READING_BOOK.getCode());
        }
        
        // 保存章节
        this.save(page);
        
        return page.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePage(PointReadingChapterUpdateBO updateBO) throws BusinessException {
        // 验证章节是否存在
        PointReadingChapter existPage = this.getById(updateBO.getId());
        if (existPage == null) {
            throw new BusinessException(AppErrorCode.POINT_READING_CHAPTER_NOT_EXIST_CODE, AppErrorCode.POINT_READING_CHAPTER_NOT_EXIST_MSG);
        }
        
        // 验证目录是否存在
        PointReadingMenu menu = pointReadingMenuBiz.getById(updateBO.getMenuId());
        if (menu == null) {
            throw new BusinessException(AppErrorCode.POINT_READING_MENU_NOT_EXIST_CODE, AppErrorCode.POINT_READING_MENU_NOT_EXIST_MSG);
        }

        // 验证同目录下章节名称是否重复
//        LambdaQueryWrapper<PointReadingPage> wrapper = new LambdaQueryWrapper<>();
//        wrapper.eq(PointReadingPage::getMenuId, updateBO.getMenuId())
//                .eq(PointReadingPage::getName, updateBO.getName())
//                .ne(PointReadingPage::getId, updateBO.getId());
//
//        if (this.count(wrapper) > 0) {
//            throw new BusinessException(AppErrorCode.POINT_READING_CHAPTER_NAME_EXIST_CODE, "同目录下章节名称已存在");
//        }
        
        // 构建更新对象
        PointReadingChapter page = new PointReadingChapter();
        BeanUtil.copyProperties(updateBO, page);
        
        return this.updateById(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePage(Integer id) throws BusinessException {
        // 验证章节是否存在
        PointReadingChapter page = this.getById(id);
        if (page == null) {
            throw new BusinessException(AppErrorCode.POINT_READING_CHAPTER_NOT_EXIST_CODE, AppErrorCode.POINT_READING_CHAPTER_NOT_EXIST_MSG);
        }
        
        return this.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBatch(List<Integer> ids) throws BusinessException {
        if (CollUtil.isEmpty(ids)) {
            return true;
        }
        
        return this.removeByIds(ids);
    }

    /**
     * 转换为DTO
     */
    private PointReadingChapterDTO convertToDTO(PointReadingChapter page) {
        PointReadingChapterDTO dto = new PointReadingChapterDTO();
        BeanUtil.copyProperties(page, dto);
        
        // 设置状态描述
        if (page.getStatus() != null) {
            dto.setStatusDesc(StatusEnum.getDescByCode(page.getStatus()));
        }

        // 设置图片来源类型描述
        if (page.getImageSourceType() != null) {
            dto.setImageSourceTypeDesc(PointReadingImageSourceTypeEnum.getDescByCode(page.getImageSourceType()));
        }

        return dto;
    }

    /**
     * 获取下一个排序号
     */
    private Integer getNextSortNum(Integer menuId) {
        LambdaQueryWrapper<PointReadingChapter> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PointReadingChapter::getMenuId, menuId)
                .orderByDesc(PointReadingChapter::getSortNum)
                .last("LIMIT 1");

        PointReadingChapter lastPage = this.getOne(wrapper);
        if (lastPage == null || lastPage.getSortNum() == null) {
            return 1;
        }

        return lastPage.getSortNum() + 1;
    }

    @Override
    public List<PointReadingChapterDTO> getChapterByMenu(Integer menuId) throws BusinessException {
        List<PointReadingChapter> pages = this.getBaseMapper().selectByMenuId(menuId);
        return pages.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<PointReadingChapterDTO> getChapterByBook(Integer bookId) throws BusinessException {
        // 先查询该点读书下的所有目录
        LambdaQueryWrapper<PointReadingMenu> menuWrapper = new LambdaQueryWrapper<>();
        menuWrapper.eq(PointReadingMenu::getBookId, bookId);
        List<PointReadingMenu> menus = pointReadingMenuBiz.list(menuWrapper);

        if (CollUtil.isEmpty(menus)) {
            return new ArrayList<>();
        }

        List<Integer> menuIds = menus.stream()
                .map(PointReadingMenu::getId)
                .collect(Collectors.toList());

        // 查询这些目录下的所有章节
        LambdaQueryWrapper<PointReadingChapter> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(PointReadingChapter::getMenuId, menuIds)
                .eq(PointReadingChapter::getStatus, YesOrNoEnum.YES.getCode())
                .orderByAsc(PointReadingChapter::getSortNum)
                .orderByAsc(PointReadingChapter::getId);

        List<PointReadingChapter> pages = this.list(wrapper);
        return pages.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSort(Integer id, Integer sortNum) throws BusinessException {
        // 验证章节是否存在
        PointReadingChapter page = this.getById(id);
        if (page == null) {
            throw new BusinessException(AppErrorCode.POINT_READING_CHAPTER_NOT_EXIST_CODE, AppErrorCode.POINT_READING_CHAPTER_NOT_EXIST_MSG);
        }

        LambdaUpdateWrapper<PointReadingChapter> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(PointReadingChapter::getId, id)
                .set(PointReadingChapter::getSortNum, sortNum);

        return this.update(wrapper);
    }

}
