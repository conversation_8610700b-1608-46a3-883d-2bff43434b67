package com.dbj.classpal.books.service.service.album;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.dto.album.AppAlbumElementsQueryApiDTO;
import com.dbj.classpal.books.common.bo.album.AppAlbumElementsQueryBO;
import com.dbj.classpal.books.common.bo.album.AppAlbumElementsSaveBO;
import com.dbj.classpal.books.common.bo.album.AppAlbumElementsUpdateBO;
import com.dbj.classpal.books.common.bo.album.AppAlbumElementsUpdateStatusBO;
import com.dbj.classpal.books.common.bo.album.AppAlbumElementsUpdateVisibleBO;
import com.dbj.classpal.books.common.bo.common.CommonIdBO;
import com.dbj.classpal.books.common.bo.common.CommonIdsBO;
import com.dbj.classpal.books.common.dto.album.AppAlbumElementsQueryDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppAlbumElementsService
 * Date:     2025-04-15 10:18:23
 * Description: 表名： ,描述： 表
 */
@Mapper
public interface IAppAlbumElementsService {

    /**
     * 查询专辑列表
     * @param appAlbumElementsQueryBO
     * @return
     * @throws BusinessException
     */
    List<AppAlbumElementsQueryDTO> getAppAlbumElementsList(AppAlbumElementsQueryBO appAlbumElementsQueryBO) throws BusinessException;


    /**
     * 分页查询专辑列表
     * @param pageRequest
     * @return
     */
    Page<AppAlbumElementsQueryDTO> pageInfo(PageInfo<AppAlbumElementsQueryBO> pageRequest) throws BusinessException;


    /**
     * 新增专辑
     * @param bo
     * @return
     */
    Boolean saveAppAlbumElements(AppAlbumElementsSaveBO bo) throws BusinessException;


    /**
     * 批量删除专辑
     * @param bo
     * @return
     */
    Boolean deleteAppAlbumElements(CommonIdsBO bo) throws BusinessException;


    /**
     * 获取单个专辑信息
     * @param bo
     * @return
     */
    AppAlbumElementsQueryDTO getAppAlbumElement(CommonIdBO bo);

    /**
     * 修改专辑标题
     * @param bo
     * @return
     */
    Boolean updateAppAlbumElement(AppAlbumElementsUpdateBO bo);


    /**
     * 修改专辑标题
     * @param bo
     * @return
     */
    Boolean updateAppAlbumElementTitle(AppAlbumElementsUpdateBO bo);


    /**
     * 修改专辑简介
     * @param bo
     * @return
     */
    Boolean updateAppAlbumElementRemark(AppAlbumElementsUpdateBO bo);

    /**
     * 修改专辑隐藏状态
     * @param bo
     * @return
     */
    Boolean updateAppAlbumElementVisible(AppAlbumElementsUpdateVisibleBO bo);

    /**
     * 修改专辑上架状态
     * @param bo
     * @return
     */
    Boolean updateAppAlbumElementStatus(AppAlbumElementsUpdateStatusBO bo);


    /**
     * 修改封面
     * @param bo
     * @return
     */
    Boolean updateAppAlbumElementCover(AppAlbumElementsUpdateBO bo);

    /**
     * 判断专辑id是否全部存在
     * @param ids
     * @return
     */
    Boolean allExistsByIds(Collection<Integer> ids);

    /**
     * 根据id集合查询专辑信息
     * @title: selectAppAlbumElementsListByIds
     * @description:
     * @author: subei
     * @date: 2025/7/18 9:43
     * @param ids
     * @return: java.util.List<com.dbj.classpal.books.common.dto.album.AppAlbumElementsQueryDTO>
     **/
    List<AppAlbumElementsQueryApiDTO> selectAppAlbumElementsListByIds(Collection<Integer> ids);
}
