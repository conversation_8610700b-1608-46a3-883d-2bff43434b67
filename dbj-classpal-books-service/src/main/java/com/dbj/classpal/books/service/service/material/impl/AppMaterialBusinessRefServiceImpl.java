package com.dbj.classpal.books.service.service.material.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.dto.material.AppCommonMediaDTO;
import com.dbj.classpal.books.common.bo.common.CommonIdBO;
import com.dbj.classpal.books.common.bo.common.CommonIdsBO;
import com.dbj.classpal.books.common.bo.material.*;
import com.dbj.classpal.books.common.dto.material.*;
import com.dbj.classpal.books.common.dto.books.BooksRefDirectDTO;
import com.dbj.classpal.books.client.enums.BusinessTypeEnum;
import com.dbj.classpal.books.client.enums.FileTypeEnum;
import com.dbj.classpal.books.client.enums.MaterialTypeEnum;
import com.dbj.classpal.books.common.enums.strategy.AppMaterialBusinessRefTypeEnum;
import com.dbj.classpal.books.service.biz.album.IAppAlbumElementsBiz;
import com.dbj.classpal.books.service.biz.books.IBooksInfoBiz;
import com.dbj.classpal.books.service.biz.books.IBooksRankInCodesContentsBiz;
import com.dbj.classpal.books.service.biz.evaluation.IAppEvaluationBiz;
import com.dbj.classpal.books.service.biz.evaluation.IAppEvaluationNodeBiz;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBiz;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBusinessRefBiz;
import com.dbj.classpal.books.service.biz.question.QuestionAnswerBiz;
import com.dbj.classpal.books.service.biz.question.QuestionBiz;
import com.dbj.classpal.books.service.entity.album.AppAlbumElements;
import com.dbj.classpal.books.service.entity.books.BooksInfo;
import com.dbj.classpal.books.service.entity.books.BooksRankInCodesContents;
import com.dbj.classpal.books.service.entity.material.AppMaterial;
import com.dbj.classpal.books.service.entity.material.AppMaterialBusinessRef;
import com.dbj.classpal.books.service.entity.question.Question;
import com.dbj.classpal.books.service.entity.question.QuestionAnswer;
import com.dbj.classpal.books.service.factory.CommonBusinessStrategyFactory;
import com.dbj.classpal.books.service.service.material.IAppMaterialBusinessRefService;
import com.dbj.classpal.books.service.strategy.business.handler.ICommonBusinessStrategyHandler;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppMaterialServiceImpl
 * Date:     2025-04-08 16:22:04
 * Description: 表名： ,描述： 表
 */
@Service
public class AppMaterialBusinessRefServiceImpl implements IAppMaterialBusinessRefService {

    @Autowired
    private IAppMaterialBusinessRefBiz business;
    @Autowired
    private IAppMaterialBiz materialBiz;
    @Autowired
    private IAppAlbumElementsBiz elementsBiz;
    @Autowired
    private IBooksInfoBiz booksInfoBiz;
    @Autowired
    private IBooksRankInCodesContentsBiz booksRankInCodesContentsBiz;
    @Autowired
    private QuestionBiz questionBiz;
    @Autowired
    private QuestionAnswerBiz questionAnswerBiz;
    @Resource
    private CommonBusinessStrategyFactory commonBusinessStrategyFactory;
    @Autowired
    private IAppEvaluationNodeBiz evaluationNodeBiz;
    @Autowired
    private IAppEvaluationBiz evaluationBiz;


    @Override
    public List<AppMaterialBusinessRefDTO> beRefBusinessList(AppMaterialBusinessRefQueryCommonBO bo) {
        Map<Integer, List<AppMaterialBusinessRefDTO>> typeMap = business.refBusinessList(bo).stream().collect(Collectors.groupingBy(AppMaterialBusinessRefDTO::getBusinessType));
        for (Map.Entry<Integer, List<AppMaterialBusinessRefDTO>> integerListEntry : typeMap.entrySet()) {
            Integer businessType = integerListEntry.getKey();
            List<AppMaterialBusinessRefDTO> businessRefDTOList = integerListEntry.getValue();
            AppMaterialBusinessRefTypeEnum businessRefTypeEnum = AppMaterialBusinessRefTypeEnum.getByCode(businessType);
            if (ObjectUtil.isNotEmpty(businessRefTypeEnum)) {
                businessRefDTOList.stream().peek(d -> {
                    d.setBusinessTypeStr(Objects.requireNonNull(BusinessTypeEnum.getByCode(d.getBusinessType())).getName());
                    if (d.getAppMaterialType() != null) {
                        if (d.getAppMaterialType().equals(MaterialTypeEnum.FILE_TYPE_DOC.getCode())){
                            d.setAppMaterialTypeStr(MaterialTypeEnum.getByCode(d.getAppMaterialType()).getDesc()+"("+d.getMaterialExtension()+")");
                            FileTypeEnum fileTypeEnum = FileTypeEnum.getByCode(d.getMaterialExtension());
                            if (fileTypeEnum != null){
                                d.setMaterialIcon(fileTypeEnum.getIcon());
                            }
                        }else{
                            d.setAppMaterialTypeStr(MaterialTypeEnum.getByCode(d.getAppMaterialType()).getDesc());
                            d.setMaterialIcon(MaterialTypeEnum.getByCode(d.getAppMaterialType()).getIcon());
                        }
                    }
                }).collect(Collectors.toList());
                typeMap.put(businessType, businessRefDTOList);
            }
        }
        return typeMap.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }

    @Override
    public List<AppMaterialBusinessRefDTO> refBusinessList(AppMaterialBusinessRefQueryCommonBO bo) {

        Map<Integer, List<AppMaterialBusinessRefDTO>> typeMap = business.refBusinessList(bo).stream().collect(Collectors.groupingBy(AppMaterialBusinessRefDTO::getBusinessType));

        for (Map.Entry<Integer, List<AppMaterialBusinessRefDTO>> integerListEntry : typeMap.entrySet()) {
            Integer businessType = integerListEntry.getKey();
            List<AppMaterialBusinessRefDTO> businessRefDTOList = integerListEntry.getValue();
            List<Integer> businessIdList = businessRefDTOList.stream().map(AppMaterialBusinessRefDTO::getBusinessId).collect(Collectors.toList());
            AppMaterialBusinessRefTypeEnum businessRefTypeEnum = AppMaterialBusinessRefTypeEnum.getByCode(businessType);
            if (ObjectUtil.isNotEmpty(businessRefTypeEnum)) {
                ICommonBusinessStrategyHandler strategy = commonBusinessStrategyFactory.getStrategy(businessRefTypeEnum.getStrategy());
                Map<Integer, String> nameMap = strategy.getBusinessName(businessIdList);
                businessRefDTOList.stream().peek(d -> {
                    if (nameMap.containsKey(d.getBusinessId())){
                        d.setBusinessName(nameMap.get(d.getBusinessId()));
                    }
                    d.setBusinessTypeStr(Objects.requireNonNull(BusinessTypeEnum.getByCode(d.getBusinessType())).getName());
                    if (d.getAppMaterialType() != null) {
                        if (d.getAppMaterialType().equals(MaterialTypeEnum.FILE_TYPE_DOC.getCode())){
                            d.setAppMaterialTypeStr(MaterialTypeEnum.getByCode(d.getAppMaterialType()).getDesc()+"("+d.getMaterialExtension()+")");
                            FileTypeEnum fileTypeEnum = FileTypeEnum.getByCode(d.getMaterialExtension());
                            if (fileTypeEnum != null){
                                d.setMaterialIcon(fileTypeEnum.getIcon());
                            }
                        }else{
                            d.setAppMaterialTypeStr(MaterialTypeEnum.getByCode(d.getAppMaterialType()).getDesc());
                            d.setMaterialIcon(MaterialTypeEnum.getByCode(d.getAppMaterialType()).getIcon());
                        }
                    }
                }).collect(Collectors.toList());
                typeMap.put(businessType, businessRefDTOList);
            }
        }
        return typeMap.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }

    @Override
    public List<AppMaterialBusinessRefTypeCountDTO> getMaterialBusinessRefTypeCount(CommonIdBO bo) {
        return business.refBusinessTypeCount(bo);
    }

    @Override
    public Boolean saveAppMaterialBusinessRef(AppMaterialBusinessRefSaveBO bo) throws BusinessException {
        //判断是否存在重复的资源id
        boolean b = hasDuplicates(bo.getAppMaterialIds());
        if (b){
            throw new BusinessException(APP_ALBUM_ELEMENTS_REF_SAVE_SAME_FAIL_CODE,APP_ALBUM_ELEMENTS_REF_SAVE_SAME_FAIL_MSG);
        }
        //1. 查询专辑关联资源列表
        List<AppMaterialBusinessRef> list = business.lambdaQuery().orderByDesc(AppMaterialBusinessRef::getBusinessType).eq(AppMaterialBusinessRef::getBusinessId, bo.getBusinessId()).eq(AppMaterialBusinessRef::getBusinessType, bo.getBusinessType()).list();
        //2. 查询新增的资源是否存在
        List<Integer> appMaterialIds = bo.getAppMaterialIds().stream().toList();
        List<AppMaterialBusinessRef> saveList =  materialBiz.lambdaQuery().in(AppMaterial::getId,appMaterialIds).list().stream().map(d -> {
            AppMaterialBusinessRef businessRef = new AppMaterialBusinessRef();
            businessRef.setBusinessId(bo.getBusinessId());
            businessRef.setBusinessType(bo.getBusinessType());
            businessRef.setAppMaterialId(d.getId());
            businessRef.setBusinessName(d.getMaterialName());
            if (list.size()>0){
                businessRef.setOrderNum(list.get(0).getOrderNum()+1);
            }else{
                businessRef.setOrderNum(1);
            }
            return businessRef;
        }).collect(Collectors.toList());
        Set<Integer> collect = saveList.stream().map(d -> d.getAppMaterialId()).collect(Collectors.toSet());
        List<String> originList = materialBiz.listByIds(collect).stream().filter(d -> FileTypeEnum.isDoc(d.getMaterialOriginUrl())).map(d -> d.getMaterialOriginUrl()).collect(Collectors.toList());
        if (!originList.isEmpty() && !FileTypeEnum.isOnlyPdf(originList)){
            throw new BusinessException(BOOK_SAVE_MATERIAL_ONLY_PDF_CODE,BOOK_SAVE_MATERIAL_ONLY_PDF_MSG);
        }
        return business.saveBatch(saveList);
    }

    @Override
    public Boolean reNameAppMaterialBusinessRef(AppMaterialBusinessRefReNameBO bo) throws BusinessException {
        return business.lambdaUpdate().eq(AppMaterialBusinessRef::getId,bo.getId()).set(AppMaterialBusinessRef::getBusinessName,bo.getBusinessName()).update();
    }

    @Override
    public Boolean removeAppMaterialBusinessRef(CommonIdBO bo) throws BusinessException {
        return business.removeById(bo.getId());
    }

    @Override
    public Boolean removeBatchAppMaterialBusinessRef(CommonIdsBO bo) throws BusinessException {
        return business.removeByIds(bo.getIds());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean changeAppMaterialBusinessRefOrderNum(CommonIdsBO bo) throws BusinessException {
        List<Integer> ids = bo.getIds();
        Map<Integer,Integer>sortedMap = new HashMap<>();
        for (int i = 0; i < ids.size(); i++) {
            sortedMap.put(ids.get(i),i+1);
        }
        List<AppMaterialBusinessRef> updateList = business.lambdaQuery().in(AppMaterialBusinessRef::getId, ids).list().stream().peek(d -> {
            if (sortedMap.containsKey(d.getId())) {
                d.setOrderNum(sortedMap.get(d.getId()));
            }
        }).collect(Collectors.toList());
        return business.updateBatchById(updateList);
    }

    public static boolean hasDuplicates(List<?> list) {
        Set<Object> seen = new HashSet<>();
        for (Object item : list) {
            if (!seen.add(item)) {
                return true;
            }
        }
        return false;
    }


    @Override
    public Page<AppMaterialBusinessRefDTO> pageInfo(PageInfo<AppMaterialBusinessRefQueryBO> bo) {
        return business.pageInfo(bo);
    }

    @Override
    public AppMaterialBusinessRefAlbumDirectDTO getAppMaterialBusinessRefAlbum(CommonIdBO bo) throws BusinessException {
        //查询素材关联专辑数据
        AppMaterialBusinessRef materialBusinessRef = business.getById(bo.getId());
        if (ObjectUtils.isEmpty(materialBusinessRef)) {
            throw new BusinessException(APP_MATERIAL_REF_NOT_EXIST_FAIL_CODE,APP_MATERIAL_REF_NOT_EXIST_FAIL_MSG);
        }
        //查询专辑详情信息
        AppAlbumElements elements = elementsBiz.getById(materialBusinessRef.getBusinessId());
        if (ObjectUtils.isEmpty(elements)) {
            throw new BusinessException(APP_ALBUM_ELEMENTS_NOT_EXIST_FAIL_CODE,APP_ALBUM_ELEMENTS_NOT_EXIST_FAIL_MSG);
        }
        AppMaterialBusinessRefAlbumDirectDTO directDTO = new AppMaterialBusinessRefAlbumDirectDTO();
        directDTO.setMenuId(elements.getAppAlbumMenuId());
        directDTO.setBusinessId(materialBusinessRef.getBusinessId());
        directDTO.setBusinessType(materialBusinessRef.getBusinessType());
        return directDTO;
    }

    @Override
    public BooksRefDirectDTO getAppMaterialBusinessRefBooks(CommonIdBO bo) throws BusinessException {
        //查询素材关联图书资源数据
        AppMaterialBusinessRef materialBusinessRef = business.getById(bo.getId());
        if (ObjectUtils.isEmpty(materialBusinessRef)) {
            throw new BusinessException(APP_MATERIAL_REF_NOT_EXIST_FAIL_CODE,APP_MATERIAL_REF_NOT_EXIST_FAIL_MSG);
        }
        //查询图书详情信息
        BooksRankInCodesContents booksRankInCodesContents = booksRankInCodesContentsBiz.getById(materialBusinessRef.getBusinessId());
        if (ObjectUtils.isEmpty(booksRankInCodesContents)) {
            throw new BusinessException(BOOK_INFO_NOT_EXIST_CODE,BOOK_INFO_NOT_EXIST_MSG);
        }
        BooksRefDirectDTO directDTO = new BooksRefDirectDTO();
        directDTO.setBooksId(booksRankInCodesContents.getBooksId());
        directDTO.setRankId(booksRankInCodesContents.getRankId());
        directDTO.setRankClassifyId(booksRankInCodesContents.getRankClassifyId());
        directDTO.setBusinessId(booksRankInCodesContents.getId());
        BooksInfo byId = booksInfoBiz.getById(booksRankInCodesContents.getBooksId());
        if (!ObjectUtils.isEmpty(byId)) {
            directDTO.setBookName(byId.getBookName());
        }
        return directDTO;
    }

    @Override
    public AppMaterialBusinessRefQuestionDirectDTO getAppMaterialBusinessRefQuestion(CommonIdBO bo) throws BusinessException {
        //查询素材关联图书管理-图库数据
        AppMaterialBusinessRef materialBusinessRef = business.getById(bo.getId());
        if (ObjectUtils.isEmpty(materialBusinessRef)) {
            throw new BusinessException(APP_MATERIAL_REF_NOT_EXIST_FAIL_CODE,APP_MATERIAL_REF_NOT_EXIST_FAIL_MSG);
        }
        
        Integer questionId = null;
        // 判断业务类型
        if (Objects.equals(BusinessTypeEnum.ANSWER_MEDIA_BUSINESS.getCode(), materialBusinessRef.getBusinessType())) {
            // 如果是选项媒体引用，先通过 answerID 查询 questionID
            QuestionAnswer answer = questionAnswerBiz.getById(materialBusinessRef.getBusinessId());
            if (ObjectUtils.isEmpty(answer)) {
                return null;
            }
            questionId = answer.getQuestionId();
        } else if (Objects.equals(BusinessTypeEnum.QUESTION_MEDIA_BUSINESS.getCode(), materialBusinessRef.getBusinessType()) ||
                  Objects.equals(BusinessTypeEnum.QUESTION_RECOGNITION_BUSINESS.getCode(), materialBusinessRef.getBusinessType())) {
            // 如果是题目媒体引用或辅助识图，直接使用 businessId 作为 questionId
            questionId = materialBusinessRef.getBusinessId();
        } else {
            return null;
        }
        
        //查询题目详情信息
        Question question = questionBiz.getById(questionId);
        if (ObjectUtils.isEmpty(question)) {
            throw new BusinessException(APP_QUESTION_NOT_EXIST_CODE,APP_QUESTION_NOT_EXIST_MSG);
        }
        
        AppMaterialBusinessRefQuestionDirectDTO directDTO = new AppMaterialBusinessRefQuestionDirectDTO();
        directDTO.setQuestionCategoryId(question.getQuestionCategoryId());
        directDTO.setBusinessId(question.getId());
        directDTO.setBusinessType(materialBusinessRef.getBusinessType());
        directDTO.setTitle(question.getTitle());
        directDTO.setQuestionId(questionId);
        return directDTO;
    }

    @Override
    public List<AppCommonMediaDTO> getCommonBusinessList(Collection<Integer> businessIds, BusinessTypeEnum typeEnum) {
        return business.getCommonBusinessList(businessIds, typeEnum);
    }
}
