//package com.dbj.classpal.books.service.mapper.config;
//
//import com.baomidou.mybatisplus.core.mapper.BaseMapper;
//import com.dbj.classpal.books.service.entity.config.ContentConfigCategory;
//import org.apache.ibatis.annotations.Param;
//import org.apache.ibatis.annotations.Select;
//
//public interface ContentConfigCategoryMapper extends BaseMapper<ContentConfigCategory> {
//
//    /**
//     * 检查名称是否存在
//     */
//    @Select("<script>" +
//            "SELECT COUNT(*) > 0 FROM content_config_category " +
//            "WHERE name = #{name} " +
//            "AND config_type = #{type} " +
//            "AND is_deleted = 0 " +
//            "<if test='id != null'> AND id != #{id} </if>" +
//            "</script>")
//    boolean existsByName(@Param("configName") String name,
//                        @Param("configType") Integer type,
//                        @Param("id") Integer id);
//}