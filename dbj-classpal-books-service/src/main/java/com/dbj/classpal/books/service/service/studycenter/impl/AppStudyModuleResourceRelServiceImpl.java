package com.dbj.classpal.books.service.service.studycenter.impl;

import com.dbj.classpal.books.common.bo.studycenter.AppStudyModuleResourceRelCreateBO;
import com.dbj.classpal.books.common.bo.studycenter.AppStudyModuleResourceRelUpdateBO;
import com.dbj.classpal.books.common.constant.AppErrorCode;
import com.dbj.classpal.books.common.dto.studycenter.AppStudyModuleResourceRelDetailDTO;
import com.dbj.classpal.books.common.enums.studycenter.StudyModuleResourceTypeEnum;
import com.dbj.classpal.books.service.biz.studycenter.IAppStudyModuleResourceRelBiz;
import com.dbj.classpal.books.service.service.studycenter.IAppStudyModuleResourceRelService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class AppStudyModuleResourceRelServiceImpl implements IAppStudyModuleResourceRelService {
    @Resource
    private IAppStudyModuleResourceRelBiz appStudyModuleResourceRelBiz;

    @Override
    public Boolean create(AppStudyModuleResourceRelCreateBO bo) throws BusinessException {
        log.info("创建资源关联 入参: {}", bo);
        validateRequiredFields(bo);
        Boolean result = appStudyModuleResourceRelBiz.create(bo);
        log.info("创建资源关联 返回: {}", result);
        return result;
    }

    @Override
    public Boolean update(AppStudyModuleResourceRelUpdateBO bo) throws BusinessException {
        log.info("修改资源关联 入参: {}", bo);
        validateRequiredFields(bo);
        if (bo.getId() == null) {
            throw new BusinessException(AppErrorCode.ID_NOT_NULL_CODE, AppErrorCode.ID_NOT_NULL_MSG);
        }
        Boolean result = appStudyModuleResourceRelBiz.update(bo);
        log.info("修改资源关联 返回: {}", result);
        return result;
    }

    @Override
    public Boolean delete(List<Integer> ids) throws BusinessException {
        log.info("删除资源关联 入参: {}", ids);
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException(AppErrorCode.ID_LIST_NOT_EMPTY_CODE, AppErrorCode.ID_LIST_NOT_EMPTY_MSG);
        }
        Boolean result = appStudyModuleResourceRelBiz.delete(ids);
        log.info("删除资源关联 返回: {}", result);
        return result;
    }


    @Override
    public AppStudyModuleResourceRelDetailDTO detail(Integer id) throws BusinessException {
        log.info("查询资源关联详情 入参: {}", id);
        if (id == null) {
            throw new BusinessException(AppErrorCode.ID_NOT_NULL_CODE, AppErrorCode.ID_NOT_NULL_MSG);
        }
        AppStudyModuleResourceRelDetailDTO result = appStudyModuleResourceRelBiz.detail(id);
        log.info("查询资源关联详情 返回: {}", result);
        return result;
    }
    private void validateRequiredFields(AppStudyModuleResourceRelCreateBO bo) throws BusinessException {
        if (bo == null) {
            throw new BusinessException(AppErrorCode.PARAM_NOT_NULL_CODE, AppErrorCode.PARAM_NOT_NULL_MSG);
        }
        if (bo.getModuleId() == null) {
            throw new BusinessException(AppErrorCode.APP_RESOURCE_REL_MODULE_ID_REQUIRED_CODE, AppErrorCode.APP_RESOURCE_REL_MODULE_ID_REQUIRED_MSG);
        }
        if (bo.getResourceId() == null) {
            throw new BusinessException(AppErrorCode.APP_RESOURCE_REL_RESOURCE_ID_REQUIRED_CODE, AppErrorCode.APP_RESOURCE_REL_RESOURCE_ID_REQUIRED_MSG);
        }
        if (bo.getResourceType() == null) {
            throw new BusinessException(AppErrorCode.APP_RESOURCE_REL_RESOURCE_TYPE_REQUIRED_CODE, AppErrorCode.APP_RESOURCE_REL_RESOURCE_TYPE_REQUIRED_MSG);
        }
        if (isSingleResourceType(bo.getResourceType()) && !isSingleResourceSelected(bo.getResourceId())) {
            throw new BusinessException(AppErrorCode.APP_RESOURCE_REL_SINGLE_RESOURCE_LIMIT_CODE, AppErrorCode.APP_RESOURCE_REL_SINGLE_RESOURCE_LIMIT_MSG);
        }
    }

    private boolean isSingleResourceType(Integer resourceType) {
        return StudyModuleResourceTypeEnum.AUDIO_ALBUM.getId().equals(resourceType) || StudyModuleResourceTypeEnum.VIDEO_ALBUM.getId().equals(resourceType) || StudyModuleResourceTypeEnum.EVALUATION.getId().equals(resourceType);
    }
    private boolean isSingleResourceSelected(Integer resourceId) {
        return resourceId != null;
    }
} 