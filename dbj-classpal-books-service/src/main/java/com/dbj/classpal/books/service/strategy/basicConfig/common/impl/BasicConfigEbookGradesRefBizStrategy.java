package com.dbj.classpal.books.service.strategy.basicConfig.common.impl;

import com.dbj.classpal.books.service.biz.ebooks.IAppEBookBiz;
import com.dbj.classpal.books.service.strategy.basicConfig.handler.IBasicConfigCommonBusinessStrategyHandler;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.dbj.classpal.books.common.constant.AppErrorCode.APP_EBOOKS_CONFIG_GRADE_EXIST_REF_FAILED_CODE;
import static com.dbj.classpal.books.common.constant.AppErrorCode.APP_EBOOKS_CONFIG_GRADE_EXIST_REF_FAILED_MSG;

@Service("basicConfigEbookGradesRefBizStrategy")
public class BasicConfigEbookGradesRefBizStrategy extends IBasicConfigCommonBusinessStrategyHandler {

    @Resource
    private IAppEBookBiz appEBookBiz;

    @Override
    public Map<Integer, Long> getRefMap(List<Integer> ids) {
        Map<Integer, Long> refMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> {
                Long refCount = appEBookBiz.lambdaQuery().apply("EXISTS (SELECT 1 FROM dual WHERE FIND_IN_SET({0}, applicable_grades))", id).count();
                refMap.put(id,refCount);
            });
        }
        return refMap;
    }

    @Override
    public void throwException(String type) throws BusinessException {
        throw new BusinessException(APP_EBOOKS_CONFIG_GRADE_EXIST_REF_FAILED_CODE,APP_EBOOKS_CONFIG_GRADE_EXIST_REF_FAILED_MSG);
    }
}
