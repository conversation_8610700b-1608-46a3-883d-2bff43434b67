package com.dbj.classpal.books.service.strategy.business.common.impl;

import com.dbj.classpal.books.service.biz.books.IBooksInfoBiz;
import com.dbj.classpal.books.service.biz.books.IBooksRankInCodesContentsBiz;
import com.dbj.classpal.books.service.entity.books.BooksInfo;
import com.dbj.classpal.books.service.entity.books.BooksRankInCodesContents;
import com.dbj.classpal.books.service.strategy.business.handler.ICommonBusinessStrategyHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppAlbumRefMaterialStrategy
 * Date:     2025-05-09 13:07:37
 * Description: 表名： ,描述： 表
 */
@Service("appBooksInCodeRefMaterialStrategy")
public class AppBooksInCodeRefMaterialStrategy extends ICommonBusinessStrategyHandler {

    @Resource
    private IBooksRankInCodesContentsBiz rankInCodesContentsBiz;
    @Resource
    private IBooksInfoBiz booksInfoBiz;


    @Override
    public Map<Integer, String> getBusinessName(List<Integer> businessId) {
        Map<Integer, String> map = new HashMap<>();
        List<BooksRankInCodesContents> appAlbumElements = rankInCodesContentsBiz.listByIds(businessId);
        if(CollectionUtils.isNotEmpty(appAlbumElements)){
            Set<Integer> bookIds = appAlbumElements.stream().map(d -> d.getBooksId()).collect(Collectors.toSet());
            Map<Integer, BooksInfo> booksInfoMap =  booksInfoBiz.listByIds(bookIds).stream()
                    // 过滤空对象或空键（避免NullPointerException）
                    .filter(obj -> obj != null && obj.getId() != null)
                    // 转换为Map：key=id, value=对象本身
                    .collect(Collectors.toMap(
                            BooksInfo::getId,       // 键提取函数
                            Function.identity(),  // 值提取函数（对象自身）
                            (oldVal, newVal) -> newVal // 键冲突时保留新值
               ));
            for (BooksRankInCodesContents booksRankInCodesContents : appAlbumElements) {
                if (booksInfoMap.containsKey(booksRankInCodesContents.getBooksId())) {
                    map.put(booksRankInCodesContents.getId(),booksInfoMap.get(booksRankInCodesContents.getBooksId()).getBookName());
                }
            }
        }
        return map;
    }
}
