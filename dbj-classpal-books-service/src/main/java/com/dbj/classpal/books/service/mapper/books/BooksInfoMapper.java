package com.dbj.classpal.books.service.mapper.books;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.books.BooksInfoPageBO;
import com.dbj.classpal.books.client.dto.books.BooksInfoPageApiDTO;
import com.dbj.classpal.books.service.entity.books.BooksInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 图书表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Mapper
public interface BooksInfoMapper extends BaseMapper<BooksInfo> {

    /**
     * 分页查询
     * @param pageRequest
     * @return
     */
    Page<BooksInfoPageApiDTO> pageInfo(Page page, @Param("bo") BooksInfoPageBO pageRequest);

}
