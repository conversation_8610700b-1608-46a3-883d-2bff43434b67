package com.dbj.classpal.books.service.entity.product;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;

/**
 * <p>
 * 商品权益范围
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("product_entitlement_scopes")
@Schema(name="ProductEntitlementScopes对象", description="商品权益范围")
public class ProductEntitlementScopes extends BizEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "商品配置ID (关联 product_sales_settings.id)")
    private Integer configId;

    @Schema(description = "权益范围类型 (例如: 'BOOK_IN_CODES', 'AUDIO_ALBUM', 'VIDEO_ALBUM', 'POINT_READ')")
    private String scopeType;

    @Schema(description = "试学模式: 0-按资源类型 1-指定数量试学")
    private Boolean trialMode;

    @Schema(description = "试学数量/个数")
    private Integer trialCount;

    @Schema(description = "资源类型（多个英文逗号分隔）")
    private String resourceTypes;

    @Schema(description = "状态 0-正常 1-停用")
    private Boolean status;


}
