package com.dbj.classpal.books.service.factory;

import com.dbj.classpal.books.service.strategy.business.handler.ICommonBusinessStrategyHandler;
import com.dbj.classpal.framework.utils.util.SpringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: MaterialBusinessStartegyFactory
 * Date:     2025-05-09 13:17:20
 * Description: 表名： ,描述： 表
 */
@Component
public class CommonBusinessStrategyFactory {

    public ICommonBusinessStrategyHandler getStrategy(String handeler) {
        return  SpringUtils.getBean(handeler);
    }
}
