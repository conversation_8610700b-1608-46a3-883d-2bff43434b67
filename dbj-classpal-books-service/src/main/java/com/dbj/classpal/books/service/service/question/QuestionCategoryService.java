package com.dbj.classpal.books.service.service.question;

import com.dbj.classpal.books.common.bo.question.*;
import com.dbj.classpal.books.common.dto.question.QuestionCategoryDTO;
import com.dbj.classpal.books.common.dto.question.QuestionCategoryRefDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;

import java.util.List;

/**
 * 题目分类服务接口
 */
public interface QuestionCategoryService {

    /**
     * 获取分类树
     *
     * @param queryBO 查询参数
     * @return 分类树列表
     */
    List<QuestionCategoryDTO> getCategoryTree(QuestionCategoryQueryBO queryBO);

    /**
     * 获取分类详情（包含子分类）
     *
     * @param idBO 分类ID
     * @return 分类详情
     */
    QuestionCategoryDTO getCategory(QuestionCategoryIdBO idBO);

    /**
     * 创建分类
     *
     * @param categoryBO 分类信息
     * @return 分类ID
     * @throws BusinessException 业务异常
     */
    Integer createCategory(QuestionCategoryBO categoryBO) throws BusinessException;

    /**
     * 更新分类
     *
     * @param categoryBO 分类信息
     * @throws BusinessException 业务异常
     */
    void updateCategory(QuestionCategoryBO categoryBO) throws BusinessException;

    /**
     * 批量删除分类
     *
     * @param idsBO 分类ID列表
     * @throws BusinessException 业务异常
     */
    void batchDeleteCategory(QuestionCategoryIdsBO idsBO) throws BusinessException;

    /**
     * 更新分类排序
     *
     * @param sortBO 排序信息
     * @throws BusinessException 业务异常
     */
    void updateSort(QuestionCategorySortBO sortBO) throws BusinessException;

    /**
     * 检查分类名称是否重复
     *
     * @param name      分类名称
     * @param parentId  父级ID
     * @param excludeId 排除的ID
     * @return true: 重复, false: 不重复
     */
    boolean checkNameExists(String name, Integer parentId, Integer excludeId);

    List<QuestionCategoryRefDTO> getBusinessRefs(QuestionCategoryIdQueryBO queryBO);
}