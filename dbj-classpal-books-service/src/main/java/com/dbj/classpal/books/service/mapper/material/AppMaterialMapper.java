package com.dbj.classpal.books.service.mapper.material;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.dto.material.AppMaterialPathDTO;
import com.dbj.classpal.books.common.bo.material.AppMaterialIOBO;
import com.dbj.classpal.books.common.bo.material.AppMaterialQueryBO;
import com.dbj.classpal.books.common.dto.material.AppMaterialQueryDTO;
import com.dbj.classpal.books.common.dto.material.AppMaterialStatisticsSizeDTO;
import com.dbj.classpal.books.service.entity.material.AppMaterial;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialMapper
 * Date:     2025-04-08 16:20:15
 * Description: 表名： ,描述： 表
 */
public interface AppMaterialMapper extends BaseMapper<AppMaterial> {

    /**
     * 分页查询素材中心
     * @param page
     * @param appMaterialQueryBO
     * @return
     */
    Page<AppMaterial> pageTree(Page page,@Param("bo")AppMaterialQueryBO appMaterialQueryBO);

    /**
     * 查询该文件夹父节点列表
     * @param id
     * @return
     */
    List<AppMaterial> getMaterialParentsPath(@Param("id")Integer id);


    /**
     * 查询该文件夹父节点拼接文件名
     * @param id
     * @return
     */
    String getMaterialParentsNames(@Param("id")Integer id);

    /**
     * 查询所有子节点，并判断目标ID是否存在
     * @param ioBo
     * @return
     */
    Integer checkAimIdInChildren(@Param("bo")AppMaterialIOBO ioBo);

    /**
     * 获取当前文件夹下所有文件
     * @return
     */
    List<AppMaterial> getChildrenFiles(@Param("id")Integer id);


    /**
     * 获取多个文件夹下所有文件
     * @return
     */
    List<AppMaterial> getBatchDirChildrenFiles(@Param("ids")List<Integer>ids);


    /**
     * 查询总时长
     * @param idSet
     * @return
     */
    Integer sumDuration(@Param("idSet")Set<Integer> idSet);

    List<AppMaterialPathDTO> getPathList(@Param("materialIds") List<Integer> materialIds);
}
