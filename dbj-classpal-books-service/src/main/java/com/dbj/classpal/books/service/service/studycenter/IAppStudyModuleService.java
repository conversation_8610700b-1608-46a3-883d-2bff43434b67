package com.dbj.classpal.books.service.service.studycenter;

import com.dbj.classpal.books.common.bo.studycenter.*;
import com.dbj.classpal.books.common.dto.studycenter.*;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;

public interface IAppStudyModuleService {
    Boolean create(AppStudyModuleCreateBO bo) throws BusinessException;
    Boolean update(AppStudyModuleUpdateBO bo) throws BusinessException;
    Boolean delete(List<Integer> ids) throws BusinessException;
    Page<AppStudyModuleListDTO> page(PageInfo<AppStudyModuleQueryPageBO> pageInfo) throws BusinessException;
    AppStudyModuleDetailDTO detail(Integer id) throws BusinessException;
    List<StudyCenterCategoryDTO> listHome(StudyCenterModuleListQueryBO queryBO) throws BusinessException;
    /** 批量上架（发布） */
    Boolean batchPublish(List<Integer> ids) throws BusinessException;
    /** 批量下架（取消发布） */
    Boolean batchUnpublish(List<Integer> ids) throws BusinessException;
    /** 批量显示 */
    Boolean batchShow(List<Integer> ids) throws BusinessException;
    /** 批量隐藏 */
    Boolean batchHide(List<Integer> ids) throws BusinessException;
} 