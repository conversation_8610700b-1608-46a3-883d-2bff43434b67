package com.dbj.classpal.books.service.service.audio.impl;

import com.dbj.classpal.books.service.biz.audio.IAudioEmotionClassifyConfigBiz;
import com.dbj.classpal.books.service.biz.audio.IAudioSpeakerBiz;
import com.dbj.classpal.books.service.entity.audio.AudioEmotionClassifyConfig;
import com.dbj.classpal.books.service.service.audio.IAudioEmotionClassifyConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 多情感分类配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Service
public class AudioEmotionClassifyConfigServiceImpl implements IAudioEmotionClassifyConfigService {

    @Autowired
    private IAudioEmotionClassifyConfigBiz audioEmotionClassifyConfigBiz;

    @Override
    public int saveBatch(List<AudioEmotionClassifyConfig> saveList) {
        boolean flag = audioEmotionClassifyConfigBiz.saveBatch(saveList);
        return flag ? saveList.size() : 0;
    }
}
