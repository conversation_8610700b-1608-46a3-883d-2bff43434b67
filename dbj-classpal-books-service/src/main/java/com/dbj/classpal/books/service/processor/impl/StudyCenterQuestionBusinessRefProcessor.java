package com.dbj.classpal.books.service.processor.impl;

import com.dbj.classpal.books.client.enums.BusinessTypeEnum;
import com.dbj.classpal.books.common.dto.question.QuestionCategoryRefDTO;
import com.dbj.classpal.books.service.biz.studycenter.IAppStudyModuleBiz;
import com.dbj.classpal.books.service.entity.question.QuestionCategoryBusinessRef;
import com.dbj.classpal.books.service.entity.studycenter.AppStudyModule;
import com.dbj.classpal.books.service.processor.BusinessRefProcessor;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 学习模块-答题业务引用处理器
 * BusinessTypeEnum = 19
 */
@Slf4j
@Component
public class StudyCenterQuestionBusinessRefProcessor implements BusinessRefProcessor {

     @Resource
     private IAppStudyModuleBiz studyModuleBiz;

    @Override
    public BusinessTypeEnum getSupportedBusinessType() {
        return BusinessTypeEnum.STUDY_CENTER_QUESTION_BUSINESS;
    }

    @Override
    public void processBusinessInfo(QuestionCategoryRefDTO dto, QuestionCategoryBusinessRef businessRef) throws Exception {
        log.info("处理学习模块-答题业务引用, businessId: {}", businessRef.getBusinessId());

        try {
            AppStudyModule studyModule = studyModuleBiz.getById(businessRef.getBusinessId());
            if (studyModule != null) {
                dto.setBusinessName(studyModule.getTitle());
                log.info("成功处理学习模块答题业务引用, moduleName: {}", studyModule.getTitle());
            } else {
                log.warn("学习模块不存在, businessId: {}", businessRef.getBusinessId());
                dto.setBusinessName(null);
            }
        } catch (Exception e) {
            log.error("处理学习模块答题业务失败, businessId: {}", businessRef.getBusinessId(), e);
            dto.setBusinessName("学习模块答题(处理失败)");
            throw e;
        }
    }

    @Override
    public int getPriority() {
        return 10;
    }
}
