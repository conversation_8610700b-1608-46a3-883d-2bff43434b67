package com.dbj.classpal.books.service.service.audio;

import com.dbj.classpal.books.service.entity.audio.AudioSpeaker;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 发音人配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Mapper
public interface IAudioSpeakerService {

    /**
     * 获取所有发音人
     * @return
     */
    List<AudioSpeaker> getAllList();

    /**
     * 批量更新
     * @param updateList
     * @return
     */
    int updateBatch(List<AudioSpeaker> updateList);
}
