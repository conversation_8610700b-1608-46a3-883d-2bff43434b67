package com.dbj.classpal.books.service.service.pointreading.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingMenuQueryBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingMenuSaveBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingMenuUpdateBO;
import com.dbj.classpal.books.common.dto.pointreading.PointReadingMenuDTO;
import com.dbj.classpal.books.service.biz.pointreading.IPointReadingMenuBiz;
import com.dbj.classpal.books.service.service.pointreading.IPointReadingMenuService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import jakarta.annotation.Resource;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 点读书目录 服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
@Service
public class PointReadingMenuServiceImpl implements IPointReadingMenuService {

    @Resource
    private IPointReadingMenuBiz pointReadingMenuBiz;

    @Override
    public Page<PointReadingMenuDTO> pageMenu(PageInfo<PointReadingMenuQueryBO> pageInfo) throws BusinessException {
        return pointReadingMenuBiz.pageMenu(pageInfo);
    }

    @Override
    public PointReadingMenuDTO detail(Integer id) throws BusinessException {
        return pointReadingMenuBiz.detail(id);
    }

    @Override
    public Integer save(PointReadingMenuSaveBO saveBO) throws BusinessException {
        return pointReadingMenuBiz.saveMenu(saveBO);
    }

    @Override
    public Boolean update(PointReadingMenuUpdateBO updateBO) throws BusinessException {
        return pointReadingMenuBiz.updateMenu(updateBO);
    }

    @Override
    public Boolean delete(Integer id) throws BusinessException {
        return pointReadingMenuBiz.deleteMenu(id);
    }

    @Override
    public Boolean deleteBatch(List<Integer> ids) throws BusinessException {
        return pointReadingMenuBiz.deleteBatch(ids);
    }

    @Override
    public List<PointReadingMenuDTO> getMenuTree(Integer bookId) throws BusinessException {
        return pointReadingMenuBiz.getMenuTree(bookId);
    }

    @Override
    public List<PointReadingMenuDTO> getChildren(Integer parentId) throws BusinessException {
        return pointReadingMenuBiz.getChildren(parentId);
    }

    @Override
    public Boolean updateSort(Integer id, Integer sortNum) throws BusinessException {
        return pointReadingMenuBiz.updateSort(id, sortNum);
    }

}
