package com.dbj.classpal.books.service.strategy.business.common.impl;

import cn.hutool.core.collection.CollUtil;
import com.dbj.classpal.books.common.dto.common.BusinessInfoDTO;
import com.dbj.classpal.books.service.strategy.business.handler.ICommonBusinessStrategyHandler;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import com.dbj.classpal.promotion.client.api.synccourse.SyncCourseApi;
import com.dbj.classpal.promotion.client.dto.synccourse.SyncCourseDTO;
import com.dbj.classpal.promotion.client.dto.synccourse.SyncCourseListBO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 同步课程 专辑引用策略实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@Service("syncCourseVideoAlbumStrategy")
public class SyncCourseVideoAlbumStrategy extends ICommonBusinessStrategyHandler {

    @Resource
    private SyncCourseApi syncCourseApi;

    @Override
    public Map<Integer, String> getBusinessName(List<Integer> businessIds) {
        return Collections.emptyMap();
    }

    @Override
    public Map<Integer, BusinessInfoDTO> getBusinessInfoMap(List<Integer> businessIds) {
        if (CollUtil.isEmpty(businessIds)) {
            return Collections.emptyMap();
        }
        try {
            RestResponse<List<SyncCourseDTO>> response = syncCourseApi.getSyncCourseList(new SyncCourseListBO().setIds(new HashSet<>(businessIds)));
            List<SyncCourseDTO> syncCourseDTOList = response.returnProcess(response);
            return syncCourseDTOList.stream().collect(Collectors.toMap(
                    SyncCourseDTO::getId,
                    dto -> {
                        BusinessInfoDTO businessInfo = new BusinessInfoDTO();
                        businessInfo.setBusinessName(dto.getCourseName());
                        Map<String, Object> otherInfo = Collections.singletonMap("gradeId", dto.getGradeId());
                        businessInfo.setOther(otherInfo);
                        return businessInfo;
                    }
            ));
        } catch (BusinessException e) {
            return Collections.emptyMap();
        }
    }
}
