package com.dbj.classpal.books.service.biz.pointreading.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.client.enums.StatusEnum;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingHotspotQueryBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingHotspotSaveBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingHotspotUpdateBO;
import com.dbj.classpal.books.common.constant.AppErrorCode;
import com.dbj.classpal.books.common.dto.pointreading.PointReadingHotspotDTO;
import com.dbj.classpal.books.common.enums.PointReadingAreaTypeEnum;
import com.dbj.classpal.books.common.enums.PointReadingEventTypeEnum;
import com.dbj.classpal.books.service.biz.pointreading.IPointReadingChapterBiz;
import com.dbj.classpal.books.service.biz.pointreading.IPointReadingHotspotBiz;
import com.dbj.classpal.books.service.entity.pointreading.PointReadingChapter;
import com.dbj.classpal.books.service.entity.pointreading.PointReadingHotspot;
import com.dbj.classpal.books.service.mapper.pointreading.PointReadingHotspotMapper;
import com.dbj.classpal.books.service.processor.media.MediaRefProcessorManager;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingHotspotMediaBO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 点读书热点区域 业务实现类
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
@Service
public class PointReadingHotspotBizImpl extends ServiceImpl<PointReadingHotspotMapper, PointReadingHotspot> implements IPointReadingHotspotBiz {

    @Resource
    private IPointReadingChapterBiz pointReadingChapterBiz;

    @Resource
    private MediaRefProcessorManager mediaRefProcessorManager;

    @Override
    public Page<PointReadingHotspotDTO> pageHotspot(Page<PointReadingHotspot> page, PointReadingHotspotQueryBO queryBO) throws BusinessException {
        Page<PointReadingHotspot> hotspotPage = this.getBaseMapper().selectPage(page, queryBO);
        
        Page<PointReadingHotspotDTO> dtoPage = new Page<>();
        BeanUtil.copyProperties(hotspotPage, dtoPage);
        
        List<PointReadingHotspotDTO> dtoList = new ArrayList<>();
        if (CollUtil.isNotEmpty(hotspotPage.getRecords())) {
            // 获取章节信息
            List<Integer> chapterIds = hotspotPage.getRecords().stream()
                    .map(PointReadingHotspot::getChapterId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            Map<Integer, PointReadingChapter> chapterMap = CollUtil.isEmpty(chapterIds) ?
                    Map.of() :
                    pointReadingChapterBiz.listByIds(chapterIds)
                            .stream()
                            .collect(Collectors.toMap(PointReadingChapter::getId, chapter -> chapter));
            
            for (PointReadingHotspot hotspot : hotspotPage.getRecords()) {
                PointReadingHotspotDTO dto = convertToDTO(hotspot);
                
                // 设置章节名称
                if (hotspot.getChapterId() != null) {
                    PointReadingChapter chapter = chapterMap.get(hotspot.getChapterId());
                    if (chapter != null) {
                        dto.setChapterName(chapter.getName());
                    }
                }
                dtoList.add(dto);
            }
        }
        
        dtoPage.setRecords(dtoList);
        return dtoPage;
    }

    @Override
    public PointReadingHotspotDTO detail(Integer id) throws BusinessException {
        PointReadingHotspot hotspot = this.getById(id);
        if (hotspot == null) {
            throw new BusinessException(AppErrorCode.POINT_READING_HOTSPOT_NOT_EXIST_CODE, AppErrorCode.POINT_READING_HOTSPOT_NOT_EXIST_MSG);
        }
        
        PointReadingHotspotDTO dto = convertToDTO(hotspot);
        
        // 设置章节名称
        if (hotspot.getChapterId() != null) {
            PointReadingChapter chapter = pointReadingChapterBiz.getById(hotspot.getChapterId());
            if (chapter != null) {
                dto.setChapterName(chapter.getName());
            }
        }
        
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer saveHotspot(PointReadingHotspotSaveBO saveBO) throws BusinessException {
        // 验证章节是否存在
        PointReadingChapter chapter = pointReadingChapterBiz.getById(saveBO.getChapterId());
        if (chapter == null) {
            throw new BusinessException(AppErrorCode.POINT_READING_CHAPTER_NOT_EXIST_CODE, AppErrorCode.POINT_READING_CHAPTER_NOT_EXIST_MSG);
        }
        
        // 验证同章节下热点名称是否重复
        LambdaQueryWrapper<PointReadingHotspot> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PointReadingHotspot::getChapterId, saveBO.getChapterId())
                .eq(PointReadingHotspot::getName, saveBO.getName());
        
        if (this.count(wrapper) > 0) {
            throw new BusinessException(AppErrorCode.POINT_READING_HOTSPOT_NAME_EXIST_CODE, "同章节下热点名称已存在");
        }
        
        // 构建实体对象
        PointReadingHotspot hotspot = new PointReadingHotspot();
        BeanUtil.copyProperties(saveBO, hotspot);
        
        // 验证和设置层级关系
        validateNodeHierarchy(hotspot.getParentNodeId(), hotspot.getNodeLevel(), saveBO.getChapterId());

        // 设置默认值
        if (hotspot.getAreaType() == null) {
            hotspot.setAreaType(PointReadingAreaTypeEnum.HOTSPOT_AREA.getCode());
        }
        if (hotspot.getEventType() == null) {
            hotspot.setEventType(PointReadingEventTypeEnum.POINT_READING.getCode());
        }

        if (hotspot.getFollowRead() == null) {
            hotspot.setFollowRead(YesOrNoEnum.NO.getCode());
        }
        if (hotspot.getNodeLevel() == null) {
            hotspot.setNodeLevel(hotspot.getParentNodeId() == null ? 1 : 2);
        }
        if (hotspot.getSortNum() == null) {
            hotspot.setSortNum(getNextSortNum(saveBO.getChapterId()));
        }
        if (hotspot.getStatus() == null) {
            hotspot.setStatus(YesOrNoEnum.YES.getCode());
        }
        
        // 保存热点
        this.save(hotspot);

        // 处理媒体文件列表
        if (CollUtil.isNotEmpty(saveBO.getMediaList())) {
            try {
                // 为每个媒体项设置默认的mediaSource（如果没有设置的话）
                List<PointReadingHotspotMediaBO> processedMediaList = processMediaList(saveBO.getMediaList(), saveBO.getMediaSource());
                mediaRefProcessorManager.saveHotspotMediaRefs(hotspot.getId(), processedMediaList);
            } catch (Exception e) {
                log.error("保存热点媒体引用失败，hotspotId：{}", hotspot.getId(), e);
                // 这里可以选择抛出异常或者记录日志继续
                throw new BusinessException(AppErrorCode.POINT_READING_HOTSPOT_MEDIA_SAVE_FAIL_CODE, "保存热点媒体引用失败");
            }
        }

        return hotspot.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveChapterHotspots(Integer chapterId, List<PointReadingHotspotSaveBO> hotspots) throws BusinessException {
        PointReadingChapter chapter = pointReadingChapterBiz.getById(chapterId);
        if (chapter == null) {
            throw new BusinessException(AppErrorCode.POINT_READING_CHAPTER_NOT_EXIST_CODE, AppErrorCode.POINT_READING_CHAPTER_NOT_EXIST_MSG);
        }

        if (CollUtil.isEmpty(hotspots)) {
            return true;
        }

        // 批量保存新的热点
        List<PointReadingHotspot> hotspotList = new ArrayList<>();
        for (int i = 0; i < hotspots.size(); i++) {
            PointReadingHotspotSaveBO saveBO = hotspots.get(i);

            PointReadingHotspot hotspot = new PointReadingHotspot();
            BeanUtil.copyProperties(saveBO, hotspot);
            hotspot.setChapterId(chapterId);

            // 设置默认值
            if (hotspot.getAreaType() == null) {
                hotspot.setAreaType(PointReadingAreaTypeEnum.HOTSPOT_AREA.getCode());
            }
            if (hotspot.getEventType() == null) {
                hotspot.setEventType(PointReadingEventTypeEnum.POINT_READING.getCode());
            }

            if (hotspot.getFollowRead() == null) {
                hotspot.setFollowRead(YesOrNoEnum.NO.getCode());
            }
            if (hotspot.getSortNum() == null) {
                hotspot.setSortNum(i + 1);
            }
            if (hotspot.getStatus() == null) {
                hotspot.setStatus(YesOrNoEnum.YES.getCode());
            }

            hotspotList.add(hotspot);
        }

        boolean saveResult = this.saveBatch(hotspotList);

        // 处理媒体文件列表
        if (saveResult) {
            for (int i = 0; i < hotspots.size(); i++) {
                PointReadingHotspotSaveBO saveBO = hotspots.get(i);
                PointReadingHotspot savedHotspot = hotspotList.get(i);

                if (CollUtil.isNotEmpty(saveBO.getMediaList())) {
                    try {
                        List<PointReadingHotspotMediaBO> processedMediaList = processMediaList(saveBO.getMediaList(), saveBO.getMediaSource());
                        mediaRefProcessorManager.saveHotspotMediaRefs(savedHotspot.getId(), processedMediaList);
                    } catch (Exception e) {
                        log.error("批量保存热点媒体引用失败，hotspotId：{}", savedHotspot.getId(), e);
                        // 这里可以选择继续处理其他热点或者抛出异常
                    }
                }
            }
        }

        return saveResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateChapterHotspots(Integer chapterId, List<PointReadingHotspotSaveBO> hotspots) throws BusinessException {
        // 验证章节是否存在
        PointReadingChapter chapter = pointReadingChapterBiz.getById(chapterId);
        if (chapter == null) {
            throw new BusinessException(AppErrorCode.POINT_READING_CHAPTER_NOT_EXIST_CODE, AppErrorCode.POINT_READING_CHAPTER_NOT_EXIST_MSG);
        }

        // 先获取章节下的所有热点ID，用于删除媒体引用
        LambdaQueryWrapper<PointReadingHotspot> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PointReadingHotspot::getChapterId, chapterId);
        List<PointReadingHotspot> existingHotspots = this.list(queryWrapper);
        List<Integer> existingHotspotIds = existingHotspots.stream()
                .map(PointReadingHotspot::getId)
                .collect(Collectors.toList());

        // 删除现有热点的媒体引用
        if (CollUtil.isNotEmpty(existingHotspotIds)) {
            try {
                mediaRefProcessorManager.batchDeleteHotspotMediaRefs(existingHotspotIds);
            } catch (Exception e) {
                log.error("批量删除热点媒体引用失败，hotspotIds：{}", existingHotspotIds, e);
                // 删除媒体引用失败不影响主要操作
            }
        }

        // 删除章节下的所有热点
        LambdaQueryWrapper<PointReadingHotspot> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(PointReadingHotspot::getChapterId, chapterId);
        this.remove(deleteWrapper);

        // 如果没有新的热点数据，直接返回成功
        if (CollUtil.isEmpty(hotspots)) {
            return true;
        }

        // 批量保存新的热点
        List<PointReadingHotspot> hotspotList = new ArrayList<>();
        for (int i = 0; i < hotspots.size(); i++) {
            PointReadingHotspotSaveBO saveBO = hotspots.get(i);

            PointReadingHotspot hotspot = new PointReadingHotspot();
            BeanUtil.copyProperties(saveBO, hotspot);
            hotspot.setChapterId(chapterId);

            // 验证和设置层级关系
            validateNodeHierarchy(hotspot.getParentNodeId(), hotspot.getNodeLevel(), chapterId);

            // 设置默认值
            if (hotspot.getAreaType() == null) {
                hotspot.setAreaType(PointReadingAreaTypeEnum.HOTSPOT_AREA.getCode());
            }
            if (hotspot.getEventType() == null) {
                hotspot.setEventType(PointReadingEventTypeEnum.POINT_READING.getCode());
            }

            if (hotspot.getFollowRead() == null) {
                hotspot.setFollowRead(YesOrNoEnum.NO.getCode());
            }
            if (hotspot.getNodeLevel() == null) {
                hotspot.setNodeLevel(hotspot.getParentNodeId() == null ? 1 : 2);
            }
            if (hotspot.getSortNum() == null) {
                hotspot.setSortNum(i + 1);
            }
            if (hotspot.getStatus() == null) {
                hotspot.setStatus(YesOrNoEnum.YES.getCode());
            }

            hotspotList.add(hotspot);
        }

        boolean saveResult = this.saveBatch(hotspotList);

        // 处理媒体文件列表
        if (saveResult) {
            for (int i = 0; i < hotspots.size(); i++) {
                PointReadingHotspotSaveBO saveBO = hotspots.get(i);
                PointReadingHotspot savedHotspot = hotspotList.get(i);

                if (CollUtil.isNotEmpty(saveBO.getMediaList())) {
                    try {
                        List<PointReadingHotspotMediaBO> processedMediaList = processMediaList(saveBO.getMediaList(), saveBO.getMediaSource());
                        mediaRefProcessorManager.saveHotspotMediaRefs(savedHotspot.getId(), processedMediaList);
                    } catch (Exception e) {
                        log.error("批量更新热点媒体引用失败，hotspotId：{}", savedHotspot.getId(), e);
                        // 这里可以选择继续处理其他热点或者抛出异常
                    }
                }
            }
        }

        return saveResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateHotspot(PointReadingHotspotUpdateBO updateBO) throws BusinessException {
        // 验证热点是否存在
        PointReadingHotspot existHotspot = this.getById(updateBO.getId());
        if (existHotspot == null) {
            throw new BusinessException(AppErrorCode.POINT_READING_HOTSPOT_NOT_EXIST_CODE, AppErrorCode.POINT_READING_HOTSPOT_NOT_EXIST_MSG);
        }
        
        // 验证章节是否存在
        PointReadingChapter chapter = pointReadingChapterBiz.getById(updateBO.getChapterId());
        if (chapter == null) {
            throw new BusinessException(AppErrorCode.POINT_READING_CHAPTER_NOT_EXIST_CODE, AppErrorCode.POINT_READING_CHAPTER_NOT_EXIST_MSG);
        }
        
        // 验证同章节下热点名称是否重复
        LambdaQueryWrapper<PointReadingHotspot> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PointReadingHotspot::getChapterId, updateBO.getChapterId())
                .eq(PointReadingHotspot::getName, updateBO.getName())
                .ne(PointReadingHotspot::getId, updateBO.getId());
        
        if (this.count(wrapper) > 0) {
            throw new BusinessException(AppErrorCode.POINT_READING_HOTSPOT_NAME_EXIST_CODE, "同章节下热点名称已存在");
        }
        
        // 构建更新对象
        PointReadingHotspot hotspot = new PointReadingHotspot();
        BeanUtil.copyProperties(updateBO, hotspot);

        boolean updateResult = this.updateById(hotspot);

        // 处理媒体文件列表
        if (updateResult && CollUtil.isNotEmpty(updateBO.getMediaList())) {
            try {
                // 先删除现有的媒体引用
                mediaRefProcessorManager.deleteHotspotMediaRefs(updateBO.getId());

                // 为每个媒体项设置默认的mediaSource（如果没有设置的话）
                List<PointReadingHotspotMediaBO> processedMediaList = processMediaList(updateBO.getMediaList(), updateBO.getMediaSource());
                mediaRefProcessorManager.saveHotspotMediaRefs(updateBO.getId(), processedMediaList);
            } catch (Exception e) {
                log.error("更新热点媒体引用失败，hotspotId：{}", updateBO.getId(), e);
                throw new BusinessException(AppErrorCode.POINT_READING_HOTSPOT_MEDIA_UPDATE_FAIL_CODE, "更新热点媒体引用失败");
            }
        } else if (updateResult && CollUtil.isEmpty(updateBO.getMediaList())) {
            // 如果媒体列表为空，删除现有的媒体引用
            try {
                mediaRefProcessorManager.deleteHotspotMediaRefs(updateBO.getId());
            } catch (Exception e) {
                log.error("删除热点媒体引用失败，hotspotId：{}", updateBO.getId(), e);
                // 删除失败不影响主要更新操作
            }
        }

        return updateResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteHotspot(Integer id) throws BusinessException {
        // 验证热点是否存在
        PointReadingHotspot hotspot = this.getById(id);
        if (hotspot == null) {
            throw new BusinessException(AppErrorCode.POINT_READING_HOTSPOT_NOT_EXIST_CODE, AppErrorCode.POINT_READING_HOTSPOT_NOT_EXIST_MSG);
        }

        // 删除热点的媒体引用
        try {
            mediaRefProcessorManager.deleteHotspotMediaRefs(id);
        } catch (Exception e) {
            log.error("删除热点媒体引用失败，hotspotId：{}", id, e);
            // 删除媒体引用失败不影响主要删除操作
        }

        return this.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBatch(List<Integer> ids) throws BusinessException {
        if (CollUtil.isEmpty(ids)) {
            return true;
        }

        // 批量删除热点的媒体引用
        try {
            mediaRefProcessorManager.batchDeleteHotspotMediaRefs(ids);
        } catch (Exception e) {
            log.error("批量删除热点媒体引用失败，hotspotIds：{}", ids, e);
            // 删除媒体引用失败不影响主要删除操作
        }

        return this.removeByIds(ids);
    }

    @Override
    public List<PointReadingHotspotDTO> getHotspotsByChapter(Integer chapterId) throws BusinessException {
        List<PointReadingHotspot> hotspots = this.getBaseMapper().selectByChapterId(chapterId);
        return hotspots.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<PointReadingHotspotDTO> getHotspotsTreeByChapter(Integer chapterId) throws BusinessException {
        // 查询章节下的所有热点
        List<PointReadingHotspot> allHotspots = this.getBaseMapper().selectByChapterId(chapterId);

        if (CollUtil.isEmpty(allHotspots)) {
            return new ArrayList<>();
        }

        // 转换为DTO
        List<PointReadingHotspotDTO> allDTOs = allHotspots.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());

        // 构建树形结构
        return buildHotspotTree(allDTOs);
    }

    /**
     * 转换为DTO
     */
    private PointReadingHotspotDTO convertToDTO(PointReadingHotspot hotspot) {
        PointReadingHotspotDTO dto = new PointReadingHotspotDTO();
        BeanUtil.copyProperties(hotspot, dto);
        
        // 设置状态描述
        if (hotspot.getStatus() != null) {
            dto.setStatusDesc(StatusEnum.getDescByCode(hotspot.getStatus()));
        }
        
        // 设置区域类型描述
        if (hotspot.getAreaType() != null) {
            dto.setAreaTypeDesc(PointReadingAreaTypeEnum.getDescByCode(hotspot.getAreaType()));
        }

        // 设置事件类型描述
        if (hotspot.getEventType() != null) {
            dto.setEventTypeDesc(PointReadingEventTypeEnum.getDescByCode(hotspot.getEventType()));
        }



        // 设置跟读支持描述
        if (hotspot.getFollowRead() != null) {
            dto.setFollowReadDesc(StatusEnum.getDescByCode(hotspot.getFollowRead()));
        }

        return dto;
    }

    /**
     * 获取下一个排序号
     */
    private Integer getNextSortNum(Integer chapterId) {
        LambdaQueryWrapper<PointReadingHotspot> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PointReadingHotspot::getChapterId, chapterId)
                .orderByDesc(PointReadingHotspot::getSortNum)
                .last("LIMIT 1");
        
        PointReadingHotspot lastHotspot = this.getOne(wrapper);
        if (lastHotspot == null || lastHotspot.getSortNum() == null) {
            return 1;
        }
        
        return lastHotspot.getSortNum() + 1;
    }


    /**
     * 验证节点层级关系
     */
    private void validateNodeHierarchy(String parentNodeId, Integer nodeLevel, Integer chapterId) throws BusinessException {
        if (parentNodeId != null) {
            // 验证父节点是否存在
            LambdaQueryWrapper<PointReadingHotspot> parentWrapper = new LambdaQueryWrapper<>();
            parentWrapper.eq(PointReadingHotspot::getChapterId, chapterId)
                    .eq(PointReadingHotspot::getNodeId, parentNodeId);

            PointReadingHotspot parentNode = this.getOne(parentWrapper);
            if (parentNode == null) {
                throw new BusinessException(AppErrorCode.POINT_READING_HOTSPOT_PARENT_NOT_EXIST_CODE, AppErrorCode.POINT_READING_HOTSPOT_PARENT_NOT_EXIST_MSG);
            }

            // 验证层级关系（子节点层级应该比父节点大1）
            if (nodeLevel != null && parentNode.getNodeLevel() != null) {
                if (nodeLevel <= parentNode.getNodeLevel()) {
                    throw new BusinessException(AppErrorCode.POINT_READING_HOTSPOT_LEVEL_ERROR_CODE, AppErrorCode.POINT_READING_HOTSPOT_LEVEL_ERROR_MSG);
                }
            }
        } else {
            // 根节点层级应该为1
            if (nodeLevel != null && nodeLevel != 1) {
                throw new BusinessException(AppErrorCode.POINT_READING_HOTSPOT_ROOT_LEVEL_ERROR_CODE, AppErrorCode.POINT_READING_HOTSPOT_ROOT_LEVEL_ERROR_MSG);
            }
        }
    }

    /**
     * 构建热点树形结构
     */
    private List<PointReadingHotspotDTO> buildHotspotTree(List<PointReadingHotspotDTO> allHotspots) {
        // 创建节点映射
        Map<String, PointReadingHotspotDTO> nodeMap = allHotspots.stream()
                .collect(Collectors.toMap(PointReadingHotspotDTO::getNodeId, dto -> dto));

        // 找出根节点并构建树
        List<PointReadingHotspotDTO> rootNodes = new ArrayList<>();

        for (PointReadingHotspotDTO hotspot : allHotspots) {
            if (hotspot.getParentNodeId() == null) {
                // 根节点
                rootNodes.add(hotspot);
            } else {
                // 子节点，添加到父节点的children中
                PointReadingHotspotDTO parent = nodeMap.get(hotspot.getParentNodeId());
                if (parent != null) {
                    if (parent.getChildren() == null) {
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(hotspot);
                }
            }
        }

        // 对每个节点的子节点进行排序
        sortTreeNodes(rootNodes);

        return rootNodes;
    }

    /**
     * 对树节点进行排序
     */
    private void sortTreeNodes(List<PointReadingHotspotDTO> nodes) {
        if (CollUtil.isEmpty(nodes)) {
            return;
        }

        // 按排序号和ID排序
        nodes.sort(Comparator.comparingInt((PointReadingHotspotDTO a) -> a.getSortNum() != null ? a.getSortNum() : 0).thenComparingInt(PointReadingHotspotDTO::getId));

        // 递归排序子节点
        for (PointReadingHotspotDTO node : nodes) {
            if (CollUtil.isNotEmpty(node.getChildren())) {
                sortTreeNodes(node.getChildren());
            }
        }
    }

    /**
     * 处理媒体列表，为没有设置mediaSource的媒体项设置默认值
     */
    private List<PointReadingHotspotMediaBO> processMediaList(List<PointReadingHotspotMediaBO> mediaList, Integer defaultMediaSource) {
        if (CollUtil.isEmpty(mediaList)) {
            return mediaList;
        }

        List<PointReadingHotspotMediaBO> processedList = new ArrayList<>();
        for (PointReadingHotspotMediaBO media : mediaList) {
            PointReadingHotspotMediaBO processedMedia = new PointReadingHotspotMediaBO();
            BeanUtil.copyProperties(media, processedMedia);

            // 如果媒体项没有设置mediaSource，使用默认值
            if (processedMedia.getMediaSource() == null && defaultMediaSource != null) {
                processedMedia.setMediaSource(defaultMediaSource);
            }

            processedList.add(processedMedia);
        }

        return processedList;
    }
}
