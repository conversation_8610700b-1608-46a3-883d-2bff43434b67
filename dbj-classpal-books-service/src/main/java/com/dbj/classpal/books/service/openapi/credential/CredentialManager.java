package com.dbj.classpal.books.service.openapi.credential;

import com.dbj.business.external.client.model.request.AppInfoRequest;
import com.dbj.business.external.client.model.response.AppInfoResponse;
import com.dbj.classpal.books.service.openapi.service.AppInfoService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.framework.openapi.config.ApiClientProperties;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;

/**
 * 凭证管理服务
 * 负责申请、存储和刷新API凭证
 * 支持手动配置的appId和appSecret最高优先级
 */
@Component
public class CredentialManager {
    private static final Logger logger = LoggerFactory.getLogger(CredentialManager.class);

    private final AppInfoService appInfoService;
    private final String clientIdentifier;
    private final ApiClientProperties apiClientProperties;

    private volatile String appId;
    private volatile String appSecret;
    private volatile boolean initialized;

    private final ReadWriteLock lock = new ReentrantReadWriteLock();
    private final Lock readLock = lock.readLock();
    private final Lock writeLock = lock.writeLock();

    public CredentialManager(
            AppInfoService appInfoService,
            @Value("${app.client-identifier}") String clientIdentifier,
            ApiClientProperties apiClientProperties) {
        this.appInfoService = appInfoService;
        this.clientIdentifier = clientIdentifier;
        this.apiClientProperties = apiClientProperties;
        logger.info("CredentialManager initialized with clientIdentifier: {}", clientIdentifier);
    }

    /**
     * 获取应用凭证，如果未初始化则先初始化
     * @return 包含appId和appSecret的凭证对象
     */
    public CompletableFuture<Credential> getCredential() {
        if (initialized) {
            try {
                readLock.lock();
                if (initialized) {
                    logger.debug("Using cached credentials: appId={}", appId);
                    return CompletableFuture.completedFuture(
                            new Credential(appId, appSecret));
                }
            } finally {
                readLock.unlock();
            }
        }
        return initializeCredential();
    }

    /**
     * 初始化应用凭证
     * 优先级顺序：
     * 1. 手动配置的appId和appSecret（如果存在）
     * 2. 从服务查询到的应用凭证（选择最新创建的）
     * 3. 创建新应用
     */
    private CompletableFuture<Credential> initializeCredential() {
        try {
            writeLock.lock();

            if (initialized) {
                return CompletableFuture.completedFuture(
                        new Credential(appId, appSecret));
            }

            String configAppId = apiClientProperties.getAppId();
            String configAppSecret = apiClientProperties.getAppSecret();

            String serviceName = "default";
            if (apiClientProperties.getServices() != null &&
                    apiClientProperties.getServices().containsKey(serviceName)) {

                ApiClientProperties.ServiceEndpoint endpoint =
                        apiClientProperties.getServices().get(serviceName);

                if (StringUtils.isNotEmpty(endpoint.getAppId())) {
                    configAppId = endpoint.getAppId();
                }

                if (StringUtils.isNotEmpty(endpoint.getAppSecret())) {
                    configAppSecret = endpoint.getAppSecret();
                }
            }

            if (StringUtils.isNotEmpty(configAppId) && StringUtils.isNotEmpty(configAppSecret)) {
                logger.info("使用手动配置的凭据： appId={}", configAppId);

                appId = configAppId;
                appSecret = configAppSecret;
                initialized = true;

                return CompletableFuture.completedFuture(
                        new Credential(appId, appSecret));
            }

            logger.info("没有手动配置的凭据，正在通过客户端标识符查询,客户端标识: {}",
                    clientIdentifier);

            List<AppInfoResponse> appList = appInfoService.getAppsByClientId(clientIdentifier);

            if (appList != null && !appList.isEmpty()) {
                AppInfoResponse selectedApp;

                if (appList.size() > 1) {
                    selectedApp = appList.stream().min((a1, a2) -> {
                                if (a1.getCreateTime() == null && a2.getCreateTime() == null) {
                                    return 0;
                                }
                                if (a1.getCreateTime() == null) {
                                    return 1;
                                }
                                if (a2.getCreateTime() == null) {
                                    return -1;
                                }
                                return a2.getCreateTime().compareTo(a1.getCreateTime());
                            })
                            .orElse(appList.get(0));

                    logger.info("选择最近创建的应用程序: appId={}, createTime={}",
                            selectedApp.getAppId(), selectedApp.getCreateTime());
                } else {
                    selectedApp = appList.get(0);
                    logger.info("根据客户端标识符找到单个应用程序,客户端标识: {}, appId: {}",
                            clientIdentifier, selectedApp.getAppId());
                }

                // 使用选定的应用凭证
                appId = selectedApp.getAppId();
                appSecret = selectedApp.getAppSecret();
                initialized = true;

                return CompletableFuture.completedFuture(
                        new Credential(appId, appSecret));
            } else {
                logger.info("没有找到与客户端标识对应的现有应用程序,客户端标识: {}, 创建新应用",
                        clientIdentifier);

                AppInfoRequest newApp = new AppInfoRequest();
                newApp.setClientId(clientIdentifier);
                newApp.setAppName("客户端： " + clientIdentifier);
                newApp.setDescription("客户端: " + clientIdentifier + " 自动创建应用");

                AppInfoResponse createdApp = appInfoService.createApp(newApp);

                if (createdApp == null) {
                    logger.error("客户端创建应用失败: {}", clientIdentifier);
                    return CompletableFuture.failedFuture(
                            new RuntimeException("创建应用失败"));
                }

                appId = createdApp.getAppId();
                appSecret = createdApp.getAppSecret();
                initialized = true;

                logger.info("创建新的应用: 客户端标识={}, appId={}",
                        clientIdentifier, appId);

                return CompletableFuture.completedFuture(
                        new Credential(appId, appSecret));
            }
        } catch (Exception e) {
            logger.error("初始化凭据错误", e);
            return CompletableFuture.failedFuture(e);
        } finally {
            writeLock.unlock();
        }
    }

    /**
     * 刷新应用密钥
     * 注意：如果使用的是手动配置的凭证，刷新将不生效
     */
    public CompletableFuture<Credential> refreshCredential() {
        try {
            writeLock.lock();

            if (!initialized || appId == null) {
                logger.error("无法刷新凭据：未初始化");
                return CompletableFuture.failedFuture(
                        new BusinessException(OPENAPI_REFRESH_CREDENTIAL_FAIL_CODE,OPENAPI_REFRESH_CREDENTIAL_FAIL_MSG));
            }

            // 检查是否使用的是配置中的凭证
            String configAppId = getConfiguredAppId();
            if (StringUtils.isNotEmpty(configAppId) && configAppId.equals(appId)) {
                return CompletableFuture.completedFuture(
                        new Credential(appId, appSecret));
            }

            logger.info("刷新凭证,appId: {}", appId);

            String newAppSecret = appInfoService.refreshAppSecret(appId);

            if (newAppSecret == null) {
                return CompletableFuture.failedFuture(
                        new BusinessException(OPENAPI_REFRESH_APP_SECRET_FAIL_CODE,OPENAPI_REFRESH_APP_SECRET_FAIL_MSG));
            }

            appSecret = newAppSecret;

            logger.info("成功刷新应用程序密钥,appId: {}", appId);

            return CompletableFuture.completedFuture(
                    new Credential(appId, appSecret));
        } catch (Exception e) {
            logger.error("刷新应用密钥失败", e);
            return CompletableFuture.failedFuture(e);
        } finally {
            writeLock.unlock();
        }
    }

    /**
     * 获取配置中的appId
     */
    private String getConfiguredAppId() {
        String configAppId = apiClientProperties.getAppId();

        String serviceName = "default"; // 或者根据您的需求确定服务名
        if (apiClientProperties.getServices() != null &&
                apiClientProperties.getServices().containsKey(serviceName)) {

            ApiClientProperties.ServiceEndpoint endpoint =
                    apiClientProperties.getServices().get(serviceName);

            if (StringUtils.isNotEmpty(endpoint.getAppId())) {
                configAppId = endpoint.getAppId();
            }
        }

        return configAppId;
    }

    /**
     * 凭证类，包含appId和appSecret
     */
    @Data
    public static class Credential {
        private final String appId;
        private final String appSecret;
    }
}