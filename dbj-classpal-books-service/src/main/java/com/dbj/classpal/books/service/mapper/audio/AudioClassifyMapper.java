package com.dbj.classpal.books.service.mapper.audio;

import com.dbj.classpal.books.client.dto.audio.AudioClassifyPathDTO;
import com.dbj.classpal.books.service.entity.audio.AudioClassify;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 音频分类 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
public interface AudioClassifyMapper extends BaseMapper<AudioClassify> {

    List<AudioClassifyPathDTO> getPathList(@Param("audioIntroIds") List<Integer> audioIntroIds);

    Set<Integer> getChildren(@Param("ids") Set<Integer> ids);

    Set<Integer> getParentIds(@Param("id") Integer id);
}
