package com.dbj.classpal.books.service.strategy.question;

import com.dbj.classpal.books.common.bo.paper.*;
import com.dbj.classpal.books.common.dto.paper.UserPaperCheckSubmitDTO;
import com.dbj.classpal.books.common.dto.paper.UserPaperDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;

/**
 * 试卷业务策略接口
 */
public interface IPaperBusinessStrategy {

    /**
     * 获取业务类型
     *
     * @return 业务类型
     */
    Integer getBusinessType();

    /**
     * 创建试卷
     *
     * @param createBO 创建试卷参数
     * @return 试卷信息
     * @throws BusinessException 业务异常
     */
    UserPaperDTO createPaper(CreateUserPaperBO createBO) throws BusinessException;
    /**
     * 检查是否提交
     *
     * @param serviceBO 检查是否提交
     * @return 试卷信息
     * @throws BusinessException 业务异常
     */
    UserPaperCheckSubmitDTO checkSubmit(CheckSubmitBO serviceBO) throws BusinessException;

    /**
     * 获取试卷信息
     *
     * @param paperId 试卷ID
     * @return 试卷信息
     * @throws BusinessException 业务异常
     */
    UserPaperDTO getPaperInfo(Integer paperId) throws BusinessException;


    /**
     * 保存试卷作答结果
     *
     * @param submitBO 提交试卷参数
     * @return 是否成功
     * @throws BusinessException 业务异常
     */
    UserPaperDTO submitPaper(SubmitPaperBO submitBO) throws BusinessException;

    /**
     * 重新考试
     *
     * @param retakePaperBO@throws BusinessException 业务异常
     */
    void retakePaper(RetakePaperBO retakePaperBO) throws BusinessException;

    /**
     * 校验查询参数
     *
     * @param queryBO 查询参数
     * @throws BusinessException 业务异常
     */
    void validateQueryParams(QueryPaperQuestionsBO queryBO) throws BusinessException;
} 