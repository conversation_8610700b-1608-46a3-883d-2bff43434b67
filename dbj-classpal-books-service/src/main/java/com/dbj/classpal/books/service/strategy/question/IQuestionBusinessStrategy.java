package com.dbj.classpal.books.service.strategy.question;

import com.dbj.classpal.books.common.bo.paper.*;
import com.dbj.classpal.books.common.dto.paper.BlankResultDTO;
import com.dbj.classpal.books.common.dto.paper.PaperQuestionDTO;
import com.dbj.classpal.books.common.dto.paper.PaperQuestionResultDTO;
import com.dbj.classpal.books.common.dto.question.QuestionCorrectAnswerDTO;
import com.dbj.classpal.books.service.entity.question.Question;
import com.dbj.classpal.books.service.entity.question.QuestionAnswer;
import com.dbj.classpal.books.service.entity.question.QuestionBlankArea;
import com.dbj.classpal.framework.commons.exception.BusinessException;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 题目业务策略接口
 */
public interface IQuestionBusinessStrategy {

    /**
     * 获取业务类型
     *
     * @return 业务类型
     */
    Integer getBusinessType();

    /**
     * 保存题目作答结果
     *
     * @param submitBO@return 是否成功
     */
    void saveQuestionResult(SubmitPaperBO submitBO, Integer paperId) throws BusinessException;

    /**
     * 保存空位作答结果（默认实现）
     */
    void saveBlankResult(SubmitPaperBO submitBO, Integer paperId) throws BusinessException;

    /**
     * 获取空位作答结果列表（默认实现）
     */
    List<BlankResultDTO> getBlankResults(QueryBlankResultBO queryBO) throws BusinessException ;
    /**
     * 获取试卷题目列表
     *
     * @param queryBO 查询参数
     * @return 题目列表
     * @throws BusinessException 业务异常
     */
    List<PaperQuestionDTO> getPaperQuestions(QueryPaperQuestionsBO queryBO) throws BusinessException;
    /**
     * 获取题目数据
     *
     * @param submitBO 提交参数
     * @return 题目Map，Key为题目ID，Value为题目对象
     */
    Map<Integer, Question> loadQuestions(SubmitPaperBO submitBO);

    /**
     * 获取空位区域数据
     * 
     * @param submitBO 提交参数
     * @return 空位区域Map，Key为题目ID，Value为该题目下的空位区域Map（Key为空位ID，Value为空位对象）
     */
    Map<Integer, Map<Integer, QuestionBlankArea>> loadBlankAreas(SubmitPaperBO submitBO);
    
    /**
     * 评分普通题目
     * 
     * @param results 普通题目作答结果列表
     * @param questionMap 题目Map
     */
    void scoreQuestionResults(List<SubmitQuestionResultBO> results, Map<Integer, Question> questionMap);
    
    /**
     * 评分填空题
     *
     * @param results     填空题作答结果列表
     * @param questionIds
     */
    void scoreBlankResults(List<SubmitBlankResultBO> results,Set<Integer> questionIds);


    /**
     * 获取题目正确答案列表
     *
     * @param businessId 业务ID
     * @param businessType 业务类型
     * @return 题目正确答案列表
     * @throws BusinessException 业务异常
     */
    List<QuestionCorrectAnswerDTO> getQuestionsCorrectAnswers(Integer businessId, Integer businessType,List<Integer> questionIds) throws BusinessException;

    /**
     * 获取结果
     * @param queryBO
     * @return
     * @throws BusinessException
     */
    PaperQuestionResultDTO getPaperQuestionResult(QueryPaperResultBO queryBO)throws BusinessException;

    /**
     * 添加错题
     * @param submitBO
     * @param paperId
     * @throws BusinessException
     */
    void saveWrongQuestion(SubmitPaperBO submitBO,Integer paperId) throws BusinessException;

    /**
     * 评测答题时需要新增的评测报告记录
     * @param submitBO
     * @param paperId
     * @throws BusinessException
     */
    default void saveEvaluation(SubmitPaperBO submitBO, Integer paperId) throws BusinessException{

    }


}