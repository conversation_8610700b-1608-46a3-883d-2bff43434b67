package com.dbj.classpal.books.service.service.ebooks;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigWatermarkTemplateEditApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigWatermarkTemplateQueryApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigWatermarkTemplateSaveApiBO;
import com.dbj.classpal.books.client.dto.ebooks.AppEbooksConfigWatermarkTemplateQueryApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface IAppEbooksConfigWatermarkTemplateService {
    /**
     * 分页查询水印模板列表
     * @param pageInfo
     * @return
     */
    Page<AppEbooksConfigWatermarkTemplateQueryApiDTO> pageInfo(PageInfo<AppEbooksConfigWatermarkTemplateQueryApiBO> pageInfo);

    List<AppEbooksConfigWatermarkTemplateQueryApiDTO> getAll();

    Boolean saveEbooksConfigWatermarkTemplate(AppEbooksConfigWatermarkTemplateSaveApiBO bo) throws BusinessException;

    Boolean updateEbooksConfigWatermarkTemplate(AppEbooksConfigWatermarkTemplateEditApiBO bo) throws BusinessException;

    Boolean deleteEbooksConfigWatermarkTemplate(CommonIdsApiBO bo) throws BusinessException;


}
