package com.dbj.classpal.books.service.strategy.advertisement.impl;

import cn.hutool.core.util.StrUtil;
import com.dbj.classpal.books.client.dto.advertisement.AdvertisementLevelConditionDTO;
import com.dbj.classpal.books.client.dto.advertisement.AdvertisementLevelConditionOptionDTO;
import com.dbj.classpal.books.common.enums.advertisement.AdvertisementConditionCodeEnum;
import com.dbj.classpal.books.service.api.client.advertisement.AdvertisementContext;
import com.dbj.classpal.books.service.strategy.advertisement.IAdvertisementConditionStrategy;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-04-26 14:27
 */
@Deprecated
@Component
public class GenderConditionStrategy implements IAdvertisementConditionStrategy {

    @Override
    public String getCode() {
        return AdvertisementConditionCodeEnum.GENDER.getCode();
    }

    @Override
    public void doHandle(AdvertisementContext context) {
        AdvertisementLevelConditionDTO condition = context.getAdvertisementLevelCondition();
        List<AdvertisementLevelConditionOptionDTO> conditionOptionList = condition.getAdvertisementLevelConditionOptionList();
        condition.setIsEligible(conditionOptionList.stream()
                .anyMatch(option -> StrUtil.split(option.getOptionValue(), ',').contains(context.getGender())));
    }
}
