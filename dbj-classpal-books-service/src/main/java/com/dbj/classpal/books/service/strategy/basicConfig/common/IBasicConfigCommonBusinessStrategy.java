package com.dbj.classpal.books.service.strategy.basicConfig.common;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.service.entity.config.BasicConfig;
import com.dbj.classpal.framework.commons.exception.BusinessException;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppMaterialBusinessStrategy
 * Date:     2025-05-09 13:44:43
 * Description: 表名： ,描述： 表
 */
public interface IBasicConfigCommonBusinessStrategy {

    Map<Integer,Long> getRefMap(List<Integer> ids);


    void throwException(String type) throws BusinessException;
}
