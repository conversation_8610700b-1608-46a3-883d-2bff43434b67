package com.dbj.classpal.books.service.mapper.ebooks;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.common.bo.ebooks.AppEbooksConfigWatermarkTemplateQueryBO;
import com.dbj.classpal.books.common.dto.ebooks.AppEbooksConfigWatermarkTemplateQueryDTO;
import com.dbj.classpal.books.service.entity.ebooks.AppEbooksConfigWatermarkTemplate;
import org.apache.ibatis.annotations.Param;

public interface AppEbooksConfigWatermarkTemplateMapper extends BaseMapper<AppEbooksConfigWatermarkTemplate> {

    /**
     * 分页查询水印模板列表
     * @param page
     * @param bo
     * @return
     */
    Page<AppEbooksConfigWatermarkTemplateQueryDTO> pageInfo(Page page, @Param("bo") AppEbooksConfigWatermarkTemplateQueryBO bo);
}
