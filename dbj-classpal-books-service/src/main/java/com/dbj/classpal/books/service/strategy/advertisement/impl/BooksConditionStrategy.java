package com.dbj.classpal.books.service.strategy.advertisement.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.dbj.classpal.books.client.dto.advertisement.AdvertisementLevelConditionDTO;
import com.dbj.classpal.books.client.dto.advertisement.AdvertisementLevelConditionOptionDTO;
import com.dbj.classpal.books.common.enums.advertisement.AdvertiseOptionTypeEnum;
import com.dbj.classpal.books.common.enums.advertisement.AdvertisementConditionCodeEnum;
import com.dbj.classpal.books.service.api.client.advertisement.AdvertisementContext;
import com.dbj.classpal.books.service.biz.books.IBooksCategoryRefBiz;
import com.dbj.classpal.books.service.biz.books.IBooksUserRefBiz;
import com.dbj.classpal.books.service.entity.books.BooksCategoryRef;
import com.dbj.classpal.books.service.entity.books.BooksUserRef;
import com.dbj.classpal.books.service.strategy.advertisement.IAdvertisementConditionStrategy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Yi
 * @since 2025-04-26 14:27
 */
@Component
public class BooksConditionStrategy implements IAdvertisementConditionStrategy {

    @Resource
    private IBooksUserRefBiz booksUserRefBiz;
    @Resource
    private IBooksCategoryRefBiz booksCategoryRefBiz;

    @Override
    public String getCode() {
        return AdvertisementConditionCodeEnum.BOOKS.getCode();
    }

    @Override
    public void doHandle(AdvertisementContext context) {
        List<BooksUserRef> booksUserRef = booksUserRefBiz.lambdaQuery()
                .eq(BooksUserRef::getAppUserId, context.getAppUserId())
                .list();
        Set<Integer> bookIds = booksUserRef.stream().map(BooksUserRef::getBooksId).collect(Collectors.toSet());
        AdvertisementLevelConditionDTO condition = context.getAdvertisementLevelCondition();
        List<AdvertisementLevelConditionOptionDTO> conditionOptionList = condition.getAdvertisementLevelConditionOptionList();
        for (AdvertisementLevelConditionOptionDTO optionDTO : conditionOptionList) {
            if(CollUtil.isEmpty(bookIds)) {
                condition.setIsEligible(false);
            } else {
                Set<Integer> ids = StrUtil.split(optionDTO.getOptionValue(), ',')
                        .stream()
                        .map(Integer::parseInt)
                        .collect(Collectors.toSet());
                if (AdvertiseOptionTypeEnum.BOOK_CATEGORY.getType().equals(optionDTO.getType())) {
                    List<BooksCategoryRef> booksCategoryRefs = booksCategoryRefBiz.lambdaQuery().in(BooksCategoryRef::getBooksId, bookIds).list();
                    Set<Integer> categoryIds = booksCategoryRefs.stream().map(BooksCategoryRef::getCategoryId).collect(Collectors.toSet());
                    condition.setIsEligible(categoryIds.stream().anyMatch(ids::contains));
                    break;
                } else if (AdvertiseOptionTypeEnum.BOOK_DESIGNATED.getType().equals(optionDTO.getType())) {
                    condition.setIsEligible(bookIds.stream().anyMatch(ids::contains));
                    break;
                }
            }
        }
    }
}
