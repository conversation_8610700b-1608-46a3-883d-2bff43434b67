package com.dbj.classpal.books.service.service.advertisement.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.service.service.advertisement.IAdvertisementConditionService;
import com.dbj.classpal.books.service.entity.advertisement.AdvertisementCondition;
import com.dbj.classpal.books.service.mapper.advertisement.AdvertisementConditionMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 广告条件表 业务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Deprecated
@Service
public class AdvertisementConditionServiceImpl extends ServiceImpl<AdvertisementConditionMapper, AdvertisementCondition> implements IAdvertisementConditionService {

}
