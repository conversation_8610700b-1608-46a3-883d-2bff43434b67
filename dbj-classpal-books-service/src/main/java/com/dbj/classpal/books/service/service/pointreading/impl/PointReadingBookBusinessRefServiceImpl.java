package com.dbj.classpal.books.service.service.pointreading.impl;

import com.dbj.classpal.books.common.bo.pointreading.PointReadingBookBusinessRefBatchSaveBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingBookBusinessRefSaveBO;
import com.dbj.classpal.books.common.dto.pointreading.PointReadingBookBusinessRefDTO;
import com.dbj.classpal.books.service.biz.pointreading.IPointReadingBookBusinessRefBiz;
import com.dbj.classpal.books.service.service.pointreading.IPointReadingBookBusinessRefService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import jakarta.annotation.Resource;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 点读书业务关联 服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
@Service
public class PointReadingBookBusinessRefServiceImpl implements IPointReadingBookBusinessRefService {

    @Resource
    private IPointReadingBookBusinessRefBiz pointReadingBookBusinessRefBiz;

    @Override
    public Integer save(PointReadingBookBusinessRefSaveBO saveBO) throws BusinessException {
        return pointReadingBookBusinessRefBiz.saveBusinessRef(saveBO);
    }

    @Override
    public Boolean saveBatch(PointReadingBookBusinessRefBatchSaveBO batchSaveBO) throws BusinessException {
        return pointReadingBookBusinessRefBiz.saveBatch(batchSaveBO);
    }

    @Override
    public Boolean delete(Integer id) throws BusinessException {
        return pointReadingBookBusinessRefBiz.deleteBusinessRef(id);
    }

    @Override
    public List<PointReadingBookBusinessRefDTO> getRefsByBookId(Integer bookId) throws BusinessException {
        return pointReadingBookBusinessRefBiz.getRefsByBookId(bookId);
    }

    @Override
    public List<PointReadingBookBusinessRefDTO> getRefsByBusiness(String businessType, Integer businessId) throws BusinessException {
        return pointReadingBookBusinessRefBiz.getRefsByBusiness(businessType, businessId);
    }

    @Override
    public List<PointReadingBookBusinessRefDTO> getRefsByBusinessType(String businessType) throws BusinessException {
        return pointReadingBookBusinessRefBiz.getRefsByBusinessType(businessType);
    }

    @Override
    public Boolean deleteByBookId(Integer bookId) throws BusinessException {
        return pointReadingBookBusinessRefBiz.deleteByBookId(bookId);
    }

    @Override
    public Boolean deleteByBusiness(String businessType, Integer businessId) throws BusinessException {
        return pointReadingBookBusinessRefBiz.deleteByBusiness(businessType, businessId);
    }

    @Override
    public Boolean bindBooksToBusiness(String businessType, Integer businessId, List<Integer> bookIds) throws BusinessException {
        return pointReadingBookBusinessRefBiz.bindBooksToBusiness(businessType, businessId, bookIds);
    }

    @Override
    public Boolean bindBusinessToBook(Integer bookId, String businessType, List<Integer> businessIds) throws BusinessException {
        return pointReadingBookBusinessRefBiz.bindBusinessToBook(bookId, businessType, businessIds);
    }
}
