package com.dbj.classpal.books.service.openapi.factory;

import com.dbj.framework.openapi.factory.DbjClientFactory;
import com.dbj.framework.openapi.provider.NoAuthProvider;
import com.dbj.framework.openapi.request.ApiRequest;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 应用信息专用客户端工厂
 * 创建用于访问应用信息API的请求实例
 * 不依赖CredentialManager，避免循环依赖
 */
@Component
public class AppInfoClientFactory {
    private final String baseUrl;
    private final DbjClientFactory dbjClientFactory;
    
    public AppInfoClientFactory(
            @Value("${dbj.api-client.base-url}") String baseUrl,
            DbjClientFactory dbjClientFactory) {
        this.baseUrl = baseUrl;
        this.dbjClientFactory = dbjClientFactory;
    }
    
    /**
     * 创建无认证的API请求实例
     * 用于注册应用和获取初始凭证
     * @return API请求实例
     */
    public ApiRequest createNoAuthClient() {
        return dbjClientFactory.createWithAuth(baseUrl, new NoAuthProvider());
    }
}