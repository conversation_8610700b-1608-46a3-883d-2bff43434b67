package com.dbj.classpal.books.service.service.advertisement.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.client.bo.advertisement.AdevertisementDelBO;
import com.dbj.classpal.books.client.bo.advertisement.AdvertisementAppBO;
import com.dbj.classpal.books.client.bo.advertisement.AdvertisementLevelConditionBO;
import com.dbj.classpal.books.client.bo.advertisement.AdvertisementLevelConditionOptionBO;
import com.dbj.classpal.books.client.bo.advertisement.AdvertisementPageBO;
import com.dbj.classpal.books.client.bo.advertisement.AdvertisementStatusUpdateBO;
import com.dbj.classpal.books.client.bo.advertisement.AdvertisementUpsertBO;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.dto.advertisement.AdvertisementAppDTO;
import com.dbj.classpal.books.client.dto.advertisement.AdvertisementDTO;
import com.dbj.classpal.books.client.dto.advertisement.AdvertisementLevelConditionDTO;
import com.dbj.classpal.books.common.enums.advertisement.AdvertiseLogicTypeEnum;
import com.dbj.classpal.books.common.enums.advertisement.AdvertiseRedirectTypeEnum;
import com.dbj.classpal.books.common.enums.advertisement.AdvertiseShowTypeEnum;
import com.dbj.classpal.books.service.api.client.advertisement.AdvertisementContext;
import com.dbj.classpal.books.service.biz.advertisement.IAdvertisementBiz;
import com.dbj.classpal.books.service.biz.advertisement.IAdvertisementLevelConditionBiz;
import com.dbj.classpal.books.service.biz.advertisement.IAdvertisementLevelConditionOptionBiz;
import com.dbj.classpal.books.service.entity.advertisement.Advertisement;
import com.dbj.classpal.books.service.entity.advertisement.AdvertisementLevelCondition;
import com.dbj.classpal.books.service.entity.advertisement.AdvertisementLevelConditionOption;
import com.dbj.classpal.books.service.factory.AdvertisementConditionStrategyFactory;
import com.dbj.classpal.books.service.mapper.advertisement.AdvertisementMapper;
import com.dbj.classpal.books.service.service.advertisement.IAdvertisementService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.redisson.config.RedissonRedisUtils;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.ADVERTISEMENT_NOT_EXIST_FAIL_CODE;
import static com.dbj.classpal.books.common.constant.AppErrorCode.ADVERTISEMENT_NOT_EXIST_FAIL_MSG;
import static com.dbj.classpal.books.common.constant.AppErrorCode.ADVERTISEMENT_PARAMETER_VERIFICATION_FAIL_CODE;
import static com.dbj.classpal.books.common.constant.RedisKeyConstants.DBJ_CLASSPAL_ADVERTISEMENT_SHOW_TIME;
import static com.dbj.classpal.books.common.constant.RedisKeyConstants.DBJ_CLASSPAL_ADVERTISEMENT_TYPE_CACHE_KEY;


/**
 * <p>
 * 广告信息表 业务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Service
public class AdvertisementServiceImpl extends ServiceImpl<AdvertisementMapper, Advertisement> implements IAdvertisementService {

    @Resource
    private IAdvertisementBiz advertisementBiz;

    @Resource
    private IAdvertisementLevelConditionBiz advertisementLevelConditionBiz;

    @Resource
    private IAdvertisementLevelConditionOptionBiz advertisementLevelConditionOptionBiz;

    @Resource
    private AdvertisementConditionStrategyFactory advertisementConditionStrategyFactory;

    @Resource
    private RedissonRedisUtils redisUtils;

    @Override
    public Page<AdvertisementDTO> getAdvertisementPage(PageInfo<AdvertisementPageBO> pageInfo) {
        Page<AdvertisementDTO> page = advertisementBiz.getAdvertisementPageList(pageInfo.getPage(), pageInfo.getData());
        List<AdvertisementDTO> advertisementDTOList = page.getRecords();
        if (CollUtil.isEmpty(advertisementDTOList)) {
            return page;
        }
        Set<Integer> ids = advertisementDTOList.stream().map(AdvertisementDTO::getId).collect(Collectors.toSet());
        Map<Integer, Long> conditionCountMap = getConditionCountMap(ids);
        for (AdvertisementDTO dto : advertisementDTOList) {
            dto.setConditionCount(conditionCountMap.getOrDefault(dto.getId(), 0L));
        }
        return page;
    }


    @Override
    public AdvertisementDTO getAdvertisementInfo(CommonIdApiBO bo) throws BusinessException {
        return getAdvertisementInfo(bo.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveAdvertisement(AdvertisementUpsertBO bo) throws BusinessException {
        checkAdvertisementUpsertParams(bo, false);
        Advertisement advertisement = buildAdvertisement(bo);
        //todo 字段赋值有待确定
        boolean save = advertisementBiz.save(advertisement);
        Assert.isTrue(save, () -> new BusinessException(ADVERTISEMENT_PARAMETER_VERIFICATION_FAIL_CODE, "信息添加失败"));
        handlerAdvertisementCondition(advertisement.getId(), bo.getAdvertisementLevelConditionList());
        redisUtils.delKey(MessageFormat.format(DBJ_CLASSPAL_ADVERTISEMENT_TYPE_CACHE_KEY, bo.getType()));
        return Boolean.TRUE;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateAdvertisement(AdvertisementUpsertBO bo) throws BusinessException {
        checkAdvertisementUpsertParams(bo, true);
        Advertisement advertisement = buildAdvertisement(bo);
        boolean update = advertisementBiz.updateById(advertisement);
        Assert.isTrue(update, () -> new BusinessException(ADVERTISEMENT_PARAMETER_VERIFICATION_FAIL_CODE, "信息更新失败"));
        deletedLevelConditionAndOptions(Collections.singletonList(bo.getId()));
        handlerAdvertisementCondition(advertisement.getId(), bo.getAdvertisementLevelConditionList());
        redisUtils.delKey(MessageFormat.format(DBJ_CLASSPAL_ADVERTISEMENT_TYPE_CACHE_KEY, bo.getType()));
        return Boolean.TRUE;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateAdvertisementStatus(AdvertisementStatusUpdateBO bo) {
        Advertisement advertisement = advertisementBiz.getById(bo.getId());
        if(advertisement == null) {
            return Boolean.TRUE;
        }
        Advertisement updAdvertisement = new Advertisement()
                .setId(bo.getId())
                .setStatus(bo.getStatus());
        if(bo.getStatus()) {
            updAdvertisement.setActivationTime(LocalDateTime.now());
        }
        redisUtils.delKey(MessageFormat.format(DBJ_CLASSPAL_ADVERTISEMENT_TYPE_CACHE_KEY, advertisement.getType()));
        return advertisementBiz.updateById(updAdvertisement);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteAdvertisement(AdevertisementDelBO bo) throws BusinessException {
        Assert.notEmpty(bo.getIds(), () -> new BusinessException(ADVERTISEMENT_PARAMETER_VERIFICATION_FAIL_CODE, "请选择要删除的数据"));
        advertisementBiz.removeByIds(bo.getIds());
        deletedLevelConditionAndOptions(bo.getIds());
        redisUtils.delKey(MessageFormat.format(DBJ_CLASSPAL_ADVERTISEMENT_TYPE_CACHE_KEY, bo.getAdvertisementType()));
        return Boolean.TRUE;
    }

    @Override
    public List<AdvertisementAppDTO> getAppUserAdvertisement(AdvertisementAppBO bo) {
        List<AdvertisementDTO> advertisementDTOList;
        String cacheKey = MessageFormat.format(DBJ_CLASSPAL_ADVERTISEMENT_TYPE_CACHE_KEY, bo.getAdvertisementType());
        if(redisUtils.hasKey(cacheKey)) {
            advertisementDTOList = JSON.parseArray(redisUtils.getValue(cacheKey), AdvertisementDTO.class);
        } else {
            List<Advertisement> advertisementList= advertisementBiz.lambdaQuery()
                    .eq(Advertisement::getStatus, true)
                    .eq(Advertisement::getType, bo.getAdvertisementType())
                    .orderByDesc(Advertisement::getSort)
                    .orderByDesc(Advertisement::getCreateTime)
                    .orderByDesc(Advertisement::getId)
                    .list();
            if (CollUtil.isEmpty(advertisementList)) {
                return Collections.emptyList();
            }
            advertisementDTOList = BeanUtil.copyToList(advertisementList, AdvertisementDTO.class);
            //过滤条件
            Set<Integer> ids = advertisementList.stream().map(Advertisement::getId).collect(Collectors.toSet());
            Map<Integer, List<AdvertisementLevelConditionDTO>> conditionMap = advertisementLevelConditionBiz.getMapByAdvertisementIds(ids);
            advertisementDTOList.forEach(dto -> dto.setAdvertisementLevelConditionList(conditionMap.get(dto.getId())));
            redisUtils.setValue(cacheKey, JSON.toJSONString(advertisementDTOList), 7, TimeUnit.DAYS);
        }

        Map<Integer, List<AdvertisementLevelConditionDTO>> conditionMap = advertisementDTOList.stream()
                .map(AdvertisementDTO::getAdvertisementLevelConditionList)
                .filter(CollUtil::isNotEmpty)
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(AdvertisementLevelConditionDTO::getAdvertisementId));

        //用户主信息
        Map<Integer, Boolean> advertisementEligibleMap = conditionMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> advertisementConditionFilter(BeanUtil.copyProperties(bo, AdvertisementContext.class), entry.getValue())
                ));
        //符合条件的广告
        List<AdvertisementDTO> eligibleList = advertisementDTOList.stream()
                .filter(dto -> !advertisementEligibleMap.containsKey(dto.getId()) || advertisementEligibleMap.get(dto.getId()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(eligibleList)) {
            return Collections.emptyList();
        }

        List<AdvertisementAppDTO> advertisementAppDTOs = BeanUtil.copyToList(eligibleList, AdvertisementAppDTO.class);
        //todo 根据提醒次数过滤广告信息，app去处理了，不用后端处理
//        filterAdvertisementByShowType(bo, advertisementAppDTOs);
        return advertisementAppDTOs;
    }

    @Deprecated
    private void filterAdvertisementByShowType(AdvertisementAppBO bo, List<AdvertisementAppDTO> advertisementAppDTOs) {
        //保存拉去信息
        if(CollUtil.isNotEmpty(advertisementAppDTOs)){
            //对每个广告进行查询判断，是否只显示一次，或者每天显示一次
            Iterator<AdvertisementAppDTO> advertisementAppDTOIterator =  advertisementAppDTOs.iterator();
            while (advertisementAppDTOIterator.hasNext()) {
                AdvertisementAppDTO advertisementAppDTO = advertisementAppDTOIterator.next();
                String adUserAdShowKey = MessageFormat.format(DBJ_CLASSPAL_ADVERTISEMENT_SHOW_TIME, bo.getAppUserId(), bo.getRelationType(), advertisementAppDTO.getId());
                String showTime = redisUtils.getValue(adUserAdShowKey);
                String today = DateUtil.format(new Date(),"yyyy-MM-dd");
                if(StringUtils.isNotEmpty(showTime)){
                    Integer showType = advertisementAppDTO.getShowType();
                    //每天显示一次 如果存有时间，则表示已经显示过了 不需要显示
                    if(Objects.equals(showType, AdvertiseShowTypeEnum.ONLY_ONCE.getValue())){
                        advertisementAppDTOIterator.remove();
                    }else if(Objects.equals(showType, AdvertiseShowTypeEnum.ONCE_A_DAY.getValue())){
                        //每天显示一次 判断时间是否与今天相等，如果相等则直接跳过
                        if(StringUtils.equals(today, showTime)){
                            advertisementAppDTOIterator.remove();
                        }
                    }
                }
            }
        }
    }


    /**
     * 获取广告条件数量
     * @param advertisementIds 广告id集合
     * @return Map<广告id, 广告条件数量>
     */
    private Map<Integer, Long> getConditionCountMap(Set<Integer> advertisementIds) {
        if(CollUtil.isEmpty(advertisementIds)) {
            return Collections.emptyMap();
        }
//        List<Object[]> result = advertisementLevelConditionBiz.getBaseMapper().selectObjs(
//                new QueryWrapper<AdvertisementLevelCondition>()
//                        .select("advertisement_id, COUNT(*) as count")
//                        .in("advertisement_id", advertisementIds)
//                        .isNotNull("advertisement_condition_id")
//                        .groupBy("advertisement_id")
//        );
//        return result.stream()
//                .collect(Collectors.toMap(
//                        row -> (Integer) row[0],
//                        row -> ((Number) row[1]).longValue()
//                ));
        String groupByColumn = "advertisement_id";
        QueryWrapper<AdvertisementLevelCondition> projectWrapper =  Wrappers.query();
        projectWrapper.select(groupByColumn, "COUNT(*) AS count")
                .in(groupByColumn, advertisementIds)
                .isNotNull("condition_type")
                .groupBy(groupByColumn);
        return advertisementLevelConditionBiz.listMaps(projectWrapper).stream()
                .collect(Collectors.toMap(
                        map -> (Integer)map.get(groupByColumn),
                        map -> (Long)map.get("count")));
    }

    /**
     * 校验广告参数
     *
     * @param bo 广告参数
     * @param isUpdate 是否更新
     * @throws BusinessException 业务异常
     */
    private void checkAdvertisementUpsertParams(AdvertisementUpsertBO bo, boolean isUpdate) throws BusinessException {
        //标题不能重复
        LambdaQueryWrapper<Advertisement> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Advertisement::getTitle, bo.getTitle())
                .eq(Advertisement::getType, bo.getType());
        if (isUpdate) {
            queryWrapper.ne(Advertisement::getId, bo.getId());
        }
        long count = advertisementBiz.count(queryWrapper);
        Assert.isTrue(count == 0, () -> new BusinessException(ADVERTISEMENT_PARAMETER_VERIFICATION_FAIL_CODE, "广告策略标题已存在"));
        //参数校验
        Integer redirectType = bo.getRedirectType();
        if(AdvertiseRedirectTypeEnum.EXTERNAL_LINK.getType().equals(redirectType)) {
            Assert.isFalse(StrUtil.isEmpty(bo.getRedirectUri()),
                    () -> new BusinessException(ADVERTISEMENT_PARAMETER_VERIFICATION_FAIL_CODE, "请输入跳转路径"));
        } else if(AdvertiseRedirectTypeEnum.INTERNAL_MP.getType().equals(redirectType)) {
            Assert.isFalse(StrUtil.isEmpty(bo.getRedirectUri()),
                    () -> new BusinessException(ADVERTISEMENT_PARAMETER_VERIFICATION_FAIL_CODE, "请输入跳转路径"));
            Assert.isFalse(StrUtil.isEmpty(bo.getRedirectAppId()),
                    () -> new BusinessException(ADVERTISEMENT_PARAMETER_VERIFICATION_FAIL_CODE, "请输入外部小程序appid"));
        } else if(AdvertiseRedirectTypeEnum.EXTERNAL_MP.getType().equals(redirectType)) {
            Assert.isFalse(StrUtil.isEmpty(bo.getContent()),
                    () -> new BusinessException(ADVERTISEMENT_PARAMETER_VERIFICATION_FAIL_CODE, "请输入图文详情"));
        }
    }


    private Advertisement buildAdvertisement(AdvertisementUpsertBO bo)  {
        Advertisement domain = BeanUtil.copyProperties(bo, Advertisement.class);
        if(bo.getStatus()) {
            domain.setActivationTime(LocalDateTime.now());
        }
        return domain;
    }

    /**
     * 保存广告条件
     *
     * @param advertisementLevelConditionList 广告条件层级表
     * @param advertisementId                  广告id
     */
    private void handlerAdvertisementCondition(Integer advertisementId, List<AdvertisementLevelConditionBO> advertisementLevelConditionList) throws BusinessException {
        if (CollUtil.isNotEmpty(advertisementLevelConditionList)) {
            saveAdvertisementCondition(null, advertisementLevelConditionList, advertisementId, LocalDateTime.now());
        }
    }

    /**
     * 保存广告条件
     * @param parentId                                  父级id
     * @param advertisementLevelConditionBOList         广告条件层级表
     * @param advertisementId                           广告id
     * @param now                                       当前时间
     */
    private void saveAdvertisementCondition(Integer parentId,
                                            List<AdvertisementLevelConditionBO> advertisementLevelConditionBOList,
                                            Integer advertisementId,
                                            LocalDateTime now) throws BusinessException {
        for (AdvertisementLevelConditionBO conditionBO : advertisementLevelConditionBOList) {
            Assert.isTrue(Objects.nonNull(conditionBO.getLogicType()) || StrUtil.isNotEmpty(conditionBO.getConditionType()),
                    () -> new BusinessException(ADVERTISEMENT_PARAMETER_VERIFICATION_FAIL_CODE, "条件参数错误"));
            AdvertisementLevelCondition conditionDomain = new AdvertisementLevelCondition();
            conditionDomain
                    .setAdvertisementId(advertisementId)
                    .setParentId(parentId)
                    .setLogicType(conditionBO.getLogicType())
                    .setLevel(conditionBO.getLevel())
                    .setName(conditionBO.getName())
                    .setConditionType(conditionBO.getConditionType())
                    .setCreateTime(now)
                    .setUpdateTime(now);
            advertisementLevelConditionBiz.save(conditionDomain);

            List<AdvertisementLevelConditionOptionBO> conditionOptionList = conditionBO.getAdvertisementLevelConditionOptionList();
            Assert.isTrue(StrUtil.isEmpty(conditionBO.getConditionType()) || CollUtil.isNotEmpty(conditionOptionList),
                    () -> new BusinessException(ADVERTISEMENT_PARAMETER_VERIFICATION_FAIL_CODE, "请选择要匹配的条件选项"));
            if (CollUtil.isNotEmpty(conditionOptionList)) {
                List<AdvertisementLevelConditionOption> advertisementLevelConditionOptions = new ArrayList<>();
                for (AdvertisementLevelConditionOptionBO levelConditionOptionBO : conditionOptionList) {
                    AdvertisementLevelConditionOption levelConditionOptionDomain = new AdvertisementLevelConditionOption();
                    levelConditionOptionDomain
                            .setAdvertisementLevelConditionId(conditionDomain.getId())
                            .setAdvertisementId(advertisementId)
                            .setType(levelConditionOptionBO.getType())
                            .setOptionValue(levelConditionOptionBO.getOptionValue())
                            .setOptionName(levelConditionOptionBO.getOptionName())
                            .setMaxQty(levelConditionOptionBO.getMaxQty())
                            .setMinQty(levelConditionOptionBO.getMinQty())
                            .setCreateTime(now)
                            .setUpdateTime(now);
                    advertisementLevelConditionOptions.add(levelConditionOptionDomain);
                }
                advertisementLevelConditionOptionBiz.saveBatch(advertisementLevelConditionOptions);
            }

            List<AdvertisementLevelConditionBO> childrenList = conditionBO.getChildrenList();
            Assert.isTrue(Objects.isNull(conditionBO.getLogicType()) || CollUtil.isNotEmpty(childrenList),
                    () -> new BusinessException(ADVERTISEMENT_PARAMETER_VERIFICATION_FAIL_CODE, "请选择条件"));

            if (CollUtil.isNotEmpty(childrenList)) {
                saveAdvertisementCondition(conditionDomain.getId(), childrenList, advertisementId, now);
            }
        }
    }

    /**
     * 批量删除广告条件
     *
     * @param advertisementIds 广告id集合
     */
    private void deletedLevelConditionAndOptions(Collection<Integer> advertisementIds) {
        advertisementLevelConditionBiz.remove(new LambdaQueryWrapper<AdvertisementLevelCondition>()
                .in(AdvertisementLevelCondition::getAdvertisementId, advertisementIds));
        advertisementLevelConditionOptionBiz.remove(new LambdaQueryWrapper<AdvertisementLevelConditionOption>()
                .in(AdvertisementLevelConditionOption::getAdvertisementId, advertisementIds));
    }

    private boolean advertisementConditionFilter(AdvertisementContext context,
                                                 List<AdvertisementLevelConditionDTO> conditionDTOList) {
        return advertisementConditionFilter(context, null, conditionDTOList);
    }

    /**
     * 广告条件过滤
     *
     * @param context          广告上下文
     * @param conditionDTOList 广告条件集合
     * @return 是否满足条件
     */
    private boolean advertisementConditionFilter(AdvertisementContext context,
                                                 Integer logicType,
                                                 List<AdvertisementLevelConditionDTO> conditionDTOList) {
        if(CollUtil.isEmpty(conditionDTOList)) {
            return true;
        }
        //顶级节点不存在条件 -> 只需要每次递归处理子集
        for (AdvertisementLevelConditionDTO conditionDTO : conditionDTOList) {
            //组节点
            List<AdvertisementLevelConditionDTO> childrenList = conditionDTO.getChildrenList();
            if (CollUtil.isNotEmpty(childrenList)) {
                conditionDTO.setIsEligible(advertisementConditionFilter(context, conditionDTO.getLogicType(), childrenList));
            } else {
                advertisementConditionStrategyFactory.handle(context.setAdvertisementLevelCondition(conditionDTO));
                Boolean isEligible = conditionDTO.getIsEligible();
                /*
                 *  满足以下情况则无需执行后续条件
                 *  1.逻辑关系为且时，当前条件已经不满足  结果为 不符合
                 *  2.逻辑关系为或时，当前条件已经满足    结果为 符合
                 *  3.逻辑关系为非时，当前条件已经满足    结果为 不符合
                 */
                if (AdvertiseLogicTypeEnum.AND.getType().equals(logicType) && !isEligible) {
                    return false;
                } else if (AdvertiseLogicTypeEnum.OR.getType().equals(logicType) && isEligible) {
                    return true;
                } else if (AdvertiseLogicTypeEnum.NOT.getType().equals(logicType) && isEligible) {
                    return false;
                }
            }
        }
        // 根节点
        if (logicType == null) {
            return CollUtil.getFirst(conditionDTOList).getIsEligible();
        }
        // 每一个子集的结果
        switch (AdvertiseLogicTypeEnum.of(logicType)) {
            case OR:
                return conditionDTOList.stream().anyMatch(AdvertisementLevelConditionDTO::getIsEligible);
            case AND:
                return conditionDTOList.stream().allMatch(AdvertisementLevelConditionDTO::getIsEligible);
            case NOT:
                return conditionDTOList.stream().noneMatch(AdvertisementLevelConditionDTO::getIsEligible);
            default:
                return false;
        }
    }


    public AdvertisementDTO getAdvertisementInfo(Integer id) throws BusinessException {
        Advertisement advertisement = advertisementBiz.getById(id);
        Assert.notNull(advertisement, () -> new BusinessException(ADVERTISEMENT_NOT_EXIST_FAIL_CODE, ADVERTISEMENT_NOT_EXIST_FAIL_MSG));
        AdvertisementDTO advertisementDTO = BeanUtil.copyProperties(advertisement, AdvertisementDTO.class);
        //展示条件
        Map<Integer, List<AdvertisementLevelConditionDTO>> advertisementLevelConditionDTOList = advertisementLevelConditionBiz
                .getMapByAdvertisementIds(Collections.singletonList(id));
        advertisementDTO.setAdvertisementLevelConditionList(advertisementLevelConditionDTOList.getOrDefault(id, Collections.emptyList()));
        return advertisementDTO;
    }
}
