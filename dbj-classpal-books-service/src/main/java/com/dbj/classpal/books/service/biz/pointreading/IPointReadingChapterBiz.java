package com.dbj.classpal.books.service.biz.pointreading;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingChapterQueryBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingChapterSaveBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingChapterUpdateBO;
import com.dbj.classpal.books.common.dto.pointreading.PointReadingChapterDTO;
import com.dbj.classpal.books.service.entity.pointreading.PointReadingChapter;
import com.dbj.classpal.framework.commons.exception.BusinessException;

import java.util.List;

/**
 * 点读书章节 业务接口
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface IPointReadingChapterBiz extends IService<PointReadingChapter> {

    /**
     * 分页查询点读书章节
     *
     * @param page 分页参数
     * @param queryBO 查询条件
     * @return 分页结果
     */
    Page<PointReadingChapterDTO> pageChapter(Page<PointReadingChapter> page, PointReadingChapterQueryBO queryBO) throws BusinessException;

    /**
     * 查询章节详情
     *
     * @param id 章节ID
     * @return 章节详情
     */
    PointReadingChapterDTO detail(Integer id) throws BusinessException;

    /**
     * 保存点读书章节
     *
     * @param saveBO 保存参数
     * @return 章节ID
     */
    Integer savePage(PointReadingChapterSaveBO saveBO) throws BusinessException;

    /**
     * 更新点读书章节
     *
     * @param updateBO 更新参数
     * @return 是否成功
     */
    boolean updatePage(PointReadingChapterUpdateBO updateBO) throws BusinessException;

    /**
     * 删除点读书章节
     *
     * @param id 章节ID
     * @return 是否成功
     */
    boolean deletePage(Integer id) throws BusinessException;

    /**
     * 批量删除点读书章节
     *
     * @param ids 章节ID列表
     * @return 是否成功
     */
    boolean deleteBatch(List<Integer> ids) throws BusinessException;

    /**
     * 查询目录下的章节列表
     *
     * @param menuId 目录ID
     * @return 章节列表
     */
    List<PointReadingChapterDTO> getChapterByMenu(Integer menuId) throws BusinessException;

    /**
     * 查询点读书下的章节列表
     *
     * @param bookId 点读书ID
     * @return 章节列表
     */
    List<PointReadingChapterDTO> getChapterByBook(Integer bookId) throws BusinessException;

    /**
     * 更新排序
     *
     * @param id 章节ID
     * @param sortNum 排序号
     * @return 是否成功
     */
    boolean updateSort(Integer id, Integer sortNum) throws BusinessException;

}
