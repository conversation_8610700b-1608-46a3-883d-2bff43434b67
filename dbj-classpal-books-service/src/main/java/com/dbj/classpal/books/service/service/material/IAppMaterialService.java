package com.dbj.classpal.books.service.service.material;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.common.bo.common.CommonIdBO;
import com.dbj.classpal.books.common.bo.file.ExcelFileEntity;
import com.dbj.classpal.books.common.bo.material.*;
import com.dbj.classpal.books.common.dto.material.AppMaterialBusinessRefDTO;
import com.dbj.classpal.books.common.dto.material.AppMaterialQueryDTO;
import com.dbj.classpal.books.common.dto.material.AppMaterialQueryDicTreeDTO;
import com.dbj.classpal.books.common.dto.material.AppMaterialStatisticsSizeDTO;
import com.dbj.classpal.books.common.dto.mns.MnsMessageBodyDTO;
import com.dbj.classpal.books.service.entity.material.AppMaterial;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppMaterialService
 * Date:     2025-04-08 16:21:26
 * Description: 表名： ,描述： 表
 */
@Mapper
public interface IAppMaterialService {

    /**
     * 分页查询素材中心列表
     * @param pageRequest
     * @return
     */
    Page<AppMaterialQueryDTO>pageInfo(PageInfo<AppMaterialQueryBO> pageRequest);


    /**
     * 查询素材中心资源是否上传过
     * @param bo
     * @return
     */
    Boolean materialExist(AppMaterialExistQueryBO bo) throws BusinessException;


    /**
     * 新增素材中心数据
     * @param bo
     * @return
     */
    AppMaterial saveMaterial(AppMaterialSaveBO bo) throws BusinessException;


    /**
     * 创建排序号
     * @param parentId
     * @return
     */
    Integer createSort(Integer parentId);

    /**
     * 新建文件夹
     * @param saveMkdirBO
     * @return
     */
    Boolean materialMkdir(AppMaterialSaveMkdirBO saveMkdirBO);


    /**
     * 移动文件资源
     * @param ioBo
     * @return
     */
    Boolean moveMaterial(AppMaterialIOBO ioBo) throws BusinessException;


    /**
     * 批量移动文件资源
     * @param ioBo
     * @return
     */
    Boolean batchMoveMaterial(AppMaterialBatchIOBO ioBo) throws BusinessException;


    /**
     * 复制文件资源
     * @param ioBo
     * @return
     */
    Boolean copyMaterial(AppMaterialIOBO ioBo) throws BusinessException;

    /**
     * 批量复制文件资源
     * @param ioBo
     * @return
     */
    Boolean batchCopyMaterial(AppMaterialBatchIOBO ioBo) throws BusinessException;

    /**
     * 获取所有文件夹结构树
     * @return
     */
    AppMaterialQueryDicTreeDTO getAllDirectsTree();


    /**
     * 查询该文件夹父节点列表
     * @param id
     * @return
     */
    List<AppMaterialQueryDTO> getMaterialParentsPath(Integer id);



    /**
     * 查询该文件夹父节点列表
     * @param id
     * @return
     */
    String getMaterialParentsNames(Integer id);


    /**
     * 查询所有子节点，并判断目标ID是否存在
     * @param ioBo
     * @return
     */
    Integer checkAimIdInChildren(AppMaterialIOBO ioBo);

    /**
     * 重命名
     */
    Boolean reNameMaterial(AppMaterialReNameBO reNameBO);

    /**
     * 编辑字幕
     */
    Boolean editCaption(AppMaterialEditCaptionBO editCaptionBO);


    /**
     * 删除素材
     */
    Boolean deleteMaterial(CommonIdBO bo) throws BusinessException;

    /**
     * 批量删除素材
     */
    Boolean batchDeleteMaterial(AppMaterialBatchCommonIdBO bo) throws BusinessException;

    /**
     * 查询已使用文件大小
     * @return
     */
    AppMaterialStatisticsSizeDTO usedSize();


    /**
     * 获取当前文件夹下所有文件
     * @return
     */
    List<AppMaterial> getChildrenFiles(Integer id);

    /**
     * 获取多个文件夹下所有文件
     * @return
     */
    List<AppMaterial> getBatchDirChildrenFiles(List<Integer>ids);


    /**
     * 上传文件，提交分析模板任务，并通知查询转码结果
     * @param excelFileEntity
     * @throws Exception
     */
    void getMaterialFileHandler(ExcelFileEntity excelFileEntity) throws Exception;

    /**
     * 查询分析模板任务结果，返回最优模板id并通知转码
     * @param excelFileEntity
     * @throws Exception
     */
    void getAnalysisMaterial(ExcelFileEntity excelFileEntity) throws Exception;

    /**
     * 转码任务
     * @param excelFileEntity
     * @throws Exception
     */
    void getTranscodeMaterial(ExcelFileEntity excelFileEntity) throws Exception;

    /**
     * 查询转码结果任务
     * @param excelFileEntity
     * @throws Exception
     */
    void getQueryTransCodeAndSaveMaterial(ExcelFileEntity excelFileEntity) throws Exception;


    /**
     * 正式环境阿里mns队列处理转码业务接口
     * @param response
     * @throws Exception
     */
    void getProEnvMnsMaterialHandler(MnsMessageBodyDTO response) throws Exception;
}
