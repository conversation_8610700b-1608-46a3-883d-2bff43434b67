package com.dbj.classpal.books.service.mapper.advertisement;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dbj.classpal.books.client.dto.advertisement.AdvertisementLevelConditionOptionDTO;
import com.dbj.classpal.books.service.entity.advertisement.AdvertisementLevelConditionOption;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 广告层级条件选项关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
public interface AdvertisementLevelConditionOptionMapper extends BaseMapper<AdvertisementLevelConditionOption> {

    /**
     * 根据广告层级条件id获取广告层级条件选项
     *
     * @param advertisementLevelConditionIds 广告层级条件id
     * @return 广告层级条件选项
     */
    List<AdvertisementLevelConditionOptionDTO> getByAdvertisementLevelConditionIds(@Param("advertisementLevelConditionIds") Collection<Integer> advertisementLevelConditionIds);
}
