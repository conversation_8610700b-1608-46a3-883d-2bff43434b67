package com.dbj.classpal.books.service.strategy.basicConfig.common.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.enums.BasicConfigBizTypeEnum;
import com.dbj.classpal.books.service.biz.question.QuestionBiz;
import com.dbj.classpal.books.service.entity.config.BasicConfig;
import com.dbj.classpal.books.service.entity.question.Question;
import com.dbj.classpal.books.service.strategy.basicConfig.handler.IBasicConfigCommonBusinessStrategyHandler;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.APP_SUBJECT_HAS_QUESTIONS_CODE;
import static com.dbj.classpal.books.common.constant.AppErrorCode.APP_SUBJECT_HAS_QUESTIONS_MSG;

@Service("basicConfigSubjectRefBizStrategy")
public class BasicConfigSubjectRefBizStrategy extends IBasicConfigCommonBusinessStrategyHandler {

    @Resource
    private  QuestionBiz questionBiz;

    @Override
    public Map<Integer, Long> getRefMap(List<Integer> ids) {
        Map<Integer, Long> refMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(ids)) {
            List<Question> questions = questionBiz.getBaseMapper().selectList(new LambdaQueryWrapper<Question>().in(Question::getSubjectId, ids));
            Map<Integer, Long> subjectIdToCountMap = questions.stream()
                    .collect(Collectors.groupingBy(
                            Question::getSubjectId,
                            Collectors.counting()
                    ));
            refMap = ids.stream()
                    .collect(Collectors.toMap(
                            id -> id,
                            id -> subjectIdToCountMap.getOrDefault(id, 0L)
                    ));
        }
        return refMap;
    }

    @Override
    public void throwException(String type) throws BusinessException {
        throw new BusinessException(APP_SUBJECT_HAS_QUESTIONS_CODE,APP_SUBJECT_HAS_QUESTIONS_MSG);
    }
}
