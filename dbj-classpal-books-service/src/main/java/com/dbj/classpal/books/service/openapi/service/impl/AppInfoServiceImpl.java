package com.dbj.classpal.books.service.openapi.service.impl;

import com.dbj.business.external.client.model.request.AppInfoRequest;
import com.dbj.business.external.client.model.response.AppInfoResponse;
import com.dbj.classpal.books.service.openapi.constant.OpenapiConstants;
import com.dbj.classpal.books.service.openapi.factory.AppInfoClientFactory;
import com.dbj.classpal.books.service.openapi.service.AppInfoService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.framework.openapi.request.ApiRequest;
import com.dbj.framework.openapi.response.ApiResponse;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;


/**
 * description: description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/04/27 14:38:11
 */
@Service
@Slf4j
public class AppInfoServiceImpl implements AppInfoService {

    private final AppInfoClientFactory appInfoClientFactory;

    public AppInfoServiceImpl(AppInfoClientFactory appInfoClientFactory) {
        this.appInfoClientFactory = appInfoClientFactory;
    }

    @Override
    public void registerApp(String clientId, String appName, String appDesc) {

    }

    @Override
    public AppInfoResponse createApp(AppInfoRequest appInfoRequest) throws BusinessException {
        ApiResponse<AppInfoResponse> response;
        try {
            ApiRequest apiRequest = appInfoClientFactory.createNoAuthClient();
            response = apiRequest.postSync(
                    OpenapiConstants.API_BASE_PATH + "/create",
                    appInfoRequest,
                    AppInfoResponse.class);
            if (response.isSuccess() && response.getData() != null) {
                log.info("应用注册成功: {}, appKey: {}", appInfoRequest.getAppName(), response.getData().getAppId());
                return response.getData();
            } else {
                log.error("应用注册失败: {}", response.getMessage());
                throw new BusinessException(OPENAPI_REGISTER_FAIL_CODE,OPENAPI_REGISTER_FAIL_MSG);

            }
        } catch (Exception e) {
            log.error("注册应用时发生异常", e);
            throw new BusinessException(OPENAPI_REGISTER_EXCEPTION_CODE,OPENAPI_REGISTER_EXCEPTION_MSG);
        }
    }

    @Override
    public AppInfoResponse getAppInfo(String appId) throws BusinessException {
        Map<String, Object> params = new HashMap<>();
        params.put("appId", appId);
        ApiResponse<AppInfoResponse> response;

        try {
            ApiRequest apiRequest = appInfoClientFactory.createNoAuthClient();
            response = apiRequest.getSync(
                    OpenapiConstants.API_BASE_PATH + "/info",
                    params,
                    AppInfoResponse.class);

            if (response.isSuccess() && response.getData() != null) {
                log.info("获取应用信息成功: appId={}", appId);
                return response.getData();
            } else {
                log.error("获取应用信息失败: {}", response.getMessage());
                throw new BusinessException(OPENAPI_GET_APP_INFO_EXCEPTION_CODE,OPENAPI_GET_APP_INFO_EXCEPTION_MSG);
            }
        } catch (Exception e) {
            log.error("获取应用信息时发生异常: appId={}", appId, e);
            throw new BusinessException(OPENAPI_GET_APP_INFO_EXCEPTION_CODE,OPENAPI_GET_APP_INFO_EXCEPTION_MSG);
        }
    }

    @Override
    public List<AppInfoResponse> getAppsByClientId(String clientId) throws BusinessException {
        Map<String, Object> params = new HashMap<>();
        params.put("clientId", clientId);
        ApiResponse<List<AppInfoResponse>> response;
        try {
            ApiRequest apiRequest = appInfoClientFactory.createNoAuthClient();
            response = apiRequest.getSync(
                    OpenapiConstants.API_BASE_PATH + "/getAppsByClientId",
                    params,
                    new TypeReference<List<AppInfoResponse>>() {});

            if (response.isSuccess() && response.getData() != null) {
                log.info("根据客户端ID获取应用信息成功: clientId={}", clientId);
                return response.getData();
            } else {
                log.error("根据客户端ID获取应用信息失败: {}", response.getMessage());
                throw new BusinessException(OPENAPI_GET_APP_INFO_FAIL_CODE,OPENAPI_GET_APP_INFO_FAIL_MSG);
            }
        } catch (Exception e) {
            log.error("根据客户端ID获取应用信息时发生异常: clientId={}", clientId, e);
            throw new BusinessException(OPENAPI_GET_APP_INFO_FAIL_CODE,OPENAPI_GET_APP_INFO_FAIL_MSG);
        }
    }

    @Override
    public List<AppInfoResponse> listApps(int page, int pageSize) throws BusinessException {
        Map<String, Object> params = new HashMap<>();
        params.put("page", String.valueOf(page));
        params.put("pageSize", String.valueOf(pageSize));
        ApiResponse<List<AppInfoResponse>> response;
        try {
            ApiRequest apiRequest = appInfoClientFactory.createNoAuthClient();
            response = apiRequest.getSync(
                    OpenapiConstants.API_BASE_PATH + "/list",
                    params,
                    new TypeReference<List<AppInfoResponse>>() {});

            if (response.isSuccess()) {
                List<AppInfoResponse> apps = response.getData();
                log.info("获取应用列表成功: page={}, pageSize={}, count={}",
                        page, pageSize, apps != null ? apps.size() : 0);
                return apps != null ? apps : Collections.emptyList();
            } else {
                log.error("获取应用列表失败: {}", response.getMessage());
                throw new BusinessException(OPENAPI_GET_APP_LIST_FAIL_CODE,OPENAPI_GET_APP_LIST_FAIL_MSG);
            }
        } catch (Exception e) {
            log.error("获取应用列表时发生异常: page={}, pageSize={}", page, pageSize, e);
            throw new BusinessException(OPENAPI_GET_APP_LIST_FAIL_CODE,OPENAPI_GET_APP_LIST_FAIL_MSG);
        }
    }

    @Override
    public Boolean updateApp(AppInfoRequest appInfoRequest) throws BusinessException {
        ApiResponse<Boolean> response;
        try {
            ApiRequest apiRequest = appInfoClientFactory.createNoAuthClient();
            response = apiRequest.postSync(
                    OpenapiConstants.API_BASE_PATH + "/update",
                    appInfoRequest,
                    Boolean.class);

            if (response.isSuccess() && response.getData() != null) {
                log.info("更新应用信息成功: appId={}", appInfoRequest.getAppId());
                return response.getData();
            } else {
                log.error("更新应用信息失败: {}", response.getMessage());
                throw new BusinessException(OPENAPI_UPDATE_APP_FAIL_CODE,OPENAPI_UPDATE_APP_FAIL_MSG);
            }
        } catch (Exception e) {
            log.error("更新应用信息时发生异常: appId={}", appInfoRequest.getAppId(), e);
            throw new BusinessException(OPENAPI_UPDATE_APP_FAIL_CODE,OPENAPI_UPDATE_APP_FAIL_MSG);
        }
    }

    @Override
    public String refreshAppSecret(String appId) throws BusinessException {
        Map<String, Object> params = new HashMap<>();
        params.put("appId", appId);
        ApiResponse<String> response = null;

        try {
            ApiRequest apiRequest = appInfoClientFactory.createNoAuthClient();
            response = apiRequest.postSync(
                    OpenapiConstants.API_BASE_PATH + "/refreshSecret",
                    params,
                    String.class);

            if (response.isSuccess() && response.getData() != null) {
                log.info("刷新应用密钥成功: appId={}", appId);
                return response.getData();
            } else {
                log.error("刷新应用密钥失败: {}", response.getMessage());
                throw new BusinessException(OPENAPI_REFRESH_APP_SECRET_FAIL_CODE,OPENAPI_REFRESH_APP_SECRET_FAIL_MSG);
            }
        } catch (Exception e) {
            log.error("刷新应用密钥时发生异常: appId={}", appId, e);
            throw new BusinessException(OPENAPI_REFRESH_APP_SECRET_FAIL_CODE,OPENAPI_REFRESH_APP_SECRET_FAIL_MSG);
        }
    }

}
