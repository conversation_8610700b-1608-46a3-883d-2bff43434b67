package com.dbj.classpal.books.service.mapper.album;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dbj.classpal.books.common.bo.album.AppAlbumElementsBusinessRefQueryCommonBO;
import com.dbj.classpal.books.common.dto.album.AppAlbumElementsBusinessRefMaterialQueryDTO;
import com.dbj.classpal.books.common.dto.album.AppAlbumElementsBusinessRefQueryDTO;
import com.dbj.classpal.books.service.entity.album.AppAlbumElementsBusinessRef;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppAlbumElementsBusinessRefMapper
 * Date:     2025-04-15 10:20:08
 * Description: 表名： ,描述： 表
 */
public interface AppAlbumElementsBusinessRefMapper extends BaseMapper<AppAlbumElementsBusinessRef> {

    /**
     * 判断专辑分类的专辑是否存在业务关联引用
     * @param id
     * @return
     */
    Integer checkMenuRefCount(@Param("id")Integer id);

    /**
     * 查询关联引用列表
     * @param bo
     * @return
     */
    List<AppAlbumElementsBusinessRefQueryDTO> refBusinessList(AppAlbumElementsBusinessRefQueryCommonBO bo);
}
