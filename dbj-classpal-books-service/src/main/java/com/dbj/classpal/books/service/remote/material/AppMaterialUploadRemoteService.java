package com.dbj.classpal.books.service.remote.material;


import com.dbj.classpal.admin.client.api.books.material.AppMaterialUploadApi;
import com.dbj.classpal.admin.client.bo.books.material.MaterialFileConversionApiBO;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class AppMaterialUploadRemoteService {

    @Resource
    private AppMaterialUploadApi appMaterialUploadApi;

    /**
     * 上传文件到素材中心
     * @param bo
     * @return
     * @throws Exception
     */
    public Boolean saveApiImportMaterialFile(MaterialFileConversionApiBO bo) throws Exception {
        RestResponse<Boolean> result = appMaterialUploadApi.saveApiImportMaterialFile(bo);
        return result.returnProcess(result);
    }
}
