package com.dbj.classpal.books.service.openapi.service;

import com.dbj.classpal.books.service.openapi.factory.DynamicApiClientFactory;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.framework.openapi.request.ApiRequest;
import com.dbj.framework.openapi.response.ApiResponse;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.CompletableFuture;

import static com.dbj.classpal.books.common.constant.AppErrorCode.OPENAPI_API_CALL_FAIL_CODE;
import static com.dbj.classpal.books.common.constant.AppErrorCode.OPENAPI_API_CALL_FAIL_MSG;

/**
 * 业务服务抽象基类
 */
@Slf4j
public abstract class BaseApiService {
    
    protected final DynamicApiClientFactory apiClientFactory;
    protected final String serviceName;

    protected BaseApiService(DynamicApiClientFactory apiClientFactory, String serviceName) {
        this.apiClientFactory = apiClientFactory;
        this.serviceName = serviceName;
    }
    
    /**
     * 获取API请求实例
     */
    protected CompletableFuture<ApiRequest> createAuthClient() {
        return apiClientFactory.createAuthClient(serviceName);
    }

    
    /**
     * 处理API响应
     */
    protected <T> T handleResponse(ApiResponse<T> response) throws BusinessException {
        if (response.isSuccess()) {
            return response.getData();
        } else {
            log.error(OPENAPI_API_CALL_FAIL_MSG + ": {}", response.getMessage());
            throw new BusinessException(OPENAPI_API_CALL_FAIL_CODE,OPENAPI_API_CALL_FAIL_MSG);
        }
    }
}