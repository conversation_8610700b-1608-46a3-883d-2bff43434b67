package com.dbj.classpal.books.service.openapi;

import com.dbj.classpal.books.service.openapi.credential.CredentialManager;
import com.dbj.classpal.books.service.openapi.factory.DynamicApiClientFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * 凭证刷新服务
 * 定期刷新API凭证
 */
@Service
public class CredentialRefreshService {
    
    private static final Logger logger = LoggerFactory.getLogger(CredentialRefreshService.class);
    
    private final CredentialManager credentialManager;
    private final DynamicApiClientFactory apiClientFactory;
    
    public CredentialRefreshService(
            CredentialManager credentialManager,
            DynamicApiClientFactory apiClientFactory) {
        this.credentialManager = credentialManager;
        this.apiClientFactory = apiClientFactory;
    }
    
    /**
     * 定期刷新凭证（例如每30天）
     */
//    @Scheduled(cron = "0 0 0 */30 * ?")
//    public void refreshCredential() {
//        logger.info("开始刷新应用凭证...");
//
//        credentialManager.refreshCredential()
//                .thenRun(() -> {
//                    // 清除API请求缓存，强制使用新凭证
//                    apiClientFactory.clearCache();
//                    logger.info("应用凭证刷新成功，API请求缓存已清除");
//                })
//                .exceptionally(ex -> {
//                    // 记录错误但不抛出异常
//                    logger.error("刷新应用凭证失败", ex);
//                    return null;
//                });
//    }
}