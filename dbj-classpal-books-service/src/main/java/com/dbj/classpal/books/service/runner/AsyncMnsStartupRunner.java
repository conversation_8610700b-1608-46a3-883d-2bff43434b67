package com.dbj.classpal.books.service.runner;

import com.dbj.classpal.books.service.util.MnsUtil;
import com.dbj.classpal.framework.oss.config.MnsConfig;
import com.dbj.classpal.framework.oss.enums.MnsEnvEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class AsyncMnsStartupRunner implements CommandLineRunner {

    @Resource
    private MnsUtil mnsUtil;
    @Resource
    private MnsConfig mnsConfig;

    @Override
    @Async  // 异步执行
    public void run(String... args) {
        log.info("执行mts消息");
        if (mnsConfig.getEnv().equals(MnsEnvEnum.ENV_PRO.getDev())){
            mnsUtil.getMnsClient();
        }
    }

}
