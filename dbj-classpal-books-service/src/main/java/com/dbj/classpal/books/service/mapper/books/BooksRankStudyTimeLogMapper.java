package com.dbj.classpal.books.service.mapper.books;

import com.dbj.classpal.books.service.entity.books.BooksRankStudyTimeLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 图书卷册赠册用户学习时长表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
public interface BooksRankStudyTimeLogMapper extends BaseMapper<BooksRankStudyTimeLog> {



    /**
     * <AUTHOR>
     * @Description  统计册书学习使用时常
     * @Date 2025/4/21 16:45
     * @param
     * @return
     **/
    Long getLastStudyTime(@Param("rankId") Integer rankId, @Param("appUserId") Integer appUserId);
}
