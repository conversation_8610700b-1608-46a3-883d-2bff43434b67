package com.dbj.classpal.books.service.service.advertisement.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.service.entity.advertisement.AdvertisementLevelCondition;
import com.dbj.classpal.books.service.mapper.advertisement.AdvertisementLevelConditionMapper;
import com.dbj.classpal.books.service.service.advertisement.IAdvertisementLevelConditionService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 广告条件层级表 业务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Service
public class AdvertisementLevelConditionServiceImpl extends ServiceImpl<AdvertisementLevelConditionMapper, AdvertisementLevelCondition> implements IAdvertisementLevelConditionService {

}
