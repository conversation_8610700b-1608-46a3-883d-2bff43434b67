package com.dbj.classpal.books.service.service.advertisement;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.client.bo.advertisement.AdvertisementResourceBO;
import com.dbj.classpal.books.client.dto.advertisement.AdvertisementResourceDTO;
import com.dbj.classpal.books.service.entity.advertisement.AdvertisementResource;

import java.util.List;

/**
 * <p>
 * 广告页面表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
public interface IAdvertisementResourceService extends IService<AdvertisementResource> {

    /**
     * 获取广告页面列表
     *
     * @param bo
     * @return
     */
    List<AdvertisementResourceDTO> getAdvertisementResourceList(AdvertisementResourceBO bo);
}
