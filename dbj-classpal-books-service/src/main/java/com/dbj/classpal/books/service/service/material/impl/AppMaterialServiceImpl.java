package com.dbj.classpal.books.service.service.material.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.sdk.service.mts20140618.models.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.client.bo.file.importfile.ExcelFileImportQueryApiBO;
import com.dbj.classpal.admin.client.bo.file.importfile.ExcelFileImportSaveApiBO;
import com.dbj.classpal.admin.client.bo.file.importfile.ExcelFileImportUpdateApiBO;
import com.dbj.classpal.admin.client.dto.file.importfile.ExcelFileImportQueryApiDTO;
import com.dbj.classpal.books.client.enums.FileTypeEnum;
import com.dbj.classpal.books.client.enums.MaterialTypeEnum;
import com.dbj.classpal.books.common.bo.common.CommonIdBO;
import com.dbj.classpal.books.common.bo.file.ExcelFileEntity;
import com.dbj.classpal.books.common.bo.material.*;
import com.dbj.classpal.books.common.dto.material.AppMaterialQueryDTO;
import com.dbj.classpal.books.common.dto.material.AppMaterialQueryDicTreeDTO;
import com.dbj.classpal.books.common.dto.material.AppMaterialStatisticsSizeDTO;
import com.dbj.classpal.books.common.dto.mns.MnsMessageBodyDTO;
import com.dbj.classpal.books.common.enums.IsRootEnum;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBiz;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBusinessRefBiz;
import com.dbj.classpal.books.service.entity.material.AppMaterial;
import com.dbj.classpal.books.service.entity.material.AppMaterialBusinessRef;
import com.dbj.classpal.books.service.remote.file.ExcelFileRemoteService;
import com.dbj.classpal.books.service.remote.file.imports.MaterialFileImportRemoteService;
import com.dbj.classpal.books.service.service.material.IAppMaterialService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.mq.bo.MaterialTransParamsBO;
import com.dbj.classpal.framework.mq.pool.DbjRabbitTemplate;
import com.dbj.classpal.framework.oss.config.MnsConfig;
import com.dbj.classpal.framework.oss.config.OssConfig;
import com.dbj.classpal.framework.oss.constant.MtsExchangeConstant;
import com.dbj.classpal.framework.oss.constant.MtsRoutingKeyConstant;
import com.dbj.classpal.framework.oss.enums.MnsEnvEnum;
import com.dbj.classpal.framework.oss.enums.MnsMessageTypeEnum;
import com.dbj.classpal.framework.oss.enums.MtsAnalysisStatesEnum;
import com.dbj.classpal.framework.oss.enums.MtsTransCodeStatesEnum;
import com.dbj.classpal.framework.oss.utils.MtsUtil;
import com.dbj.classpal.framework.utils.enums.FileStatusEnum;
import com.dbj.classpal.framework.utils.util.ContextCommandHolder;
import com.dbj.classpal.framework.utils.util.ContextUtil;
import com.dbj.classpal.framework.utils.util.PinYinUtil;
import com.google.gson.Gson;
import io.micrometer.common.util.StringUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.TaskRejectedException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;
import static com.dbj.classpal.framework.oss.constant.MtsRoutingKeyConstant.MTS_QUERY_ANALYSIS_JOB_ROUTING_KEY;
import static com.dbj.classpal.framework.oss.constant.MtsRoutingKeyConstant.MTS_QUERY_JOB_ROUTING_KEY;
import static com.dbj.classpal.framework.oss.enums.MnsResponseCodeEnum.*;
import static com.dbj.classpal.framework.oss.enums.MtsTransCodeStatesEnum.STATES_TRANSCODE_FAIL;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppMaterialServiceImpl
 * Date:     2025-04-08 16:22:04
 * Description: 表名： ,描述： 表
 */
@Service
@Slf4j
public class AppMaterialServiceImpl implements IAppMaterialService {

    @Autowired
    private IAppMaterialBiz business;
    @Autowired
    private IAppMaterialBusinessRefBiz businessRefBusiness;
    @Autowired
    private ExcelFileRemoteService excelFileRemoteService;
    @Resource
    private MaterialFileImportRemoteService fileImportRemoteService;
    @Resource
    private OssConfig ossConfig;
    @Resource
    private MtsUtil mtsUtil;
    @Resource
    private MnsConfig mnsConfig;
    @Resource
    private DbjRabbitTemplate dbjRabbitTemplate;


    @Override
    public Page<AppMaterialQueryDTO> pageInfo(PageInfo<AppMaterialQueryBO> pageRequest) {
        // 1. 查询分页数据
        Page<AppMaterial> page = business.pageMaterials(pageRequest);
        // 2. 转换为VO
        return (Page<AppMaterialQueryDTO>) page.convert(this::convertToDTO);
    }

    @Override
    public Boolean materialExist(AppMaterialExistQueryBO bo) throws BusinessException {
        ExcelFileImportQueryApiBO excelFileImportQueryApiBO = new ExcelFileImportQueryApiBO();
        excelFileImportQueryApiBO.setMd5(bo.getMaterialMd5());
        Boolean checkFlag = excelFileRemoteService.checkProcessMd5File(excelFileImportQueryApiBO);
        if (checkFlag){
            throw new BusinessException(FILE_SAME_PROCESSING_EXIST_CODE,FILE_SAME_PROCESSING_EXIST_MSG);
        }
        List<AppMaterial> existList = business.lambdaQuery().eq(AppMaterial::getMaterialMd5, bo.getMaterialMd5()).list();
        if (existList.isEmpty()){
            return false;
        }
        String dirPath = bo.getDirPath();
        Integer parentId = bo.getMaterialId();
        //如果文件夹id不为空，执行以下代码
        if (parentId == null) {
            throw new BusinessException(FILE_DIRECTORY_NOT_EXIST_CODE,FILE_DIRECTORY_NOT_EXIST_MSG);
        }
        //查询目标文件夹目录是否存在
        AppMaterial byId = business.getById(parentId);
        //不存在，处理失败
        if (byId == null) {
            throw new BusinessException(FILE_DIRECTORY_NOT_EXIST_CODE,FILE_DIRECTORY_NOT_EXIST_MSG);
        }
        if (StringUtils.isNotEmpty(dirPath)){
            String[] pathArr = dirPath.split("/");
            boolean rootNotExist = false;
            List<String>mkdirList = new ArrayList<>();
            for (int i = 0; i < pathArr.length; i++) {
                List<AppMaterial> list = business.lambdaQuery().orderByDesc(AppMaterial::getOrderNum).eq(AppMaterial::getParentId, parentId).eq(AppMaterial::getMaterialType, MaterialTypeEnum.FILE_TYPE_DIR.getCode()).eq(AppMaterial::getMaterialName, pathArr[i]).list();
                //判断是否存在文件夹，如果存在遍历查询上级文件夹id
                if (CollectionUtils.isNotEmpty(list)){
                    AppMaterial appMaterial = list.get(0);
                    parentId = appMaterial.getId();
                    continue;
                }
                //判断是否第一个文件夹就不存在
                if (i == 0){
                    rootNotExist = true;
                    break;
                }else{
                    mkdirList.add(pathArr[i]);
                }
            }
            if(rootNotExist){
                for (String path : pathArr) {
                    AppMaterial appMaterial = new AppMaterial();
                    appMaterial.setParentId(parentId);
                    appMaterial.setMaterialName(path);
                    appMaterial.setMaterialType(MaterialTypeEnum.FILE_TYPE_DIR.getCode());
                    appMaterial.setOrderNum(this.createSort(parentId));
                    appMaterial.setFirstPinYin(PinYinUtil.getFirstCharFirstLetter(path));
                    appMaterial.setPinYin(PinYinUtil.convertChineseToPinyin(path));
                    business.save(appMaterial);
                    parentId = appMaterial.getId();
                }
            }else{
                for (String path : mkdirList) {
                    AppMaterial appMaterial = new AppMaterial();
                    appMaterial.setParentId(parentId);
                    appMaterial.setMaterialName(path);
                    appMaterial.setMaterialType(MaterialTypeEnum.FILE_TYPE_DIR.getCode());
                    appMaterial.setOrderNum(this.createSort(parentId));
                    appMaterial.setFirstPinYin(PinYinUtil.getFirstCharFirstLetter(path));
                    appMaterial.setPinYin(PinYinUtil.convertChineseToPinyin(path));
                    business.save(appMaterial);
                    parentId = appMaterial.getId();
                }
            }
        }
        LambdaQueryWrapper<AppMaterial> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppMaterial::getParentId,parentId);
        queryWrapper.orderByDesc(AppMaterial::getOrderNum);
        //查询当前文件夹下其他文件（查询最大的排序号）
        List<AppMaterial> broList = business.list(queryWrapper);
        AppMaterial appMaterial = new AppMaterial();
        appMaterial.setParentId(parentId);
        appMaterial.setMaterialName(bo.getMaterialName());
        appMaterial.setPinYin(existList.get(0).getPinYin());
        appMaterial.setFirstPinYin(existList.get(0).getFirstPinYin());
        appMaterial.setMaterialType(existList.get(0).getMaterialType());
        appMaterial.setMaterialExtension(existList.get(0).getMaterialExtension());
        appMaterial.setMaterialPath(existList.get(0).getMaterialPath());
        appMaterial.setMaterialOriginUrl(existList.get(0).getMaterialOriginUrl());
        appMaterial.setMaterialSize(existList.get(0).getMaterialSize());
        appMaterial.setMaterialDuration(existList.get(0).getMaterialDuration());
        appMaterial.setMaterialStatus(existList.get(0).getMaterialStatus());
        appMaterial.setMaterialCaption(existList.get(0).getMaterialCaption());
        appMaterial.setMaterialMd5(bo.getMaterialMd5());
        //给新增的资源赋予排序号
        if (CollectionUtils.isNotEmpty(broList)) {
            AppMaterial orderNum = broList.get(0);
            appMaterial.setOrderNum(orderNum.getOrderNum()+1);
        }
        //新增素材资源数据
        if(business.save(appMaterial)){
            ExcelFileImportSaveApiBO saveApiBO = new ExcelFileImportSaveApiBO();
            saveApiBO.setFileName(bo.getMaterialName());
            saveApiBO.setFileUrl(appMaterial.getMaterialPath());
            saveApiBO.setProcessedUrl(appMaterial.getMaterialPath());
            saveApiBO.setHandleStartTime(LocalDateTime.now());
            saveApiBO.setHandleEndTime(LocalDateTime.now());
            saveApiBO.setStatus(2);
            excelFileRemoteService.saveExcelFileImportById(saveApiBO);
        }else{
            ExcelFileImportSaveApiBO saveApiBO = new ExcelFileImportSaveApiBO();
            saveApiBO.setFileName(bo.getMaterialName());
            saveApiBO.setFileUrl(appMaterial.getMaterialPath());
            saveApiBO.setProcessedUrl(appMaterial.getMaterialPath());
            saveApiBO.setHandleStartTime(LocalDateTime.now());
            saveApiBO.setHandleEndTime(LocalDateTime.now());
            saveApiBO.setStatus(4);
            excelFileRemoteService.saveExcelFileImportById(saveApiBO);
        }
        return true;
    }

    @Override
    public AppMaterial saveMaterial(AppMaterialSaveBO bo) throws BusinessException {
        if (bo.getParentId() == null){
            throw new BusinessException("目录ID不能为空");
        }
        if (StringUtils.isEmpty(bo.getMaterialName())){
            throw new BusinessException("资源名称不能为空");
        }
        if (bo.getMaterialType() == null){
            throw new BusinessException("资源类型不能为空");
        }
        if (StringUtils.isEmpty(bo.getMaterialPath())){
            throw new BusinessException("资源路径不能为空");
        }
        if (StringUtils.isEmpty(bo.getMaterialOriginUrl())){
            throw new BusinessException("原始资源oss路径不能为空");
        }
        if (bo.getMaterialSize() == null){
            throw new BusinessException("资源大小不能为空");
        }
        if (bo.getMaterialDuration() == null){
            throw new BusinessException("资源时长不能为空");
        }
        //查询当前文件夹下其他文件（查询最大的排序号）
        List<AppMaterial> broList = business.lambdaQuery().eq(AppMaterial::getParentId, bo.getParentId()).orderByDesc(AppMaterial::getOrderNum).list();
        AppMaterial appMaterial = new AppMaterial();
        BeanUtil.copyProperties(bo, appMaterial);
        appMaterial.setMaterialType(Objects.requireNonNull(FileTypeEnum.getMaterialType(bo.getMaterialOriginUrl())).getCode());
        //给新增的资源赋予排序号
        if (CollectionUtils.isNotEmpty(broList)) {
            AppMaterial orderNum = broList.get(0);
            appMaterial.setOrderNum(orderNum.getOrderNum() + 1);
        }
        String materialPath = appMaterial.getMaterialPath();
        if (StringUtils.isNotEmpty(materialPath)){
            appMaterial.setMaterialPath(materialPath.replaceAll(ossConfig.getUrlPrefix(),ossConfig.getCdn()));
        }
        appMaterial.setMaterialOriginUrl(appMaterial.getMaterialPath());
        business.save(appMaterial);
        return appMaterial;
    }

    @Override
    public Integer createSort(Integer parentId) {
        LambdaQueryWrapper<AppMaterial> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppMaterial::getParentId,parentId);
        queryWrapper.orderByDesc(AppMaterial::getOrderNum);
        List<AppMaterial> list = business.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(list)){
            return list.get(0).getOrderNum()+1;
        }else{
            return 1;
        }
    }

    @Override
    public Boolean materialMkdir(AppMaterialSaveMkdirBO saveMkdirBO) {
        AppMaterial appMaterial = new AppMaterial();
        BeanUtil.copyProperties(saveMkdirBO, appMaterial);
        appMaterial.setMaterialType(MaterialTypeEnum.FILE_TYPE_DIR.getCode());
        appMaterial.setOrderNum(this.createSort(saveMkdirBO.getParentId()));
        String materialName = saveMkdirBO.getMaterialName();
        appMaterial.setFirstPinYin(PinYinUtil.getFirstCharFirstLetter(materialName));
        appMaterial.setPinYin(PinYinUtil.convertChineseToPinyin(materialName));
        return business.save(appMaterial);
    }

    @Override
    public Boolean moveMaterial(AppMaterialIOBO ioBo) throws BusinessException {
        if (ioBo.getParentId().equals(ioBo.getAimParentId())){
            throw new BusinessException(APP_MATERIAL_MOVE_FAIL_SAME_DIR_CODE,APP_MATERIAL_MOVE_FAIL_SAME_DIR_MSG);
        }
        Integer count = this.checkAimIdInChildren(ioBo);
        if (count>0){
            throw new BusinessException(APP_MATERIAL_MOVE_FAIL_CHILDREN_DIR_CODE,APP_MATERIAL_MOVE_FAIL_CHILDREN_DIR_MSG);
        }
        return business.lambdaUpdate().eq(AppMaterial::getId,ioBo.getId()).eq(AppMaterial::getParentId,ioBo.getParentId()).set(AppMaterial::getParentId,ioBo.getAimParentId()).update();
    }

    @Override
    public Boolean batchMoveMaterial(AppMaterialBatchIOBO ioBo) throws BusinessException {
          List<AppMaterialIOBO> ioApiBOList = ioBo.getIoApiBOList();
          Boolean notSupported = false;
          Boolean sameDir = false;
          for (AppMaterialIOBO appMaterialIOBO : ioApiBOList) {
              if (appMaterialIOBO.getParentId().equals(appMaterialIOBO.getAimParentId())){
                  sameDir = true;
                  break;
              }
              Integer count = this.checkAimIdInChildren(appMaterialIOBO);
              if (count>0){
                  notSupported = true;
                  break;
              }
          }
          if (sameDir){
              throw new BusinessException(APP_MATERIAL_MOVE_FAIL_SAME_DIR_CODE,APP_MATERIAL_MOVE_FAIL_SAME_DIR_MSG);
          }
          if (notSupported){
              throw new BusinessException(APP_MATERIAL_MOVE_FAIL_CHILDREN_DIR_CODE,APP_MATERIAL_MOVE_FAIL_CHILDREN_DIR_MSG);
          }
          Set<Integer> idSet = ioApiBOList.stream().map(AppMaterialIOBO::getId).collect(Collectors.toSet());
          Integer aimId = ioApiBOList.stream().map(AppMaterialIOBO::getAimParentId).collect(Collectors.toList()).get(0);
          return business.lambdaUpdate().in(AppMaterial::getId,idSet).set(AppMaterial::getParentId,aimId).update();

    }

    @Override
    public Boolean copyMaterial(AppMaterialIOBO ioBo) throws BusinessException {
        if (ioBo.getParentId().equals(ioBo.getAimParentId())){
            throw new BusinessException(APP_MATERIAL_COPY_FAIL_SAME_DIR_CODE,APP_MATERIAL_COPY_FAIL_SAME_DIR_MSG);
        }
        AppMaterial byId = business.getById(ioBo.getId());
        if (ObjectUtils.isEmpty(byId)){
            throw new BusinessException(APP_MATERIAL_NOT_EXIST_CODE,APP_MATERIAL_NOT_EXIST_MSG);
        }
        Integer count = this.checkAimIdInChildren(ioBo);
        if (count>0){
            throw new BusinessException(APP_MATERIAL_COPY_FAIL_CHILDREN_DIR_CODE,APP_MATERIAL_COPY_FAIL_CHILDREN_DIR_MSG);
        }
        AppMaterial appMaterial = new AppMaterial();
        BeanUtil.copyProperties(byId, appMaterial);
        appMaterial.setParentId(ioBo.getAimParentId());
        String materialName = appMaterial.getMaterialName();
        appMaterial.setFirstPinYin(PinYinUtil.getFirstCharFirstLetter(materialName));
        appMaterial.setPinYin(PinYinUtil.convertChineseToPinyin(materialName));
        return business.save(appMaterial);
    }

    @Override
    public Boolean batchCopyMaterial(AppMaterialBatchIOBO ioBo) throws BusinessException {
        List<AppMaterialIOBO> ioApiBOList = ioBo.getIoApiBOList();
        Boolean notSupported = false;
        Boolean sameDir = false;
        for (AppMaterialIOBO appMaterialIOBO : ioApiBOList) {
            if (appMaterialIOBO.getParentId().equals(appMaterialIOBO.getAimParentId())){
                sameDir = true;
                break;
            }
            Integer count = this.checkAimIdInChildren(appMaterialIOBO);
            if (count>0){
                notSupported = true;
                break;
            }
        }
        if (sameDir){
            throw new BusinessException(APP_MATERIAL_COPY_FAIL_SAME_DIR_CODE,APP_MATERIAL_COPY_FAIL_SAME_DIR_MSG);
        }
        if (notSupported){
            throw new BusinessException(APP_MATERIAL_COPY_FAIL_CHILDREN_DIR_CODE,APP_MATERIAL_COPY_FAIL_CHILDREN_DIR_MSG);
        }
        Set<Integer> idSet = ioApiBOList.stream().map(AppMaterialIOBO::getId).collect(Collectors.toSet());
        Integer aimId = ioApiBOList.stream().map(AppMaterialIOBO::getAimParentId).collect(Collectors.toList()).get(0);
        List<AppMaterial> list = business.lambdaQuery().in(AppMaterial::getId, idSet).list();
        List<Integer> idList = list.stream().map(AppMaterial::getId).collect(Collectors.toList());
        List<Integer> notExistList = idSet.stream()
                .filter(num -> !idList.contains(num))
                .collect(Collectors.toList());
        //判断是否列表中有不存在的资源
        if (CollectionUtils.isNotEmpty(notExistList)){
            throw new BusinessException(APP_MATERIAL_COLLECT_NOT_EXIST_CODE,APP_MATERIAL_COLLECT_NOT_EXIST_MSG);
        }
        List<AppMaterial> saveBeans = list.stream().map(d -> {
            AppMaterial appMaterial = new AppMaterial();
            BeanUtil.copyProperties(d, appMaterial);
            appMaterial.setId(null);
            appMaterial.setParentId(aimId);
            String materialName = appMaterial.getMaterialName();
            appMaterial.setFirstPinYin(PinYinUtil.getFirstCharFirstLetter(materialName));
            appMaterial.setPinYin(PinYinUtil.convertChineseToPinyin(materialName));
            return appMaterial;
        }).collect(Collectors.toList());
        return business.saveBatch(saveBeans);
    }

    @Override
    public AppMaterialQueryDicTreeDTO getAllDirectsTree() {
        LambdaQueryWrapper<AppMaterial> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppMaterial::getIsRoot, IsRootEnum.IS_ROOT_YES.getCode());
        //1. 查询根节点数据
        AppMaterial one =  business.getOne(queryWrapper);
        if (ObjectUtils.isEmpty(one)){
            return null;
        }
        AppMaterialQueryDicTreeDTO rootBean = new AppMaterialQueryDicTreeDTO();
        BeanUtil.copyProperties(one, rootBean);

        //2.查询所有文件夹列表
        List<AppMaterialQueryDicTreeDTO> dirList = business.lambdaQuery().eq(AppMaterial::getMaterialType, MaterialTypeEnum.FILE_TYPE_DIR.getCode()).eq(AppMaterial::getIsRoot,IsRootEnum.IS_ROOT_NO.getCode()).list().stream().map(d -> {
            AppMaterialQueryDicTreeDTO nodeBean = new AppMaterialQueryDicTreeDTO();
            BeanUtil.copyProperties(d, nodeBean);
            return nodeBean;
        }).collect(Collectors.toList());

        //3. 查询根节点下所有文件夹列表
        List<AppMaterialQueryDicTreeDTO> rootNodes = business.lambdaQuery().eq(AppMaterial::getParentId, rootBean.getId()).eq(AppMaterial::getMaterialType, MaterialTypeEnum.FILE_TYPE_DIR.getCode()).list().stream().map(d -> {
            AppMaterialQueryDicTreeDTO nodeBean = new AppMaterialQueryDicTreeDTO();
            BeanUtil.copyProperties(d, nodeBean);
            return nodeBean;
        }).collect(Collectors.toList());

        // 3. 递归设置子节点
        rootNodes.forEach(root -> buildTree(root, dirList));
        rootBean.setChildren(rootNodes);
        return rootBean;
    }

    @Override
    public List<AppMaterialQueryDTO> getMaterialParentsPath(Integer id) {
        return business.getMaterialParentsPath(id).stream().map(d -> {
            AppMaterialQueryDTO appMaterialQueryDTO = new AppMaterialQueryDTO();
            BeanUtil.copyProperties(d, appMaterialQueryDTO);
            return appMaterialQueryDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public String getMaterialParentsNames(Integer id) {
        return business.getMaterialParentsNames(id);
    }

    @Override
    public Integer checkAimIdInChildren(AppMaterialIOBO ioBo) {
        return business.checkAimIdInChildren(ioBo);
    }

    @Override
    public Boolean reNameMaterial(AppMaterialReNameBO reNameBO) {
        String materialName = reNameBO.getMaterialName();
        String firstName = materialName.substring(0, 1);
        return business.lambdaUpdate()
                .eq(AppMaterial::getId, reNameBO.getId())
                .set(AppMaterial::getMaterialName,reNameBO.getMaterialName())
                .set(AppMaterial::getFirstPinYin,PinYinUtil.getFirstCharFirstLetter(firstName))
                .set(AppMaterial::getPinYin,PinYinUtil.convertChineseToPinyin(materialName)).update();
    }

    @Override
    public Boolean editCaption(AppMaterialEditCaptionBO editCaptionBO) {
        return business.lambdaUpdate().eq(AppMaterial::getId, editCaptionBO.getId()).set(AppMaterial::getMaterialCaption,editCaptionBO.getMaterialCaption()).update();
    }

    @Override
    public Boolean deleteMaterial(CommonIdBO bo) throws BusinessException {
        AppMaterial byId = business.getById(bo.getId());
        if (byId.getMaterialType().equals(MaterialTypeEnum.FILE_TYPE_DIR.getCode())){
            List<AppMaterial> childrenFiles = business.getChildrenFiles(bo.getId());
            Set<Integer> idSet = childrenFiles.stream().map(AppMaterial::getId).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(idSet)){
                return business.removeById(bo.getId());
            }
            int refCount = businessRefBusiness.lambdaQuery().in(AppMaterialBusinessRef::getAppMaterialId, idSet).count().intValue();
            if (refCount>0){
                throw new BusinessException(APP_MATERIAL_DELETE_REF_FAIL_CODE,APP_MATERIAL_DELETE_REF_FAIL_MSG);
            }
            return business.removeByIds(idSet);
        }else{
            int refCount = businessRefBusiness.lambdaQuery().eq(AppMaterialBusinessRef::getAppMaterialId, bo.getId()).count().intValue();
            if (refCount>0){
                throw new BusinessException(APP_MATERIAL_DELETE_REF_FAIL_CODE,APP_MATERIAL_DELETE_REF_FAIL_MSG);
            }
            return business.removeById(bo.getId());
        }
    }

    @Override
    public Boolean batchDeleteMaterial(AppMaterialBatchCommonIdBO bo) throws BusinessException {
        List<AppMaterial> dataList = business.lambdaQuery().in(AppMaterial::getId, bo.getIds()).list();
        List<Integer> dirIdList = dataList.stream().filter(d -> d.getMaterialType().equals(MaterialTypeEnum.FILE_TYPE_DIR.getCode())).map(AppMaterial::getId).collect(Collectors.toList());
        Set<Integer> fileIdSet = dataList.stream().filter(d -> !(d.getMaterialType().equals(MaterialTypeEnum.FILE_TYPE_DIR.getCode()))).map(AppMaterial::getId).collect(Collectors.toSet());
        int fileRefCount = 0;
        if (CollectionUtils.isNotEmpty(fileIdSet)){
            fileRefCount = businessRefBusiness.lambdaQuery().in(AppMaterialBusinessRef::getAppMaterialId, fileIdSet).count().intValue();
        }
        if (fileRefCount>0){
            throw new BusinessException(APP_MATERIAL_DELETE_REF_FAIL_CODE,APP_MATERIAL_DELETE_REF_FAIL_MSG);
        }
        Set<Integer> dirIdSet  = null;
        if (CollectionUtils.isNotEmpty(dirIdList)){
            dirIdSet = business.getBatchDirChildrenFiles(dirIdList).stream().map(AppMaterial::getId).collect(Collectors.toSet());
        }
        int dirFileRefCount = 0;
        if (CollectionUtils.isNotEmpty(dirIdSet)){
            dirFileRefCount = businessRefBusiness.lambdaQuery().in(AppMaterialBusinessRef::getAppMaterialId, dirIdSet).count().intValue();
        }
        if (dirFileRefCount>0){
            throw new BusinessException(APP_MATERIAL_DELETE_REF_FAIL_CODE,APP_MATERIAL_DELETE_REF_FAIL_MSG);
        }
        boolean fileRemove = true;
        if (CollectionUtils.isNotEmpty(fileIdSet)){
            fileRemove = business.removeByIds(fileIdSet);
        }
        boolean dirRemove = true;
        if (CollectionUtils.isNotEmpty(dirIdSet)){
            dirRemove = business.removeByIds(dirIdSet);
        }
        return  fileRemove && dirRemove;
    }

    @Override
    public AppMaterialStatisticsSizeDTO usedSize() {
        double size = 0d;
        List<AppMaterial> list = business.lambdaQuery().list().stream().filter(d -> {
            return d.getMaterialSize() != null && !d.getMaterialSize().equals("");
        }).collect(Collectors.toList());
        for (AppMaterial appMaterial : list) {
            size += appMaterial.getMaterialSize();
        }
        return new AppMaterialStatisticsSizeDTO(size);
    }

    @Override
    public List<AppMaterial> getChildrenFiles(Integer id) {
        return business.getChildrenFiles(id);
    }

    @Override
    public List<AppMaterial> getBatchDirChildrenFiles(List<Integer> ids) {
        return business.getBatchDirChildrenFiles(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void getMaterialFileHandler(ExcelFileEntity excelFileEntity) throws Exception{
        try {
            ExcelFileImportQueryApiBO queryApiBO = new ExcelFileImportQueryApiBO();
            BeanUtil.copyProperties(excelFileEntity, queryApiBO);
            ExcelFileImportQueryApiDTO fileDomain = null;
            fileDomain = fileImportRemoteService.getExcelFileImportById(queryApiBO);
            log.info("查询文件信息{}", JSON.toJSONString(fileDomain));
            if (fileDomain == null || StringUtils.isEmpty(fileDomain.getFileUrl()) || !Objects.equals(fileDomain.getStatus(), FileStatusEnum.PENDING_PROCESSING.getCode())) {
                return;
            }
            String fileUrl = fileDomain.getFileUrl();
            String fileName = fileUrl.replace(ossConfig.getUrlPrefix(),"").replace(ossConfig.getCdn(),"");
            boolean isSupported = FileTypeEnum.isSupportedType(fileName);
            //如果不支持类型,则改变文件上传状态
            if (!isSupported) {
                throw new BusinessException(FILE_TYPE_IMPORT_FAIL_CODE, FILE_TYPE_IMPORT_FAIL_MSG);
            }
            ExcelFileImportUpdateApiBO subFileDomain = new ExcelFileImportUpdateApiBO();
            subFileDomain.setId(fileDomain.getId());
            subFileDomain.setHandleStartTime(LocalDateTime.now());
            subFileDomain.setStatus(FileStatusEnum.PROCESSING.getCode());
            log.info("更改的文件信息:{}", JSONObject.toJSONString(subFileDomain));
            fileImportRemoteService.updateExcelFileImportById(subFileDomain);
            MaterialTypeEnum materialType = FileTypeEnum.getMaterialType(fileName);
            //如果不支持类型,则改变文件上传状态
            if (materialType == null) {
                throw new BusinessException(FILE_TYPE_IMPORT_FAIL_CODE, FILE_TYPE_IMPORT_FAIL_MSG);
            }
            log.info("文件类型:{}", JSONObject.toJSONString(materialType.getDesc()));
            //如果未找到保存的路径信息，处理失败
            String paramJson = fileDomain.getParamJson();
            if (StringUtils.isEmpty(paramJson)) {
                throw new BusinessException(FILE_DIRECTORY_NOT_EXIST_CODE, FILE_DIRECTORY_NOT_EXIST_MSG);
            }
            Gson gson = new Gson();
            //转换成单文件接收对象
            MaterialTransParamsBO dto = gson.fromJson(paramJson, MaterialTransParamsBO.class);
            String extension = fileName.substring(fileName.lastIndexOf(".") + 1);
            //如果文件夹id不为空，执行以下代码
            if (dto.getMaterialId() == null) {
                throw new BusinessException(FILE_DIRECTORY_NOT_EXIST_CODE, FILE_DIRECTORY_NOT_EXIST_MSG);
            }
            Integer parentId = dto.getMaterialId();
            //查询当前文件夹目录是否存在
            AppMaterial byId = business.getById(parentId);
            //不存在，处理失败
            if (byId == null) {
                throw new BusinessException(FILE_DIRECTORY_NOT_EXIST_CODE, FILE_DIRECTORY_NOT_EXIST_MSG);
            }
            String dirPath = dto.getDirPath();
            if (StringUtils.isNotEmpty(dirPath)) {
                String[] pathArr = dirPath.split("/");
                boolean rootNotExist = false;
                List<String> mkdirList = new ArrayList<>();
                for (int i = 0; i < pathArr.length; i++) {
                    List<AppMaterial> list = business.lambdaQuery().orderByDesc(AppMaterial::getOrderNum).eq(AppMaterial::getParentId, parentId).eq(AppMaterial::getMaterialType, MaterialTypeEnum.FILE_TYPE_DIR.getCode()).eq(AppMaterial::getMaterialName, pathArr[i]).list();
                    //判断是否存在文件夹，如果存在遍历查询上级文件夹id
                    if (CollectionUtils.isNotEmpty(list)) {
                        AppMaterial appMaterial = list.get(0);
                        parentId = appMaterial.getId();
                        continue;
                    }
                    //判断是否第一个文件夹就不存在
                    if (i == 0) {
                        rootNotExist = true;
                        break;
                    } else {
                        mkdirList.add(pathArr[i]);
                    }
                }

                if (rootNotExist) {
                    for (String path : pathArr) {
                        AppMaterial appMaterial = new AppMaterial();
                        appMaterial.setParentId(parentId);
                        appMaterial.setMaterialName(path);
                        appMaterial.setMaterialType(MaterialTypeEnum.FILE_TYPE_DIR.getCode());
                        appMaterial.setOrderNum(this.createSort(parentId));
                        appMaterial.setFirstPinYin(PinYinUtil.getFirstCharFirstLetter(path));
                        appMaterial.setPinYin(PinYinUtil.convertChineseToPinyin(path));
                        business.save(appMaterial);
                        parentId = appMaterial.getId();
                    }
                } else {
                    for (String path : mkdirList) {
                        AppMaterial appMaterial = new AppMaterial();
                        appMaterial.setParentId(parentId);
                        appMaterial.setMaterialName(path);
                        appMaterial.setMaterialType(MaterialTypeEnum.FILE_TYPE_DIR.getCode());
                        appMaterial.setOrderNum(this.createSort(parentId));
                        appMaterial.setFirstPinYin(PinYinUtil.getFirstCharFirstLetter(path));
                        appMaterial.setPinYin(PinYinUtil.convertChineseToPinyin(path));
                        business.save(appMaterial);
                        parentId = appMaterial.getId();
                    }
                }
            }
            excelFileEntity.setParentId(parentId);
            dto.setMaterialId(parentId);
            subFileDomain.setParamJson(JSON.toJSONString(dto));
            //如果不为视频或音频需要转码的类型,则直接保存文件
            if ((!(materialType.getCode().equals(MaterialTypeEnum.FILE_TYPE_VIDEO.getCode())) && !(materialType.getCode().equals(MaterialTypeEnum.FILE_TYPE_AUDIO.getCode())))) {
                LambdaQueryWrapper<AppMaterial> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(AppMaterial::getParentId, parentId);
                queryWrapper.orderByDesc(AppMaterial::getOrderNum);
                //查询当前文件夹下其他文件（查询最大的排序号）
                List<AppMaterial> broList = business.list(queryWrapper);
                AppMaterial appMaterial = new AppMaterial();
                appMaterial.setParentId(parentId);
                appMaterial.setMaterialName(dto.getFileName());
                appMaterial.setMaterialType(Objects.requireNonNull(FileTypeEnum.getMaterialType(fileDomain.getFileUrl())).getCode());
                appMaterial.setMaterialExtension(extension);
                appMaterial.setMaterialPath(fileDomain.getFileUrl());
                appMaterial.setMaterialOriginUrl(fileDomain.getFileUrl());
                appMaterial.setFirstPinYin(PinYinUtil.getFirstCharFirstLetter(dto.getFileName()));
                appMaterial.setPinYin(PinYinUtil.convertChineseToPinyin(dto.getFileName()));
                if (dto.getMd5() != null) {
                    appMaterial.setMaterialMd5(dto.getMd5());
                }
                if (dto.getSize() != null) {
                    appMaterial.setMaterialSize(dto.getSize());
                }
                //给新增的资源赋予排序号
                if (CollectionUtils.isNotEmpty(broList)) {
                    AppMaterial orderNum = broList.get(0);
                    appMaterial.setOrderNum(orderNum.getOrderNum() + 1);
                }
                //新增素材资源数据
                business.save(appMaterial);
                //成功后修改上传文件状态等相关信息
                subFileDomain.setStatus(FileStatusEnum.PROCESSED.getCode());
                subFileDomain.setHandleEndTime(LocalDateTime.now());
                subFileDomain.setProcessedUrl(fileDomain.getFileUrl());
                ExcelFileImportUpdateApiBO updateApiBO = new ExcelFileImportUpdateApiBO();
                BeanUtil.copyProperties(subFileDomain, updateApiBO);
                fileImportRemoteService.updateExcelFileImportById(updateApiBO);
                return;
            }
            //如果为音频文件,提交mts音频转码任务
            if (materialType.getCode().equals(MaterialTypeEnum.FILE_TYPE_AUDIO.getCode())) {
                log.info("文件类型:{}", JSONObject.toJSONString(materialType.getDesc()));
                log.info("音频文件提交转码任务:{}", JSONObject.toJSONString(LocalDateTime.now()));
                SubmitJobsResponse response = mtsUtil.translateAudio(fileName).get();
                if (response == null) {
                    throw new BusinessException(FILE_CONVERSION_ERROR_CODE, FILE_CONVERSION_ERROR_MSG);
                }
                if (response.getStatusCode() != 200) {
                    throw new BusinessException(FILE_CONVERSION_ERROR_CODE, FILE_CONVERSION_ERROR_MSG);
                }
                String jobId = response.getBody().getJobResultList().getJobResult().get(0).getJob().getJobId();
                subFileDomain.setTransSubmitJobId(jobId);
                subFileDomain.setHandleEndTime(LocalDateTime.now());
                fileImportRemoteService.updateExcelFileImportById(subFileDomain);
                log.info("音频文件结束提交转码任务:{}", JSONObject.toJSONString(LocalDateTime.now()));
                excelFileEntity.setTransCodeJobId(jobId);
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                    @Override
                    public void afterCommit() {
                        dbjRabbitTemplate.sendExchangeEntityMessage(excelFileEntity, MtsExchangeConstant.MTS_TASK_EXCHANGE, MtsRoutingKeyConstant.MTS_QUERY_JOB_ROUTING_KEY);
                    }
                });
                return;
            }
            //如果为视频文件,因为需要适配系统智能预制模板,所以需要先“分析”，后根据分析提供的推荐模板进行转码
            if (materialType.getCode().equals(MaterialTypeEnum.FILE_TYPE_VIDEO.getCode())) {
                log.info("文件类型:{}", JSONObject.toJSONString(materialType.getDesc()));
                log.info("视频文件开始提交分析转码模板任务:{}", JSONObject.toJSONString(LocalDateTime.now()));
                SubmitAnalysisJobResponse response = mtsUtil.subAnalysisVideoTemplate(fileName).get();
                if (response == null) {
                    throw new BusinessException(FILE_ANALYSIS_ERROR_CODE, FILE_ANALYSIS_ERROR_MSG);
                }
                if (response.getStatusCode() != 200) {
                    throw new BusinessException(response.getBody().getAnalysisJob().getMessage());
                }
                String state = response.getBody().getAnalysisJob().getState();
                if (state.equals(MtsAnalysisStatesEnum.STATES_FAIL.getState())) {
                    throw new BusinessException(response.getBody().getAnalysisJob().getMessage());
                }
                subFileDomain.setAnalysisSubmitJobId(response.getBody().getAnalysisJob().getId());
                subFileDomain.setHandleEndTime(LocalDateTime.now());
                fileImportRemoteService.updateExcelFileImportById(subFileDomain);
                excelFileEntity.setAnalysisJobId(response.getBody().getAnalysisJob().getId());
                excelFileEntity.setFileName(fileName);
                log.info("视频文件结束提交分析转码模板任务:{}", JSONObject.toJSONString(LocalDateTime.now()));
                if (!(mnsConfig.getEnv().equals(MnsEnvEnum.ENV_PRO.getDev()))) {
                    log.info("视频文件发送mq通知查询分析转码模板结果:{}", JSONObject.toJSONString(LocalDateTime.now()));
                    TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                        @Override
                        public void afterCommit() {
                            dbjRabbitTemplate.sendExchangeEntityMessage(excelFileEntity, MtsExchangeConstant.MTS_TASK_EXCHANGE, MtsRoutingKeyConstant.MTS_QUERY_ANALYSIS_JOB_ROUTING_KEY);
                        }
                    });
                }
            }

        }catch (Exception e) {
            errorHandler(excelFileEntity,e);
            throw e;
        }
    }



    public void errorHandler(ExcelFileEntity excelFileEntity,Exception e) throws Exception {
        ExcelFileImportQueryApiBO queryApiBO = new ExcelFileImportQueryApiBO();
        BeanUtil.copyProperties(excelFileEntity, queryApiBO);
        ExcelFileImportQueryApiDTO fileDomain = fileImportRemoteService.getExcelFileImportById(queryApiBO);
        if (ObjectUtil.isNotEmpty(fileDomain)) {
            ExcelFileImportUpdateApiBO subFileDomain = new ExcelFileImportUpdateApiBO();
            subFileDomain.setId(fileDomain.getId());
            subFileDomain.setHandleEndTime(LocalDateTime.now());
            subFileDomain.setStatus(FileStatusEnum.PROCESSING_FAILED_SYS.getCode());
            subFileDomain.setErrorMsg(e.getMessage());
            fileImportRemoteService.updateExcelFileImportById(subFileDomain);
//            if (fileDomain.getBusinessType().equals(FileBusinessTypeEnum.BUSINESS_TTS.getCode())) {
//                AppMaterialMqEntity appMaterialMqEntity = new AppMaterialMqEntity();
//                appMaterialMqEntity.setCode(MaterialTransCodeEnum.TRANS_CODE_ERROR.getCode());
//                appMaterialMqEntity.setMsg(e.getMessage());
//                appMaterialMqEntity.setParamJson(excelFileEntity.getParamJson());
//                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
//                    @Override
//                    public void afterCommit() {
//                        dbjRabbitTemplate.sendExchangeEntityMessage(appMaterialMqEntity, CLASSPAL_BOOKS_EXCHANGE, AUDIO_SYNC_MATERIAL_ROUTING_KEY );
//                    }
//                });
//            }
        }

    }


    @Override
    @Transactional(noRollbackFor = TaskRejectedException.class)
    public void getAnalysisMaterial(ExcelFileEntity excelFileEntity) throws Exception {
        try{
            log.info("开始查询分析模板结果消息:{}", JSONObject.toJSONString(LocalDateTime.now()));
            CompletableFuture<QueryAnalysisJobListResponse> analysisVideoTemplate = mtsUtil.getAnalysisVideoTemplate(excelFileEntity.getAnalysisJobId());
            QueryAnalysisJobListResponse response = analysisVideoTemplate.get();
            ExcelFileImportUpdateApiBO subFileDomain = new ExcelFileImportUpdateApiBO();
            subFileDomain.setId(excelFileEntity.getId());
            String state = response.getBody().getAnalysisJobList().getAnalysisJob().get(0).getState();
            Long percent = response.getBody().getAnalysisJobList().getAnalysisJob().get(0).getPercent();
            log.info("查询的分析结果状态:{}", state);
            log.info("查询的分析结果进度:{}", percent);
            if (state.equals(MtsAnalysisStatesEnum.STATES_FAIL.getState())) {
                throw new BusinessException(FILE_ANALYSIS_ERROR_CODE, FILE_ANALYSIS_ERROR_MSG);
            }
            if (!state.equals(MtsAnalysisStatesEnum.STATES_SUCCESS.getState())) {
                log.info("未完成分析任务重新入队:{}", JSONObject.toJSONString(LocalDateTime.now()));
                throw new TaskRejectedException("未完成分析任务重新入队");
            }
            List<QueryAnalysisJobListResponseBody.Template> templates = response.getBody().getAnalysisJobList().getAnalysisJob().get(0).getTemplateList().getTemplate().stream().filter(d -> d.getName().contains("M3U8")).sorted(Comparator.comparing(QueryAnalysisJobListResponseBody.Template::getId).reversed()).collect(Collectors.toList());
            if (templates.isEmpty()) {
                throw new CompletionException(new IllegalStateException("无可用模板"));
            }
            subFileDomain.setHandleEndTime(LocalDateTime.now());
            subFileDomain.setAnalysisQueryJobId(response.getBody().getAnalysisJobList().getAnalysisJob().get(0).getId());
            fileImportRemoteService.updateExcelFileImportById(subFileDomain);
            excelFileEntity.setTemplateId(templates.get(0).getId());
            log.info("已完成分析,发送本地mq通知转码任务:{}", JSONObject.toJSONString(LocalDateTime.now()));
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    dbjRabbitTemplate.sendExchangeEntityMessage(excelFileEntity, MtsExchangeConstant.MTS_TASK_EXCHANGE, MtsRoutingKeyConstant.MTS_SUBMIT_JOB_ROUTING_KEY);
                }
            });
        }catch (TaskRejectedException e){
            throw e;
        }catch (Exception e){
            errorHandler(excelFileEntity,e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void getTranscodeMaterial(ExcelFileEntity excelFileEntity) throws Exception {
        try{
            log.info("开始视频转码:{}", JSONObject.toJSONString(LocalDateTime.now()));
            CompletableFuture<SubmitJobsResponse> future = mtsUtil.submitTranscodeJob(excelFileEntity.getTemplateId(), excelFileEntity.getFileName());
            SubmitJobsResponse response = future.get();
            if (response == null) {
                throw new BusinessException(FILE_CONVERSION_ERROR_CODE, FILE_CONVERSION_ERROR_MSG);
            }
            if (response.getStatusCode() != 200) {
                throw new BusinessException(FILE_CONVERSION_ERROR_CODE, FILE_CONVERSION_ERROR_MSG);
            }
            Boolean success = response.getBody().getJobResultList().getJobResult().get(0).getSuccess();
            if (!success) {
                throw new BusinessException(FILE_CONVERSION_ERROR_CODE, response.getBody().getJobResultList().getJobResult().get(0).getMessage());
            }
            String jobId = response.getBody().getJobResultList().getJobResult().get(0).getJob().getJobId();
            ExcelFileImportUpdateApiBO subFileDomain = new ExcelFileImportUpdateApiBO();
            subFileDomain.setId(excelFileEntity.getId());
            subFileDomain.setHandleEndTime(LocalDateTime.now());
            subFileDomain.setTransSubmitJobId(jobId);
            fileImportRemoteService.updateExcelFileImportById(subFileDomain);
            excelFileEntity.setTransCodeJobId(jobId);
            if (!(mnsConfig.getEnv().equals(MnsEnvEnum.ENV_PRO.getDev()))) {
                log.info("发送本地mq通知查询视频转码结果消息:{}", JSONObject.toJSONString(LocalDateTime.now()));
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                    @Override
                    public void afterCommit() {
                        dbjRabbitTemplate.sendExchangeEntityMessage(excelFileEntity, MtsExchangeConstant.MTS_TASK_EXCHANGE, MtsRoutingKeyConstant.MTS_QUERY_JOB_ROUTING_KEY);
                    }
                });
            }
        }catch (Exception e){
            errorHandler(excelFileEntity,e);
           throw e;
        }
    }

    @Override
    public void getQueryTransCodeAndSaveMaterial(ExcelFileEntity excelFileEntity) throws Exception {
        try{
            log.info("开始查询视频转码结果:{}", JSONObject.toJSONString(LocalDateTime.now()));
            QueryJobListResponse response = mtsUtil.queryJobListResponse(excelFileEntity.getTransCodeJobId()).get();
            QueryJobListResponseBody.Job job = response.getBody().getJobList().getJob().get(0);
            String jobId = job.getJobId();
            String state = job.getState();
            Long percent = job.getPercent();
            log.info("查询的转码结果状态:{}", state);
            log.info("查询的转码结果进度:{}", percent);
            log.info("查询转码结果:{}", job);
            ExcelFileImportUpdateApiBO subFileDomain = new ExcelFileImportUpdateApiBO();
            subFileDomain.setId(excelFileEntity.getId());
            if (state.equals(STATES_TRANSCODE_FAIL.getState())) {
                throw new BusinessException(FILE_CONVERSION_ERROR_MSG);
            }
            if (!state.equals(MtsTransCodeStatesEnum.STATES_TRANSCODE_SUCCESS.getState())) {
                log.info("未完成转码任务重新入队:{}", JSONObject.toJSONString(LocalDateTime.now()));
                throw new TaskRejectedException("未完成转码任务重新入队");
            }
            subFileDomain.setHandleEndTime(LocalDateTime.now());
            subFileDomain.setTransQueryJobId(job.getJobId());
            fileImportRemoteService.updateExcelFileImportById(subFileDomain);
            log.info("已完成查询转码任务,开始生成素材中心数据:{}", JSONObject.toJSONString(LocalDateTime.now()));
            //转码任务响应内容
            QueryJobListResponseBody.OutputFile outputFile = job.getOutput().getOutputFile();
            //转码后文件全名（含后缀）
            String fileFullName = outputFile.getObject();
            //转码文件oss全路径
            String resultUrl = ossConfig.getCdn() + fileFullName;

            ExcelFileImportQueryApiBO excelFileImportQueryApiBO = new ExcelFileImportQueryApiBO();
            excelFileImportQueryApiBO.setId(excelFileEntity.getId());
            ExcelFileImportQueryApiDTO file = fileImportRemoteService.getExcelFileImportById(excelFileImportQueryApiBO);
            if (ObjectUtil.isNotEmpty(file)) {
                AppMaterial byId = business.getById(excelFileEntity.getParentId());
                //不存在，处理失败并跳过
                if (byId == null) {
                    throw new BusinessException(FILE_DIRECTORY_NOT_EXIST_MSG);
                }
                //上传时保存的文件路径
                String paramJson = file.getParamJson();
                //如果未找到保存的路径信息，处理失败并跳过
                if (StringUtils.isEmpty(paramJson)) {
                    throw new BusinessException(FILE_DIRECTORY_NOT_EXIST_MSG);
                }
                Integer parentId = excelFileEntity.getParentId();
                Gson gson = new Gson();
                //转换成单文件接收对象
                MaterialTransParamsBO dto = gson.fromJson(paramJson, MaterialTransParamsBO.class);
                AppMaterial appMaterial = new AppMaterial();
                appMaterial.setParentId(parentId);
                appMaterial.setMaterialName(dto.getFileName());
                MaterialTypeEnum materialType = FileTypeEnum.getMaterialType(file.getFileUrl());
                if (materialType != null) {
                    appMaterial.setMaterialType(materialType.getCode());
                }
                appMaterial.setMaterialPath(resultUrl);
                appMaterial.setMaterialOriginUrl(file.getFileUrl());
                appMaterial.setMaterialMd5(dto.getMd5());
                //获取计算oss转码后文件大小（kb）
                BigDecimal size = new BigDecimal(job.getOutput().getProperties().getFileSize());
                BigDecimal change = new BigDecimal(1024);
                BigDecimal resultSize = size.divide(change, 2, RoundingMode.HALF_UP);
                double materialSize = resultSize.doubleValue();
                appMaterial.setMaterialSize(materialSize);
                appMaterial.setMaterialDuration(Integer.valueOf(job.getOutput().getProperties().getDuration()));
                appMaterial.setJobId(jobId);
                appMaterial.setFirstPinYin(PinYinUtil.getFirstCharFirstLetter(dto.getFileName()));
                appMaterial.setPinYin(PinYinUtil.convertChineseToPinyin(dto.getFileName()));
                //给新增的资源赋予排序号
                appMaterial.setOrderNum(this.createSort(parentId));
                int saveCount = business.lambdaQuery().eq(AppMaterial::getJobId, jobId).count().intValue();
                if (saveCount <= 0) {
                    //新增素材资源数据
                    if (business.save(appMaterial)) {
                        //成功后修改上传文件状态等相关信息
                        file.setStatus(FileStatusEnum.PROCESSED.getCode());
                        file.setHandleEndTime(LocalDateTime.now());
                        file.setProcessedUrl(resultUrl);

                        ExcelFileImportUpdateApiBO updateApiBO = new ExcelFileImportUpdateApiBO();
                        BeanUtil.copyProperties(file, updateApiBO);
                        fileImportRemoteService.updateExcelFileImportById(updateApiBO);
                        log.info("生成素材中心数据完成:{}", JSONObject.toJSONString(LocalDateTime.now()));
//                        if (file.getBusinessType().equals(FileBusinessTypeEnum.BUSINESS_TTS.getCode())) {
//                            AppMaterialMqEntity appMaterialMqEntity = new AppMaterialMqEntity();
//                            appMaterialMqEntity.setCode(MaterialTransCodeEnum.TRANS_CODE_SUCCESS.getCode());
//                            appMaterialMqEntity.setFinalMaterialId(appMaterial.getId());
//                            appMaterialMqEntity.setParamJson(excelFileEntity.getParamJson());
//                            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
//                                @Override
//                                public void afterCommit() {
//                                    dbjRabbitTemplate.sendExchangeEntityMessage(appMaterialMqEntity, CLASSPAL_BOOKS_EXCHANGE, AUDIO_SYNC_MATERIAL_ROUTING_KEY );
//                                }
//                            });
//                        }
                    }
                }
            }
        }catch (TaskRejectedException e){
            throw e;
        }catch (Exception e){
            errorHandler(excelFileEntity,e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void getProEnvMnsMaterialHandler(MnsMessageBodyDTO response) throws Exception {
        try {
            if (ObjectUtil.isEmpty(response)){
                return;
            }
            String type = response.getType();
            String state = response.getState();
            String jobId = response.getJobId();
            ExcelFileImportQueryApiBO queryApiBO = new ExcelFileImportQueryApiBO();
            if (state.equals(MNS_RESPONSE_CODE_SUCCESS.getCode())) {
                ExcelFileEntity excelFileEntity = new ExcelFileEntity();
                ExcelFileImportQueryApiDTO fileImportQueryApiDTO = new ExcelFileImportQueryApiDTO();
                String routingKey = "";
                if (type.equals(MnsMessageTypeEnum.MESSAGE_TYPE_ANALYSIS.getType())) {
                    log.info("分析模板成功，调用本地查询分析结果mq:{}", JSONObject.toJSONString(LocalDateTime.now()));
                    queryApiBO.setAnalysisSubmitJobId(jobId);
                    fileImportQueryApiDTO = fileImportRemoteService.getByAnalysisJobId(queryApiBO);
                    routingKey = MTS_QUERY_ANALYSIS_JOB_ROUTING_KEY;
                }
                if (type.equals(MnsMessageTypeEnum.MESSAGE_TYPE_TRANSCODE.getType())){
                    log.info("转码成功，调用本地查询转码结果mq:{}", JSONObject.toJSONString(LocalDateTime.now()));
                    queryApiBO.setTransSubmitJobId(jobId);
                    fileImportQueryApiDTO = fileImportRemoteService.getByTransCodeJobId(queryApiBO);
                    routingKey = MTS_QUERY_JOB_ROUTING_KEY;
                }
                if (ObjectUtil.isNotEmpty(fileImportQueryApiDTO)) {
                    ContextCommandHolder.set(ContextUtil.TENANT_ID_HEADER,fileImportQueryApiDTO.getTenantId());
                    String fileUrl = fileImportQueryApiDTO.getFileUrl();
                    String fileName = fileUrl.replace(ossConfig.getUrlPrefix(),"").replace(ossConfig.getCdn(),"");
                    excelFileEntity.setId(fileImportQueryApiDTO.getId());
                    excelFileEntity.setFileName(fileName);
                    //转换成单文件接收对象
                    Gson gson = new Gson();
                    String paramJson = fileImportQueryApiDTO.getParamJson();
                    if (org.apache.commons.lang3.StringUtils.isNotEmpty(paramJson)){
                        MaterialTransParamsBO dto = gson.fromJson(paramJson, MaterialTransParamsBO.class);
                        if (type.equals(MnsMessageTypeEnum.MESSAGE_TYPE_ANALYSIS.getType())){
                            excelFileEntity.setAnalysisJobId(jobId);
                        }else{
                            excelFileEntity.setTransCodeJobId(jobId);
                            excelFileEntity.setParentId(dto.getMaterialId());
                        }
                        dbjRabbitTemplate.sendExchangeEntityMessage(excelFileEntity, MtsExchangeConstant.MTS_TASK_EXCHANGE, routingKey);
                    }
                }
            }else if (state.equals(MNS_RESPONSE_CODE_FAIL.getCode()) || state.equals(MNS_RESPONSE_CODE_CANCEL.getCode())){
                ExcelFileImportQueryApiDTO fileImportQueryApiDTO = new ExcelFileImportQueryApiDTO();
                if (type.equals(MnsMessageTypeEnum.MESSAGE_TYPE_ANALYSIS.getType())) {
                    log.info("分析模板失败或取消:{}", JSONObject.toJSONString(LocalDateTime.now()));
                    queryApiBO.setAnalysisSubmitJobId(jobId);
                    fileImportQueryApiDTO = fileImportRemoteService.getByAnalysisJobId(queryApiBO);
                }
                if (type.equals(MnsMessageTypeEnum.MESSAGE_TYPE_TRANSCODE.getType())){
                    log.info("转码任务失败或取消:{}", JSONObject.toJSONString(LocalDateTime.now()));
                    queryApiBO.setTransSubmitJobId(jobId);
                    fileImportQueryApiDTO = fileImportRemoteService.getByTransCodeJobId(queryApiBO);
                }
                if (ObjectUtil.isNotEmpty(fileImportQueryApiDTO)) {
                    ContextCommandHolder.set(ContextUtil.TENANT_ID_HEADER, fileImportQueryApiDTO.getTenantId());
                    ExcelFileImportUpdateApiBO subFileDomain = new ExcelFileImportUpdateApiBO();
                    BeanUtil.copyProperties(fileImportQueryApiDTO, subFileDomain);
                    subFileDomain.setHandleEndTime(LocalDateTime.now());
                    subFileDomain.setStatus(FileStatusEnum.PROCESSING_FAILED_SYS.getCode());
                    String desc = getByCode(state).getDesc();
                    subFileDomain.setErrorMsg(desc);
                    fileImportRemoteService.updateExcelFileImportById(subFileDomain);
                    String paramJson = fileImportQueryApiDTO.getParamJson();
//                    if (fileImportQueryApiDTO.getBusinessType().equals(FileBusinessTypeEnum.BUSINESS_TTS.getCode()) && org.apache.commons.lang3.StringUtils.isNotEmpty(paramJson)) {
//                        AppMaterialMqEntity appMaterialMqEntity = new AppMaterialMqEntity();
//                        appMaterialMqEntity.setCode(MaterialTransCodeEnum.TRANS_CODE_ERROR.getCode());
//                        appMaterialMqEntity.setMsg(desc);
//                        appMaterialMqEntity.setParamJson(paramJson);
//                        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
//                            @Override
//                            public void afterCommit() {
//                                dbjRabbitTemplate.sendExchangeEntityMessage(appMaterialMqEntity, CLASSPAL_BOOKS_EXCHANGE, AUDIO_SYNC_MATERIAL_ROUTING_KEY );
//                            }
//                        });
//                    }
                }
            }
        }catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    private AppMaterialQueryDTO convertToDTO(AppMaterial material) {
        AppMaterialQueryDTO vo = new AppMaterialQueryDTO();
        BeanUtil.copyProperties(material, vo);
        return vo;
    }


    private void buildTree(AppMaterialQueryDicTreeDTO parentNode, List<AppMaterialQueryDicTreeDTO> allNodes) {
        List<AppMaterialQueryDicTreeDTO> children = allNodes.stream()
                .filter(node -> node.getParentId().equals(parentNode.getId()))
                .collect(Collectors.toList());

        if (!children.isEmpty()) {
            parentNode.setChildren(children);
            children.forEach(child -> buildTree(child, allNodes));
        }
    }
}
