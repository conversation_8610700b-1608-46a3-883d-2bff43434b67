package com.dbj.classpal.books.service.mapper.ebooks;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookshelfH5QueryBO;
import com.dbj.classpal.books.service.entity.product.AppEBookshelf;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 书架表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Mapper
public interface AppEBookshelfMapper extends BaseMapper<AppEBookshelf> {

    /**
     * 分页查询书架列表
     *
     * @param page 分页对象
     * @param shelfName 书架名称
     * @param isHide 是否隐藏
     * @param launchStatus 上下架状态
     * @return 分页结果
     */
    Page<AppEBookshelf> pageBookshelf(Page<AppEBookshelf> page,
                                       @Param("shelfName") String shelfName,
                                       @Param("isHide") Integer isHide,
                                       @Param("launchStatus") Integer launchStatus);

    /**
     * H5分页查询书架列表（支持书城关联查询）
     *
     * @param page 分页参数
     * @param query 查询条件
     * @return 分页结果
     */
    Page<AppEBookshelf> pageForH5(@Param("page") Page<AppEBookshelf> page, @Param("query") AppEBookshelfH5QueryBO query);
}