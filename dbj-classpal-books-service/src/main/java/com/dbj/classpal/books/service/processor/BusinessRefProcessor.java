package com.dbj.classpal.books.service.processor;

import com.dbj.classpal.books.client.enums.BusinessTypeEnum;
import com.dbj.classpal.books.common.dto.question.QuestionCategoryRefDTO;
import com.dbj.classpal.books.service.entity.question.QuestionCategoryBusinessRef;

/**
 * 业务引用处理器接口
 * 用于处理不同业务类型的引用信息
 */
public interface BusinessRefProcessor {

    /**
     * 获取支持的业务类型
     * 
     * @return 业务类型枚举
     */
    BusinessTypeEnum getSupportedBusinessType();

    /**
     * 处理业务引用信息
     * 
     * @param dto 要填充的DTO对象
     * @param businessRef 业务引用实体
     * @throws Exception 处理异常
     */
    void processBusinessInfo(QuestionCategoryRefDTO dto, QuestionCategoryBusinessRef businessRef) throws Exception;

    /**
     * 获取处理器优先级，数值越小优先级越高
     * 
     * @return 优先级
     */
    default int getPriority() {
        return 100;
    }
}
