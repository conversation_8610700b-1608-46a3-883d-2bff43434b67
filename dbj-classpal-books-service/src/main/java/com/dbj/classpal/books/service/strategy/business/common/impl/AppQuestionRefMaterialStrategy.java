package com.dbj.classpal.books.service.strategy.business.common.impl;

import com.dbj.classpal.books.service.biz.books.IBooksInfoBiz;
import com.dbj.classpal.books.service.biz.books.IBooksRankInCodesContentsBiz;
import com.dbj.classpal.books.service.biz.question.QuestionBiz;
import com.dbj.classpal.books.service.biz.question.QuestionCategoryBiz;
import com.dbj.classpal.books.service.entity.books.BooksInfo;
import com.dbj.classpal.books.service.entity.books.BooksRankInCodesContents;
import com.dbj.classpal.books.service.entity.question.Question;
import com.dbj.classpal.books.service.entity.question.QuestionCategory;
import com.dbj.classpal.books.service.strategy.business.handler.ICommonBusinessStrategyHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppAlbumRefMaterialStrategy
 * Date:     2025-05-09 13:07:37
 * Description: 表名： ,描述： 表
 */
@Service("appQuestionRefMaterialStrategy")
public class AppQuestionRefMaterialStrategy extends ICommonBusinessStrategyHandler {

    @Resource
    private QuestionCategoryBiz questionCategoryBiz;
    @Resource
    private QuestionBiz questionBiz;


    @Override
    public Map<Integer, String> getBusinessName(List<Integer> businessId) {
        Map<Integer, String> map = new HashMap<>();
        List<Question> questions = questionBiz.listByIds(businessId);
        if(CollectionUtils.isNotEmpty(questions)){
            Set<Integer> categoryIds = questions.stream().map(d -> d.getQuestionCategoryId()).collect(Collectors.toSet());
            Map<Integer, QuestionCategory> questionCategoryMap =  questionCategoryBiz.listByIds(categoryIds).stream()
                    // 过滤空对象或空键（避免NullPointerException）
                    .filter(obj -> obj != null && obj.getId() != null)
                    // 转换为Map：key=id, value=对象本身
                    .collect(Collectors.toMap(
                            QuestionCategory::getId,       // 键提取函数
                            Function.identity(),  // 值提取函数（对象自身）
                            (oldVal, newVal) -> newVal // 键冲突时保留新值
               ));
            for (Question question : questions) {
                if (questionCategoryMap.containsKey(question.getQuestionCategoryId())) {
                    map.put(question.getId(),questionCategoryMap.get(question.getQuestionCategoryId()).getName());
                }
            }
        }
        return map;
    }
}
