package com.dbj.classpal.books.service.processor.media;

import com.dbj.classpal.books.common.bo.pointreading.PointReadingHotspotMediaBO;
import com.dbj.classpal.books.common.enums.MediaSourceEnum;
import com.dbj.classpal.books.common.dto.pointreading.PointReadingHotspotMediaDTO;

import java.util.List;

/**
 * 媒体引用处理器接口
 * 用于处理不同媒体来源的引用信息
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
public interface MediaRefProcessor {

    /**
     * 获取支持的媒体来源
     * 
     * @return 媒体来源枚举
     */
    MediaSourceEnum getSupportedMediaSource();

    /**
     * 保存媒体引用
     * 
     * @param hotspotId 热点ID
     * @param mediaList 媒体列表
     * @throws Exception 处理异常
     */
    void saveMediaRefs(Integer hotspotId, List<PointReadingHotspotMediaBO> mediaList) throws Exception;

    /**
     * 查询媒体引用（实时数据）
     * 
     * @param hotspotId 热点ID
     * @return 媒体列表
     * @throws Exception 处理异常
     */
    List<PointReadingHotspotMediaDTO> getMediaRefs(Integer hotspotId) throws Exception;

    /**
     * 删除媒体引用
     * 
     * @param hotspotId 热点ID
     * @throws Exception 处理异常
     */
    void deleteMediaRefs(Integer hotspotId) throws Exception;

    /**
     * 批量删除媒体引用
     * 
     * @param hotspotIds 热点ID列表
     * @throws Exception 处理异常
     */
    void batchDeleteMediaRefs(List<Integer> hotspotIds) throws Exception;

    /**
     * 获取处理器优先级，数值越小优先级越高
     * 
     * @return 优先级
     */
    default int getPriority() {
        return 100;
    }
}
