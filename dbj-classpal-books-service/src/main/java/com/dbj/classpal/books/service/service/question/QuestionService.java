package com.dbj.classpal.books.service.service.question;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.common.bo.question.*;
import com.dbj.classpal.books.common.dto.question.QuestionBlankContentDTO;
import com.dbj.classpal.books.common.dto.question.QuestionInfoDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;

import java.util.List;

public interface QuestionService {
    /**
     * 获取题目详情
     */
    QuestionInfoDTO getQuestion(QuestionIdBO idBO) throws BusinessException;

    /**
     * 根据分类ID获取题目列表
     */
    List<QuestionInfoDTO> getQuestionList(QuestionCategoryIdQueryBO idBO);

    /**
     * 创建题目
     */
    Integer createQuestion(QuestionBO question) throws BusinessException;

    /**
     * 更新题目
     */
    void updateQuestion(QuestionBO question) throws BusinessException;

    /**
     * 删除题目
     */
    void batchDeleteQuestion(QuestionIdsBO idsBO) throws BusinessException;

    /**
     * 获取题目正确答案
     */
    List<String> getQuestionAnswer(QuestionIdBO idBO);

    /**
     * 获取完形填空题内容
     */
    QuestionBlankContentDTO getQuestionBlankContent(QuestionIdBO idBO) throws BusinessException;

    /**
     * 批量复制题目
     *
     * @param copyBO 复制参数
     */
    void batchCopyQuestion(QuestionCopyBO copyBO) throws BusinessException;

    /**
     * 批量移动题目
     *
     * @param moveBO 移动参数
     */
    void batchMoveQuestion(QuestionMoveBO moveBO);

    /**
     * 分页查询题目列表
     */
    Page<QuestionInfoDTO> pageList(PageInfo<QuestionPageBO> pageBO);
} 