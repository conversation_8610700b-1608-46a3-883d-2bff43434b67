package com.dbj.classpal.books.service.strategy.business.common.impl;

import com.dbj.classpal.books.service.biz.studycenter.IAppStudyModuleBiz;
import com.dbj.classpal.books.service.entity.studycenter.AppStudyModule;
import com.dbj.classpal.books.service.strategy.business.handler.ICommonBusinessStrategyHandler;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

/**
 *
 * 学习中心模块引用策略实现类
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Component
public class StudyModelAudioStrategy extends ICommonBusinessStrategyHandler {

    @Resource
    private IAppStudyModuleBiz studyModuleBiz;

    @Override
    public Map<Integer, String> getBusinessName(List<Integer> businessId) {
        List<AppStudyModule> pinyinList = studyModuleBiz.listByIds(businessId);
        return pinyinList.stream().collect(Collectors.toMap(AppStudyModule::getId, AppStudyModule::getTitle));
    }
}
