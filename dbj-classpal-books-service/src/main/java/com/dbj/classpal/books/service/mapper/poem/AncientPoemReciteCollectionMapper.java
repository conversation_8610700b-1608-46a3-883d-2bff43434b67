package com.dbj.classpal.books.service.mapper.poem;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionBO;
import com.dbj.classpal.books.client.bo.poem.app.AppAncientPoemReciteCollectionPageBO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemReciteCollectionDTO;
import com.dbj.classpal.books.client.dto.poem.app.AppAncientPoemReciteCollectionPageDTO;
import com.dbj.classpal.books.service.entity.poem.AncientPoemReciteCollection;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dbj.classpal.framework.commons.request.PageInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 古诗背诵合集表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
public interface AncientPoemReciteCollectionMapper extends BaseMapper<AncientPoemReciteCollection> {


    List<AncientPoemReciteCollectionDTO> listAncientPoemReciteCollection(@Param("bo") AncientPoemReciteCollectionBO ancientPoemReciteCollectionBO);

    Page<AppAncientPoemReciteCollectionPageDTO> pageAncientPoemReciteCollection(Page page,@Param("bo") AppAncientPoemReciteCollectionPageBO ancientPoemReciteCollectionBO);

}
