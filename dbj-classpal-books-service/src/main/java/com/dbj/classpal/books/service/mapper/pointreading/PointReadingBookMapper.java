package com.dbj.classpal.books.service.mapper.pointreading;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingBookQueryBO;
import com.dbj.classpal.books.service.entity.pointreading.PointReadingBook;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 点读书表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Mapper
public interface PointReadingBookMapper extends BaseMapper<PointReadingBook> {

    /**
     * 分页查询点读书
     *
     * @param page 分页参数
     * @param query 查询条件
     * @return 分页结果
     */
    Page<PointReadingBook> selectPage(@Param("page") Page<PointReadingBook> page, @Param("query") PointReadingBookQueryBO query);

    /**
     * 根据MD5查询点读书
     *
     * @param md5 文件MD5
     * @return 点读书信息
     */
    PointReadingBook selectByMd5(@Param("md5") String md5);

    /**
     * 统计分类下的点读书数量
     *
     * @param categoryId 分类ID
     * @return 点读书数量
     */
    int countByCategory(@Param("categoryId") Integer categoryId);
}
