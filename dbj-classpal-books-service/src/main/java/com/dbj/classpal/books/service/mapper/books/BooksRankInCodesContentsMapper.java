package com.dbj.classpal.books.service.mapper.books;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsPageBO;
import com.dbj.classpal.books.client.dto.books.BooksRankInCodesContentsPageDTO;
import com.dbj.classpal.books.client.dto.books.app.BooksRankInCodesContentsCountDTO;
import com.dbj.classpal.books.client.dto.books.app.BooksRankInCodesContentsRankCountDTO;
import com.dbj.classpal.books.service.entity.books.BooksRankInCodesContents;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 图书书内码分类表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
public interface BooksRankInCodesContentsMapper extends BaseMapper<BooksRankInCodesContents> {


    Page<BooksRankInCodesContentsPageDTO> page(Page pageInfo,@Param("bo") BooksRankInCodesContentsPageBO bo);


    /**
     * 获取图书书内码数量
     * @param bookIds
     * @return
     */
    List<BooksRankInCodesContentsCountDTO> listCount(@Param("list") List<Integer> bookIds);
    /**
     * rankIds
     * @param rankIds
     * @return
     */
    List<BooksRankInCodesContentsRankCountDTO> listRankCount(@Param("list") List<Integer> rankIds);

}
