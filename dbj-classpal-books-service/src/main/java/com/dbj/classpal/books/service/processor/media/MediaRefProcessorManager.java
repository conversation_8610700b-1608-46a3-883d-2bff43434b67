package com.dbj.classpal.books.service.processor.media;

import com.dbj.classpal.books.common.bo.pointreading.PointReadingHotspotMediaBO;
import com.dbj.classpal.books.common.enums.MediaSourceEnum;
import com.dbj.classpal.books.common.dto.pointreading.PointReadingHotspotMediaDTO;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 媒体引用处理器管理器
 * 负责管理和调度不同的媒体引用处理器
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@Component
public class MediaRefProcessorManager {

    @Resource
    private List<MediaRefProcessor> processors;

    private final Map<MediaSourceEnum, MediaRefProcessor> processorMap = new HashMap<>();

    @PostConstruct
    public void init() {
        // 按优先级排序处理器
        processors.sort(Comparator.comparingInt(MediaRefProcessor::getPriority));
        
        for (MediaRefProcessor processor : processors) {
            MediaSourceEnum supportedSource = processor.getSupportedMediaSource();
            if (supportedSource != null) {
                processorMap.put(supportedSource, processor);
                log.info("注册媒体引用处理器: {} -> {}", 
                    supportedSource.getDescription(), processor.getClass().getSimpleName());
            }
        }
        
        log.info("媒体引用处理器管理器初始化完成，共注册 {} 个处理器", processors.size());
    }

    /**
     * 保存热点媒体引用
     * 
     * @param hotspotId 热点ID
     * @param mediaList 媒体列表
     * @throws Exception 处理异常
     */
    public void saveHotspotMediaRefs(Integer hotspotId, List<PointReadingHotspotMediaBO> mediaList) throws Exception {
        if (mediaList == null || mediaList.isEmpty()) {
            return;
        }

        // 按媒体来源分组
        Map<MediaSourceEnum, List<PointReadingHotspotMediaBO>> groupedMedia = mediaList.stream()
                .collect(Collectors.groupingBy(media -> MediaSourceEnum.getByCode(media.getMediaSource())));

        // 分别处理不同来源的媒体
        for (Map.Entry<MediaSourceEnum, List<PointReadingHotspotMediaBO>> entry : groupedMedia.entrySet()) {
            MediaSourceEnum source = entry.getKey();
            List<PointReadingHotspotMediaBO> sourceMediaList = entry.getValue();

            MediaRefProcessor processor = processorMap.get(source);
            if (processor != null) {
                try {
                    processor.saveMediaRefs(hotspotId, sourceMediaList);
                } catch (Exception e) {
                    log.error("保存媒体引用失败, processor: {}, mediaSource: {}, hotspotId: {}", 
                        processor.getClass().getSimpleName(), source.getDescription(), hotspotId, e);
                    throw e;
                }
            } else {
                log.warn("未找到媒体来源处理器: {}", source.getDescription());
            }
        }
    }

    /**
     * 查询热点媒体引用（实时数据）
     * 
     * @param hotspotId 热点ID
     * @return 媒体列表
     * @throws Exception 处理异常
     */
    public List<PointReadingHotspotMediaDTO> getHotspotMediaRefs(Integer hotspotId) throws Exception {
        List<PointReadingHotspotMediaDTO> allMedia = new ArrayList<>();

        for (MediaRefProcessor processor : processorMap.values()) {
            try {
                List<PointReadingHotspotMediaDTO> mediaList = processor.getMediaRefs(hotspotId);
                if (mediaList != null && !mediaList.isEmpty()) {
                    allMedia.addAll(mediaList);
                }
            } catch (Exception e) {
                log.error("查询媒体引用失败, processor: {}, hotspotId: {}", 
                    processor.getClass().getSimpleName(), hotspotId, e);
                // 单个处理器失败不影响其他处理器
            }
        }

        // 按排序号排序
        allMedia.sort(Comparator.comparing(PointReadingHotspotMediaDTO::getSortNum, 
            Comparator.nullsLast(Comparator.naturalOrder())));

        return allMedia;
    }

    /**
     * 删除热点媒体引用
     * 
     * @param hotspotId 热点ID
     * @throws Exception 处理异常
     */
    public void deleteHotspotMediaRefs(Integer hotspotId) throws Exception {
        for (MediaRefProcessor processor : processorMap.values()) {
            try {
                processor.deleteMediaRefs(hotspotId);
            } catch (Exception e) {
                log.error("删除媒体引用失败, processor: {}, hotspotId: {}", 
                    processor.getClass().getSimpleName(), hotspotId, e);
                // 单个处理器失败不影响其他处理器
            }
        }
    }

    /**
     * 批量删除热点媒体引用
     * 
     * @param hotspotIds 热点ID列表
     * @throws Exception 处理异常
     */
    public void batchDeleteHotspotMediaRefs(List<Integer> hotspotIds) throws Exception {
        if (hotspotIds == null || hotspotIds.isEmpty()) {
            return;
        }

        for (MediaRefProcessor processor : processorMap.values()) {
            try {
                processor.batchDeleteMediaRefs(hotspotIds);
            } catch (Exception e) {
                log.error("批量删除媒体引用失败, processor: {}, hotspotIds: {}", 
                    processor.getClass().getSimpleName(), hotspotIds, e);
                // 单个处理器失败不影响其他处理器
            }
        }
    }

    /**
     * 获取支持的媒体来源列表
     * 
     * @return 支持的媒体来源列表
     */
    public List<MediaSourceEnum> getSupportedMediaSources() {
        return new ArrayList<>(processorMap.keySet());
    }

    /**
     * 检查是否支持指定的媒体来源
     * 
     * @param mediaSource 媒体来源
     * @return 是否支持
     */
    public boolean isSupported(MediaSourceEnum mediaSource) {
        return processorMap.containsKey(mediaSource);
    }
}
