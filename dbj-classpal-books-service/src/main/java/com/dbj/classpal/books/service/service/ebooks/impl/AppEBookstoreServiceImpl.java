package com.dbj.classpal.books.service.service.ebooks.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookstoreH5QueryBO;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookstoreQueryBO;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookstoreSaveBO;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookstoreUpdateBO;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookstoreDTO;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookstoreH5DTO;
import com.dbj.classpal.books.service.biz.ebooks.IAppEBookshelfBiz;
import com.dbj.classpal.books.service.biz.ebooks.IAppEBookstoreBiz;
import com.dbj.classpal.books.service.biz.ebooks.IAppEBookstoreShelfRefBiz;
import com.dbj.classpal.books.service.entity.product.AppEBookshelf;
import com.dbj.classpal.books.service.entity.product.AppEBookstore;
import com.dbj.classpal.books.service.mapper.ebooks.AppEBookstoreMapper;
import com.dbj.classpal.books.service.service.ebooks.IAppEBookstoreService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.books.common.constant.AppErrorCode;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 书城 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-14
 */
@Slf4j
@Service
public class AppEBookstoreServiceImpl extends ServiceImpl<AppEBookstoreMapper, AppEBookstore> implements IAppEBookstoreService {

    @Resource
    private IAppEBookstoreBiz eBookstoreBiz;

    @Resource
    private IAppEBookstoreShelfRefBiz eBookstoreShelfRefBiz;

    @Resource
    private IAppEBookshelfBiz eBookshelfBiz;

    @Override
    public Page<AppEBookstoreDTO> page(PageInfo<AppEBookstoreQueryBO> pageRequest) throws BusinessException {
        if (pageRequest == null) {
            throw new BusinessException(AppErrorCode.PARAMS_NOT_NULL_CODE, AppErrorCode.PARAMS_NOT_NULL_MSG);
        }
        
        Page<AppEBookstore> page = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        return eBookstoreBiz.pageBookstore(page, pageRequest.getData());
    }

    @Override
    public AppEBookstoreDTO detail(Integer id) throws BusinessException {
        return eBookstoreBiz.getBookstoreDetail(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer save(AppEBookstoreSaveBO saveBO) throws BusinessException {
        if (saveBO == null) {
            throw new BusinessException(AppErrorCode.PARAMS_NOT_NULL_CODE, AppErrorCode.PARAMS_NOT_NULL_MSG);
        }
        
        // 构建实体对象
        AppEBookstore store = new AppEBookstore();
        BeanUtils.copyProperties(saveBO, store);

        // 保存书城信息
        boolean saveResult = eBookstoreBiz.save(store);
        if (!saveResult) {
            throw new BusinessException(AppErrorCode.BOOKSTORE_NOT_EXIST_CODE,AppErrorCode.BOOKSTORE_NOT_EXIST_MSG);
        }
        
        // 保存书城-书架关联关系
        if (CollectionUtils.isNotEmpty(saveBO.getShelfIds())) {
            eBookstoreShelfRefBiz.saveBatch(store.getId(), saveBO.getShelfIds());
        }
        
        return store.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(AppEBookstoreUpdateBO updateBO) throws BusinessException {
        if (updateBO == null || updateBO.getId() == null) {
            throw new BusinessException(AppErrorCode.PARAMS_NOT_NULL_CODE, AppErrorCode.PARAMS_NOT_NULL_MSG);
        }
        
        // 检查书城是否存在
        AppEBookstore store = eBookstoreBiz.getById(updateBO.getId());
        if (store == null) {
            throw new BusinessException(AppErrorCode.BOOKSTORE_NOT_EXIST_CODE, AppErrorCode.BOOKSTORE_NOT_EXIST_MSG);
        }
        
        // 更新实体对象
        BeanUtils.copyProperties(updateBO, store);
        
        // 更新书城信息
        boolean updateResult = eBookstoreBiz.updateById(store);
        if (!updateResult) {
            throw new BusinessException(AppErrorCode.BOOKSTORE_NOT_EXIST_CODE, AppErrorCode.BOOKSTORE_NOT_EXIST_CODE);
        }
        
        // 更新书城-书架关联关系
        if (CollectionUtils.isNotEmpty(updateBO.getShelfIds())) {
            // 删除原有关联关系
            eBookstoreShelfRefBiz.removeBatch(List.of(store.getId()), null);
            
            // 添加新的关联关系
            eBookstoreShelfRefBiz.saveBatch(store.getId(), updateBO.getShelfIds());
        }
        
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(Integer id) throws BusinessException {
        if (id == null) {
            throw new BusinessException(AppErrorCode.BOOKSTORE_ID_NOT_NULL_CODE, AppErrorCode.BOOKSTORE_ID_NOT_NULL_MSG);
        }

        AppEBookstore bookstore = eBookstoreBiz.getBaseMapper().selectById(id);
        if(bookstore.getLaunchStatus().equals(YesOrNoEnum.YES.getCode())){
            throw new BusinessException(AppErrorCode.EBOOK_ENABLED_DELETE_CODE, AppErrorCode.EBOOK_ENABLED_DELETE_MSG);
        }
        
        // 删除书城-书架关联关系
        eBookstoreShelfRefBiz.removeBatch(List.of(id), null);
        
        // 删除书城信息
        return eBookstoreBiz.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBatch(List<Integer> ids) throws BusinessException {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException(AppErrorCode.PARAMS_NOT_NULL_CODE, AppErrorCode.PARAMS_NOT_NULL_MSG);
        }

        List<AppEBookstore> bookstoreList = eBookstoreBiz.getBaseMapper().selectByIds(ids);
        if (CollectionUtils.isEmpty(bookstoreList)) {
            throw new BusinessException(AppErrorCode.BOOKSTORE_NOT_EXIST_CODE, AppErrorCode.BOOKSTORE_NOT_EXIST_MSG);
        }
        if(bookstoreList.stream().anyMatch(e -> e.getLaunchStatus().equals(YesOrNoEnum.YES.getCode()))){
            throw new BusinessException(AppErrorCode.EBOOK_ENABLED_DELETE_CODE, AppErrorCode.EBOOK_ENABLED_DELETE_MSG);
        }
        eBookstoreShelfRefBiz.removeBatch(ids, null);
        eBookstoreBiz.removeBatchByIds(ids);
        return true;
    }

    @Override
    public boolean enableBatch(List<Integer> ids) throws BusinessException {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException(AppErrorCode.PARAMS_NOT_NULL_CODE, AppErrorCode.PARAMS_NOT_NULL_MSG);
        }
        
        List<AppEBookstore> stores = eBookstoreBiz.listByIds(ids);
        if (CollectionUtils.isEmpty(stores)) {
            return true;
        }
        List<Integer> storesIds = new ArrayList<>();
        stores.forEach(bookstore -> {
            bookstore.setLaunchStatus(YesOrNoEnum.YES.getCode());
            storesIds.add(bookstore.getId());
        });
        List<Integer> shelfIds = eBookstoreShelfRefBiz.listShelfIds(storesIds);
        List<AppEBookshelf> eBookshelfList = eBookshelfBiz.getBaseMapper().selectByIds(shelfIds);
        if(CollectionUtils.isNotEmpty(eBookshelfList) && eBookshelfList.stream().anyMatch(e -> e.getLaunchStatus().equals(YesOrNoEnum.NO.getCode()))){
            throw new BusinessException(AppErrorCode.BOOKSHELF_NOT_EXIST_CODE, AppErrorCode.BOOKSTORE_NOT_EXIST_MSG);
        }
        return eBookstoreBiz.updateBatchById(stores);
    }

    @Override
    public boolean disableBatch(List<Integer> ids) throws BusinessException {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException(AppErrorCode.PARAMS_NOT_NULL_CODE, AppErrorCode.PARAMS_NOT_NULL_MSG);
        }
        
        List<AppEBookstore> stores = eBookstoreBiz.listByIds(ids);
        if (CollectionUtils.isEmpty(stores)) {
            return true;
        }
        
        for (AppEBookstore store : stores) {
            store.setLaunchStatus(YesOrNoEnum.NO.getCode());
        }
        
        return eBookstoreBiz.updateBatchById(stores);
    }

    @Override
    public boolean setCover(Integer id, Integer shelfId, String picUrl) throws BusinessException {
        return eBookstoreBiz.setBookstoreCover(id, shelfId, picUrl);
    }

    @Override
    public boolean addShelves(Integer storeId, List<Integer> shelfIds) throws BusinessException {
        return eBookstoreBiz.addShelvesToStore(storeId, shelfIds);
    }

    @Override
    public boolean removeShelves(Integer storeId, List<Integer> shelfIds) throws BusinessException {
        return eBookstoreBiz.removeShelvesFromStore(storeId, shelfIds);
    }

    @Override
    public boolean sortShelves(Integer storeId, List<Integer> shelfIds, Map<Integer, Integer> shelfSortMap) throws BusinessException {
        shelfIds.forEach(id-> shelfSortMap.put(id,shelfIds.indexOf(id)));
        return eBookstoreBiz.sortShelvesInStore(storeId, shelfSortMap);
    }

    
    @Override
    public AppEBookstoreDTO getStatistics(Integer id) throws BusinessException {
        return eBookstoreBiz.getBookstoreStatistics(id);
    }

    @Override
    public boolean allowDownloadBatch(List<Integer> ids) throws BusinessException {
        return eBookstoreBiz.allowDownloadBatch(ids);
    }

    @Override
    public boolean disableDownloadBatch(List<Integer> ids) throws BusinessException {
        return eBookstoreBiz.disableDownloadBatch(ids);
    }

    @Override
    public Page<AppEBookstoreH5DTO> pageBookstoreCollections(PageInfo<AppEBookstoreH5QueryBO> pageRequest) throws BusinessException {
        log.info("分页查询书城集，参数：{}", pageRequest);

        AppEBookstoreH5QueryBO queryData = pageRequest.getData();

        // 创建分页对象
        Page<AppEBookstore> page = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());

        // 创建查询条件
        AppEBookstoreQueryBO queryBO = new AppEBookstoreQueryBO();

        // 判断查询模式
        if (queryData != null && queryData.getStoreId() != null) {
            // 模式2：查询特定分享的书城（不限制启用状态，因为分享的书城可能已下架但仍需访问）
            log.info("查询特定分享书城，书城ID：{}", queryData.getStoreId());

            // 只查询书城基本信息，不查询完整详情（避免查询所有书籍）
            AppEBookstore store = eBookstoreBiz.getById(queryData.getStoreId());
            if (store == null) {
                // 返回空结果
                Page<AppEBookstoreH5DTO> emptyPage = new Page<>(page.getCurrent(), page.getSize(), 0);
                emptyPage.setRecords(new ArrayList<>());
                return emptyPage;
            }

            // 转换为DTO（只包含基本信息）
            AppEBookstoreDTO storeDTO = new AppEBookstoreDTO();
            BeanUtil.copyProperties(store, storeDTO);

            // 构建单个书城的结果
            Page<AppEBookstoreDTO> resultPage = new Page<>(page.getCurrent(), page.getSize(), 1);
            resultPage.setRecords(List.of(storeDTO));
            return buildH5PageResult(resultPage);
        } else {
            // 模式1：查询所有已分享过的书城集
            queryBO.setLaunchStatus(YesOrNoEnum.YES.getCode());

            // 调用业务层查询已分享的书城集（在数据库层面过滤）
            Page<AppEBookstoreDTO> resultPage = eBookstoreBiz.pageBookstore(page, queryBO);

            return buildH5PageResult(resultPage);
        }
    }

    /**
     * 构建H5页面结果（超级优化版本 - 批量查询所有关联数据）
     * H5分页查询只需要书城和书架的统计信息，不需要具体的书籍列表
     * 优化策略：
     * 1. 批量查询所有书城的书架关联关系（避免N+1查询）
     * 2. 批量查询所有书架信息（避免N+1查询）
     * 3. 批量查询所有书架的书籍数量（避免N+1查询）
     */
    private Page<AppEBookstoreH5DTO> buildH5PageResult(Page<AppEBookstoreDTO> resultPage) throws BusinessException {
        log.debug("构建H5页面结果，书城数量：{}", resultPage.getRecords().size());

        // 转换为书城集格式的分页结果
        Page<AppEBookstoreH5DTO> h5Page = new Page<>(resultPage.getCurrent(), resultPage.getSize(), resultPage.getTotal());
        List<AppEBookstoreH5DTO> h5Records = new ArrayList<>();

        if (CollectionUtils.isEmpty(resultPage.getRecords())) {
            h5Page.setRecords(h5Records);
            return h5Page;
        }

        // 批量获取所有书城的书架关联关系（一次查询解决N+1问题）
        List<Integer> storeIds = resultPage.getRecords().stream()
                .map(AppEBookstoreDTO::getId)
                .collect(Collectors.toList());

        // 批量查询所有书城的书架ID映射
        Map<Integer, List<Integer>> storeShelfIdsMap = eBookstoreShelfRefBiz.batchListShelfIds(storeIds);

        // 收集所有书架ID，用于批量查询书架信息和书籍数量
        Set<Integer> allShelfIds = storeShelfIdsMap.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toSet());

        // 批量查询所有书架信息
        Map<Integer, AppEBookshelf> shelfMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(allShelfIds)) {
            List<AppEBookshelf> allShelves = eBookshelfBiz.getBaseMapper().selectByIds(new ArrayList<>(allShelfIds));
            shelfMap = allShelves.stream()
                    .filter(shelf -> YesOrNoEnum.YES.getCode().equals(shelf.getLaunchStatus()))
                    .collect(Collectors.toMap(AppEBookshelf::getId, shelf -> shelf));
        }

        // 批量查询所有书架的书籍数量
        Map<Integer, Integer> allShelfBookCounts = new HashMap<>();
        if (!shelfMap.isEmpty()) {
            allShelfBookCounts = eBookshelfBiz.batchCountBooks(new ArrayList<>(shelfMap.keySet()));
        }

        // 构建每个书城的H5数据
        for (AppEBookstoreDTO storeDTO : resultPage.getRecords()) {
            AppEBookstoreH5DTO h5DTO = new AppEBookstoreH5DTO();
            h5DTO.setStoreId(storeDTO.getId());
            h5DTO.setStoreTitle(storeDTO.getStoreTitle());
            h5DTO.setCoverUrl(storeDTO.getCoverUrl());
            h5DTO.setCoverUrlName(storeDTO.getCoverUrlName());
            h5DTO.setBlurb(storeDTO.getBlurb());
            h5DTO.setShareUrl(storeDTO.getShareUrl());
            h5DTO.setShareQrCode(storeDTO.getShareQrCode());

            // 获取当前书城的书架ID列表
            List<Integer> currentStoreShelfIds = storeShelfIdsMap.getOrDefault(storeDTO.getId(), new ArrayList<>());

            // 过滤出启用的书架并构建统计信息
            List<AppEBookstoreH5DTO.ShelfH5DTO> shelfStatistics = new ArrayList<>();
            int totalBookCount = 0;

            for (Integer shelfId : currentStoreShelfIds) {
                AppEBookshelf shelf = shelfMap.get(shelfId);
                if (shelf != null) {
                    Integer bookCount = allShelfBookCounts.getOrDefault(shelfId, 0);
                    totalBookCount += bookCount;

                    // 创建书架统计对象（不包含具体书籍列表）
                    AppEBookstoreH5DTO.ShelfH5DTO shelfDTO = new AppEBookstoreH5DTO.ShelfH5DTO();
                    BeanUtil.copyProperties(shelf, shelfDTO);
                    shelfDTO.setBookCount(bookCount);
                    shelfStatistics.add(shelfDTO);
                }
            }

            // 设置总计数据
            h5DTO.setTotalShelfCount(shelfStatistics.size());
            h5DTO.setTotalBookCount(totalBookCount);
            h5DTO.setShelfStatistics(shelfStatistics);

            h5Records.add(h5DTO);
        }

        h5Page.setRecords(h5Records);
        log.debug("H5页面结果构建完成，返回书城数量：{}", h5Records.size());
        return h5Page;
    }
}