package com.dbj.classpal.books.service.service.ebooks.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigCategoryQueryApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigCategorySaveApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigCategoryUpdateApiBO;
import com.dbj.classpal.books.client.dto.ebooks.AppEbooksConfigCategoryQueryApiDTO;
import com.dbj.classpal.books.service.biz.ebooks.IAppEBookBiz;
import com.dbj.classpal.books.service.biz.ebooks.IAppEbooksConfigCategoryBiz;
import com.dbj.classpal.books.service.config.ConfigCacheManager;
import com.dbj.classpal.books.service.entity.ebooks.AppEbooksConfigCategory;
import com.dbj.classpal.books.service.entity.product.AppEBook;
import com.dbj.classpal.books.service.service.ebooks.IAppEbooksConfigCategoryService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;

@Service
@Slf4j
public class AppEbooksConfigCategoryServiceImpl implements IAppEbooksConfigCategoryService {

    @Resource
    private IAppEbooksConfigCategoryBiz appEbooksConfigCategoryBiz;
    @Resource
    private IAppEBookBiz eBookBiz;
    @Resource
    private ConfigCacheManager configCacheManager;

    @Override
    public List<AppEbooksConfigCategoryQueryApiDTO> getAllCategory(AppEbooksConfigCategoryQueryApiBO bo) throws BusinessException {
        return queryDatabaseAndBuildResult(bo);
    }


    /**
     * 查询数据库并构建结果
     */
    private List<AppEbooksConfigCategoryQueryApiDTO> queryDatabaseAndBuildResult(AppEbooksConfigCategoryQueryApiBO bo) {
        LambdaQueryChainWrapper<AppEbooksConfigCategory> categoryLambdaQueryChainWrapper = appEbooksConfigCategoryBiz.lambdaQuery();
        if (StringUtils.isNotBlank(bo.getName())) {
            categoryLambdaQueryChainWrapper.like(AppEbooksConfigCategory::getName, bo.getName());
        }
        List<AppEbooksConfigCategory> categoryList = categoryLambdaQueryChainWrapper.orderByDesc(AppEbooksConfigCategory::getSort).list();
        List<AppEbooksConfigCategoryQueryApiDTO> categoryDtoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(categoryList)) {
            categoryDtoList = BeanUtil.copyToList(categoryList, AppEbooksConfigCategoryQueryApiDTO.class);
        }
        for (int i = 0; i < categoryDtoList.size(); i++) {
            AppEbooksConfigCategoryQueryApiDTO dto = categoryDtoList.get(i);
            List<AppEBook> modules = eBookBiz.lambdaQuery()
                    .apply("EXISTS (SELECT 1 FROM dual WHERE FIND_IN_SET({0}, category_ids))", dto.getId())
                    .list();
            if (CollectionUtils.isNotEmpty(modules)) {
                dto.setRefCount(modules.size());
            } else {
                dto.setRefCount(0);
            }
        }
        // 增量更新缓存
        categoryDtoList = categoryDtoList.stream().sorted(Comparator.comparing(AppEbooksConfigCategoryQueryApiDTO::getSort, Comparator.nullsLast(Comparator.reverseOrder()))).toList();
        configCacheManager.updateCategoriesToCache(categoryList);
        return categoryDtoList;
    }

    @Override
    public Boolean saveCategory(AppEbooksConfigCategorySaveApiBO bo) throws BusinessException {
        int existCount = appEbooksConfigCategoryBiz.lambdaQuery().eq(AppEbooksConfigCategory::getParentId,bo.getParentId()).eq(AppEbooksConfigCategory::getName, bo.getName()).count().intValue();
        if (existCount > 0){
            throw new BusinessException(APP_EBOOKS_CONFIG_CATEGORY_SAME_NAME_FAILED_CODE,APP_EBOOKS_CONFIG_CATEGORY_SAME_NAME_FAILED_MSG);
        }
        AppEbooksConfigCategory appEbooksConfigCategory = new AppEbooksConfigCategory();
        BeanUtil.copyProperties(bo,appEbooksConfigCategory);
        Boolean result = appEbooksConfigCategoryBiz.save(appEbooksConfigCategory);
        // 清除统一配置缓存
        configCacheManager.clearCache();
        return result;
    }

    @Override
    public Boolean updateCategory(AppEbooksConfigCategoryUpdateApiBO bo) throws BusinessException {
        int existCount  = appEbooksConfigCategoryBiz.lambdaQuery().ne(AppEbooksConfigCategory::getId,bo.getId()).eq(AppEbooksConfigCategory::getParentId,bo.getParentId()).eq(AppEbooksConfigCategory::getName, bo.getName()).count().intValue();
        if (existCount > 0){
            throw new BusinessException(APP_EBOOKS_CONFIG_CATEGORY_SAME_NAME_FAILED_CODE,APP_EBOOKS_CONFIG_CATEGORY_SAME_NAME_FAILED_MSG);
        }
        Boolean result = appEbooksConfigCategoryBiz.lambdaUpdate()
                .eq(AppEbooksConfigCategory::getId, bo.getId())
                .set(AppEbooksConfigCategory::getParentId,bo.getParentId())
                .set(AppEbooksConfigCategory::getName,bo.getName())
                .set(AppEbooksConfigCategory::getSort,bo.getSort())
                .update();
        // 清除统一配置缓存
        configCacheManager.clearCache();
        return result;
    }

    @Override
    public Boolean deleteCategory(CommonIdsApiBO bo) throws BusinessException {
        int existRefCount = 0;
        int childCategoryNum = appEbooksConfigCategoryBiz.lambdaQuery().in(AppEbooksConfigCategory::getParentId, bo.getIds()).count().intValue();
        if (childCategoryNum > 0){
            throw new BusinessException(APP_EBOOKS_CONFIG_CATEGORY_EXIST_CHILD_FAILED_CODE,APP_EBOOKS_CONFIG_CATEGORY_EXIST_CHILD_FAILED_MSG);
        }
        for (int i = 0; i < bo.getIds().size(); i++) {
            List<AppEBook> modules = eBookBiz.lambdaQuery()
                    .apply("EXISTS (SELECT 1 FROM dual WHERE FIND_IN_SET({0}, category_ids))", bo.getIds().get(i))
                    .list();
            if (CollectionUtils.isNotEmpty(modules)) {
                existRefCount = modules.size();
                break;
            }
        }
        if (existRefCount > 0){
            throw new BusinessException(APP_EBOOKS_CONFIG_CATEGORY_EXIST_REF_FAILED_CODE,APP_EBOOKS_CONFIG_CATEGORY_EXIST_REF_FAILED_MSG);
        }
        Boolean result = appEbooksConfigCategoryBiz.removeByIds(bo.getIds());
        // 清除统一配置缓存
        configCacheManager.clearCache();
        return result;
    }
}
