package com.dbj.classpal.books.service.strategy.business.handler;

import com.dbj.classpal.books.common.dto.common.BusinessInfoDTO;
import com.dbj.classpal.books.service.strategy.business.common.ICommonBusinessStrategy;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppMaterialBusinessStrategy
 * Date:     2025-05-09 13:04:54
 * Description: 表名： ,描述： 表
 */
@Service
public abstract class ICommonBusinessStrategyHandler implements ICommonBusinessStrategy {

    public Map<Integer, BusinessInfoDTO> getBusinessInfoMap(List<Integer> businessIds) {return Collections.emptyMap();}
}
