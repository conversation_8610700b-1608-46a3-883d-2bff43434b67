package com.dbj.classpal.books.service.service.paper.impl;

import cn.hutool.core.bean.BeanUtil;
import com.dbj.classpal.books.common.bo.paper.*;
import com.dbj.classpal.books.common.bo.question.QueryQuestionsCorrectAnswerBO;
import com.dbj.classpal.books.common.dto.paper.PaperQuestionDTO;
import com.dbj.classpal.books.common.dto.paper.PaperQuestionResultDTO;
import com.dbj.classpal.books.common.dto.paper.UserPaperCheckSubmitDTO;
import com.dbj.classpal.books.common.dto.paper.UserPaperDTO;
import com.dbj.classpal.books.common.dto.question.QuestionCorrectAnswerDTO;
import com.dbj.classpal.books.service.entity.question.Question;
import com.dbj.classpal.books.service.factory.QuestionBusinessStrategyFactory;
import com.dbj.classpal.books.service.service.paper.IAppUserPaperService;
import com.dbj.classpal.books.service.strategy.question.IPaperBusinessStrategy;
import com.dbj.classpal.books.service.strategy.question.IQuestionBusinessStrategy;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.redisson.config.RedissonRedisUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;

/**
 * 用户试卷服务实现类
 */
@Slf4j
@Service
public class AppUserPaperServiceImpl implements IAppUserPaperService {

    @Resource
    private QuestionBusinessStrategyFactory strategyFactory;
    @Resource
    private RedissonRedisUtils redissonRedisUtils;

    @Override
    public List<PaperQuestionDTO> getPaperQuestions(QueryPaperQuestionsBO queryBO) throws BusinessException {
        validateParam(queryBO);
        IPaperBusinessStrategy paperStrategy = strategyFactory.getPaperStrategy(queryBO.getBusinessType());
        IQuestionBusinessStrategy questionStrategy = strategyFactory.getQuestionStrategy(queryBO.getBusinessType());
        paperStrategy.validateQueryParams(queryBO);
        List<PaperQuestionDTO> questions = questionStrategy.getPaperQuestions(queryBO);
        if (CollectionUtils.isNotEmpty(questions)) {
            String redisKey = "paper_question_order:" + queryBO.getAppUserId() + ":" + queryBO.getBusinessId() + ":" + queryBO.getBusinessType();
            List<Integer> questionIds = questions.stream()
                    .map(PaperQuestionDTO::getId)
                    .toList();
            // 将 List<Integer> 转换为逗号分隔的字符串
            String orders = questionIds.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
            redissonRedisUtils.setValue(redisKey, orders,30, TimeUnit.DAYS);
        }
        return questions;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserPaperDTO submitPaper(SubmitPaperBO submitBO) throws BusinessException {
        validateParam(submitBO);
        IPaperBusinessStrategy paperStrategy = strategyFactory.getPaperStrategy(submitBO.getBusinessType());
        IQuestionBusinessStrategy questionStrategy = strategyFactory.getQuestionStrategy(submitBO.getBusinessType());
        
        try {
            Map<Integer, Question> questionMap = questionStrategy.loadQuestions(submitBO);
            questionStrategy.scoreQuestionResults(submitBO.getQuestionResults(), questionMap);
            questionStrategy.scoreBlankResults(submitBO.getBlankResults(), questionMap.keySet());
            UserPaperDTO paperInfo = paperStrategy.submitPaper(submitBO);
            questionStrategy.saveQuestionResult(submitBO, paperInfo.getId());
            questionStrategy.saveBlankResult(submitBO, paperInfo.getId());
            questionStrategy.saveEvaluation(submitBO,paperInfo.getId());
            questionStrategy.saveWrongQuestion(submitBO,paperInfo.getId());
            return BeanUtil.copyProperties(paperInfo, UserPaperDTO.class);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("提交试卷失败", e);
            throw new BusinessException(APP_SUBMIT_PARER_FAIL_CODE,APP_SUBMIT_PARER_FAIL_MSG);
        }
    }
    
    @Override
    public PaperQuestionResultDTO getPaperQuestionResult(QueryPaperResultBO queryBO) throws BusinessException {
        IQuestionBusinessStrategy questionStrategy = strategyFactory.getQuestionStrategy(queryBO.getBusinessType());
        return questionStrategy.getPaperQuestionResult(queryBO);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void retakePaper(RetakePaperBO retakePaperBO) throws BusinessException {
        try {
            validateParam(retakePaperBO);
            IPaperBusinessStrategy paperStrategy = strategyFactory.getPaperStrategy(retakePaperBO.getBusinessType());
            QueryPaperBO queryBO = new QueryPaperBO();
            queryBO.setPaperId(retakePaperBO.getPaperId());
            UserPaperDTO paperInfo = paperStrategy.getPaperInfo(queryBO.getPaperId());
            if (paperInfo == null) {
                throw new BusinessException(APP_PAPER_NOT_EXIST_CODE,APP_PAPER_NOT_EXIST_MSG);
            }
            if (!retakePaperBO.getAppUserId().equals(paperInfo.getAppUserId())) {
                throw new BusinessException(APP_PAPER_NO_PERMISSION_CODE,APP_PAPER_NO_PERMISSION_MSG);
            }
            IPaperBusinessStrategy strategy = strategyFactory.getPaperStrategy(paperInfo.getBusinessType());
            strategy.retakePaper(retakePaperBO);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("重新考试失败", e);
            throw new BusinessException(APP_RE_EXAM_FAIL_CODE,APP_RE_EXAM_FAIL_MSG);
        }
    }

    @Override
    public List<QuestionCorrectAnswerDTO> getAllQuestionsCorrectAnswers(QueryQuestionsCorrectAnswerBO bo) throws BusinessException {
        try {
            IQuestionBusinessStrategy questionStrategy = strategyFactory.getQuestionStrategy(bo.getBusinessType());
            return questionStrategy.getQuestionsCorrectAnswers(bo.getBusinessId(), bo.getBusinessType(), bo.getQuestionIds());
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取题目正确答案失败", e);
            throw new BusinessException(APP_GET_QUESTION_FAIL_CODE, APP_GET_QUESTION_FAIL_MSG);
        }
    }

    @Override
    public UserPaperCheckSubmitDTO checkSubmit(CheckSubmitBO serviceBO) throws BusinessException {
        validateParam(serviceBO);
        IPaperBusinessStrategy paperStrategy = strategyFactory.getPaperStrategy(serviceBO.getBusinessType());
        return paperStrategy.checkSubmit(serviceBO);
    }

    /**
     * 验证参数
     */
    private void validateParam(Object param) throws BusinessException {
        if (param == null) {
            throw new BusinessException(APP_PARAM_NOT_NULL_CODE, APP_PARAM_NOT_NULL_MSG);
        }
    }
} 