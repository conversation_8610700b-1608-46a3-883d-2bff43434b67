package com.dbj.classpal.books.service.service.pinyin;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinClassifyBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinClassifyUpsertBO;
import com.dbj.classpal.books.client.dto.pinyin.PinyinClassifyDTO;
import com.dbj.classpal.books.service.entity.pinyin.PinyinClassify;
import com.dbj.classpal.framework.commons.exception.BusinessException;

import java.util.List;

/**
 * <p>
 * 拼音分类表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
public interface IPinyinClassifyService extends IService<PinyinClassify> {

    /**
     * 获取所有拼音分类
     *
     * @param bo
     * @return
     */
    List<PinyinClassifyDTO> getPinyinClassifyAll(PinyinClassifyBO bo);
    /**
     * 保存拼音分类
     *
     * @param bo
     * @return
     */
    Boolean savePinyinClassify(PinyinClassifyUpsertBO bo) throws BusinessException;
    /**
     * 更新拼音分类
     *
     * @param bo
     * @return
     */
    Boolean updatePinyinClassify(PinyinClassifyUpsertBO bo) throws BusinessException;
    /**
     * 删除拼音分类
     *
     * @param bo
     * @return
     */
    Boolean deletePinyinClassify(CommonIdApiBO bo);
    /**
     * 排序拼音分类
     *
     * @param bo
     * @return
     */
    Boolean sortPinyinClassify(CommonIdsApiBO bo);

    /**
     * 获取应用拼音分类
     *
     * @param bo
     * @return
     */
    List<PinyinClassifyDTO> getAppPinyinClassify(PinyinClassifyBO bo);
}
