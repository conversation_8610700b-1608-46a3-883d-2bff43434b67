package com.dbj.classpal.books.service.mapper.evaluation;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.common.bo.evaluation.AdminEvaluationQueryBO;
import com.dbj.classpal.books.service.entity.evaluation.AppEvaluation;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppEvaluationMapper
 * Date:     2025-05-16 14:57:09
 * Description: 表名： ,描述： 表
 */
public interface AppEvaluationMapper extends BaseMapper<AppEvaluation> {

    /**
     * 分页查询评测模板列表
     * @param page
     * @param bo
     * @return
     */
    Page<AppEvaluation> pageInfo(Page page, @Param("bo") AdminEvaluationQueryBO bo);
}
