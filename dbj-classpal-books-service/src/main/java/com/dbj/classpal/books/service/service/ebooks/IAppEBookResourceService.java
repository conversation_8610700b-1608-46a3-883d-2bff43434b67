package com.dbj.classpal.books.service.service.ebooks;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookResourceBatchQueryBO;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookResourceQueryBO;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookResourceSaveBO;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookResourceDTO;
import com.dbj.classpal.books.service.entity.product.AppEBookResource;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;

import java.util.List;

/**
 * <p>
 * 单书资源 服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-10
 */
public interface IAppEBookResourceService extends IService<AppEBookResource> {

    /**
     * 分页查询单书资源
     *
     * @param pageRequest 分页查询参数
     * @return 分页结果
     * @throws BusinessException 业务异常
     */
    Page<AppEBookResourceDTO> page(PageInfo<AppEBookResourceQueryBO> pageRequest) throws BusinessException;

    /**
     * 保存单书资源
     *
     * @param saveBO 保存参数
     * @return 资源ID
     * @throws BusinessException 业务异常
     */
    Integer save(AppEBookResourceSaveBO saveBO) throws BusinessException;

    /**
     * 批量保存单书资源
     *
     * @param saveBOList 保存参数列表
     * @return 是否成功
     * @throws BusinessException 业务异常
     */
    boolean saveBatch(List<AppEBookResourceSaveBO> saveBOList) throws BusinessException;

    /**
     * 删除单书资源
     *
     * @param id 资源ID
     * @return 是否成功
     * @throws BusinessException 业务异常
     */
    boolean delete(Integer id) throws BusinessException;

    /**
     * 根据单书ID删除所有资源
     *
     * @param bookId 单书ID
     * @return 是否成功
     * @throws BusinessException 业务异常
     */
    boolean deleteByBookId(Integer bookId) throws BusinessException;

    /**
     * 分页查询单书图片资源
     *
     * @param pageRequest 查询参数
     * @return 图片资源分页数据
     * @throws BusinessException 业务异常
     */
    Page<AppEBookResourceDTO> pageImages(PageInfo<AppEBookResourceBatchQueryBO> pageRequest) throws BusinessException;
} 