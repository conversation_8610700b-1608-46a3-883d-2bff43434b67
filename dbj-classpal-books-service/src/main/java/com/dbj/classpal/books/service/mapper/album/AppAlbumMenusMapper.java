package com.dbj.classpal.books.service.mapper.album;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dbj.classpal.books.common.bo.album.AppAlbumMenusQueryBO;
import com.dbj.classpal.books.common.dto.album.AppAlbumMenusInfoDTO;
import com.dbj.classpal.books.common.dto.album.AppAlbumMenusTreeDTO;
import com.dbj.classpal.books.service.entity.album.AppAlbumMenus;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppAlbumMenusMapper
 * Date:     2025-04-08 16:20:15
 * Description: 表名： ,描述： 表
 */
public interface AppAlbumMenusMapper extends BaseMapper<AppAlbumMenus> {

    /**
     * 根据部门id统计部门信息
     *
     * @return SysUserDeptGroupDTO
     */
    List<AppAlbumMenusInfoDTO> getAppAlbumMenusInfoByDeptIdGroup(@Param("bo")AppAlbumMenusQueryBO bo);


    /**
     * 获取某个节点下子孙节点深度层数
     * @param id
     * @return
     */
    Integer getChildrenDepth(@Param("id")Integer id);


    /**
     * 获取某个节点到根节点深度层数
     * @param id
     * @return
     */
    Integer getRootDepth(@Param("id")Integer id);

    /**
     * 获取最大排序号
     * @param id
     * @return
     */
    Integer getMaxOrderNum(@Param("id") Integer id);
}
