package com.dbj.classpal.books.service.service.album.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.dto.album.AppAlbumElementsQueryApiDTO;
import com.dbj.classpal.books.client.enums.BusinessTypeEnum;
import com.dbj.classpal.books.client.enums.StatusEnum;
import com.dbj.classpal.books.common.bo.album.AppAlbumElementsQueryBO;
import com.dbj.classpal.books.common.bo.album.AppAlbumElementsSaveBO;
import com.dbj.classpal.books.common.bo.album.AppAlbumElementsUpdateBO;
import com.dbj.classpal.books.common.bo.album.AppAlbumElementsUpdateStatusBO;
import com.dbj.classpal.books.common.bo.album.AppAlbumElementsUpdateVisibleBO;
import com.dbj.classpal.books.common.bo.common.CommonIdBO;
import com.dbj.classpal.books.common.bo.common.CommonIdsBO;
import com.dbj.classpal.books.common.bo.product.ProductSalesConfigSaveBO;
import com.dbj.classpal.books.common.dto.album.AppAlbumElementsQueryDTO;
import com.dbj.classpal.books.common.dto.album.AppAlbumVipRightsTagDTO;
import com.dbj.classpal.books.common.dto.product.ProductSalesConfigDTO;
import com.dbj.classpal.books.common.enums.AlbumElementsStatusEnum;
import com.dbj.classpal.books.common.enums.AlbumElementsVisibleEnum;
import com.dbj.classpal.books.common.enums.AlbumType;
import com.dbj.classpal.books.common.enums.product.ProductType;
import com.dbj.classpal.books.service.biz.album.IAppAlbumElementsBiz;
import com.dbj.classpal.books.service.biz.album.IAppAlbumElementsBusinessRefBiz;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBusinessRefBiz;
import com.dbj.classpal.books.service.entity.album.AppAlbumElements;
import com.dbj.classpal.books.service.entity.album.AppAlbumElementsBusinessRef;
import com.dbj.classpal.books.service.entity.material.AppMaterialBusinessRef;
import com.dbj.classpal.books.service.remote.vip.VipRightsTagBusinessRemoteService;
import com.dbj.classpal.books.service.service.album.IAppAlbumElementsService;
import com.dbj.classpal.books.service.service.product.IProductSalesConfigService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.utils.Assert;
import com.dbj.classpal.promotion.client.dto.vip.VipRightsTagBusinessRefListDTO;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.APP_ALBUM_ELEMENTS_DELETE_REF_FAIL_CODE;
import static com.dbj.classpal.books.common.constant.AppErrorCode.APP_ALBUM_ELEMENTS_DELETE_REF_FAIL_MSG;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppAlbumElementsServiceImpl
 * Date:     2025-04-15 10:21:26
 * Description: 表名： ,描述： 表
 */
@Service
public class AppAlbumElementsServiceImpl implements IAppAlbumElementsService {

    @Resource
    private IAppAlbumElementsBiz business;
    @Resource
    private IAppMaterialBusinessRefBiz materialBusinessRef;
    @Resource
    private IAppAlbumElementsBusinessRefBiz elementsBusinessRefBiz;
    @Resource
    private IProductSalesConfigService productSalesConfigService;
    @Resource
    private VipRightsTagBusinessRemoteService vipRightsTagBusinessRemoteService;

    @Override
    public List<AppAlbumElementsQueryDTO> getAppAlbumElementsList(AppAlbumElementsQueryBO bo) throws BusinessException {
        // 1. 查询数据
        LambdaQueryWrapper<AppAlbumElements> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.hasText(bo.getAlbumTitle()),
                        AppAlbumElements::getAlbumTitle, bo.getAlbumTitle())
                .eq(bo.getAppAlbumMenuId() != null,
                        AppAlbumElements::getAppAlbumMenuId, bo.getAppAlbumMenuId())
                .eq(bo.getAlbumStatus() != null,
                        AppAlbumElements::getAlbumStatus, bo.getAlbumStatus())
                .eq(bo.getAlbumVisible() != null,
                        AppAlbumElements::getAlbumVisible, bo.getAlbumVisible())
                .orderByDesc(AppAlbumElements::getCreateTime,AppAlbumElements::getCreateTime);
        List<AppAlbumElements> list = business.list(wrapper);
        //2. 获取查询的id列表
        Set<Integer> idSet = list.stream().map(AppAlbumElements::getId).collect(Collectors.toSet());
        //3. 查询关联业务map
        Map<Integer,Long>businessRefMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(idSet)){
            businessRefMap = materialBusinessRef.lambdaQuery().in(AppMaterialBusinessRef::getBusinessId,idSet).eq(AppMaterialBusinessRef::getBusinessType,bo.getAlbumType()).list().stream().collect(Collectors.groupingBy(AppMaterialBusinessRef::getBusinessId,Collectors.counting()));
        }

        //4. 赋值关联业务数量
        Map<Integer, Long> finalBusinessRefMap = businessRefMap;
        return list.stream().map(d -> {
            AppAlbumElementsQueryDTO queryDTO = new AppAlbumElementsQueryDTO();
            BeanUtil.copyProperties(d,queryDTO);
            if (finalBusinessRefMap.containsKey(d.getId())) {
                queryDTO.setRefCount(finalBusinessRefMap.get(d.getId()).intValue());
            }else{
                queryDTO.setRefCount(0);
            }
            queryDTO.setAlbumVisibleStr(Objects.requireNonNull(AlbumElementsVisibleEnum.getByCode(d.getAlbumVisible())).getType());
            queryDTO.setAlbumStatusStr(Objects.requireNonNull(AlbumElementsStatusEnum.getByCode(d.getAlbumStatus())).getType());
            return queryDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public Page<AppAlbumElementsQueryDTO> pageInfo(PageInfo<AppAlbumElementsQueryBO> pageRequest) throws BusinessException {
        AppAlbumElementsQueryBO data = pageRequest.getData();
        boolean isAudio = AlbumType.AUDIO.getCode().equals(data.getAlbumType());
        ProductType productType = isAudio ? ProductType.AUDIO_ALBUM : ProductType.VIDEO_ALBUM;
        data.setProductType(productType.getCode());

        // 权益标签
        BusinessTypeEnum businessType = isAudio ? BusinessTypeEnum.AUDIO_ALBUM_BUSINESS : BusinessTypeEnum.VIDEO_ALBUM_BUSINESS;
        if (CollUtil.isNotEmpty(data.getVipRightsTagIds())) {
            data.setIds(vipRightsTagBusinessRemoteService.getBusinessIds(businessType, data.getVipRightsTagIds()));
        }

        Page<AppAlbumElementsQueryDTO> page = business.pageAlbumElements(pageRequest.getPage(), data);

        List<AppAlbumElementsQueryDTO> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return page;
        }

        Set<Integer> ids = records.stream().map(AppAlbumElementsQueryDTO::getId).collect(Collectors.toSet());

        Map<Integer, Long> businessRefMap = new HashMap<>();
        Map<Integer, List<VipRightsTagBusinessRefListDTO>> vipTagMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(ids)) {
            List<AppMaterialBusinessRef> list = materialBusinessRef.lambdaQuery()
                    .in(AppMaterialBusinessRef::getBusinessId, ids)
                    .eq(AppMaterialBusinessRef::getBusinessType, data.getAlbumType())
                    .list();
            businessRefMap = list.stream().collect(Collectors.groupingBy(AppMaterialBusinessRef::getBusinessId, Collectors.counting()));
            List<VipRightsTagBusinessRefListDTO> vipRightsTagList = vipRightsTagBusinessRemoteService.getVipRightsTagList(businessType, ids);
            vipTagMap = vipRightsTagList.stream().collect(Collectors.groupingBy(VipRightsTagBusinessRefListDTO::getBusinessId));
        }

        for (AppAlbumElementsQueryDTO record : records) {
            if (businessRefMap.containsKey(record.getId())) {
                record.setRefCount(businessRefMap.get(record.getId()).intValue());
            } else {
                record.setRefCount(0);
            }
            record.setAlbumVisibleStr(AlbumElementsVisibleEnum.getByCode(record.getAlbumVisible()).getType());
            record.setAlbumStatusStr(AlbumElementsStatusEnum.getByCode(record.getAlbumStatus()).getType());
            List<VipRightsTagBusinessRefListDTO> tags = vipTagMap.get(record.getId());
            if (CollUtil.isNotEmpty(tags)) {
                record.setVipRightsTagList(BeanUtil.copyToList(tags, AppAlbumVipRightsTagDTO.class));
            }
        }
        return page;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveAppAlbumElements(AppAlbumElementsSaveBO bo) throws BusinessException {
        AppAlbumElements appAlbumElements = new AppAlbumElements();
        BeanUtil.copyProperties(bo, appAlbumElements);
        Assert.isTrue(business.save(appAlbumElements));

        // 售卖参数
        boolean isAudio = AlbumType.AUDIO.getCode().equals(appAlbumElements.getAlbumType());
        ProductType productType = isAudio ? ProductType.AUDIO_ALBUM : ProductType.VIDEO_ALBUM;
        ProductSalesConfigSaveBO configBO = new ProductSalesConfigSaveBO()
                .setProductId(appAlbumElements.getId())
                .setProductType(productType.getCode())
                .setTrialCount(bo.getTrialCount())
                .setSalesModeStatus(bo.getSalesModeStatus())
                .setSalesMode(bo.getSalesMode())
                .setSalesPrice(bo.getSalesPrice())
                .setOriginalPrice(bo.getOriginalPrice())
                .setListingStatus(StatusEnum.NORMAL.getCode().equals(bo.getAlbumStatus()))
                .setIsHide(StatusEnum.NORMAL.getCode().equals(bo.getAlbumVisible()));
        Assert.isTrue(productSalesConfigService.save(configBO));

        // 权益标签
        BusinessTypeEnum businessType = isAudio ? BusinessTypeEnum.AUDIO_ALBUM_BUSINESS : BusinessTypeEnum.VIDEO_ALBUM_BUSINESS;
        vipRightsTagBusinessRemoteService.saveVipRightsTagBusinessRef(businessType, appAlbumElements.getId(), bo.getVipRightsTagIds());
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteAppAlbumElements(CommonIdsBO bo) throws BusinessException {
        int refCount = elementsBusinessRefBiz.lambdaQuery().in(AppAlbumElementsBusinessRef::getAppAlbumId, bo.getIds()).count().intValue();
        if (refCount>0){
            throw new BusinessException(APP_ALBUM_ELEMENTS_DELETE_REF_FAIL_CODE,APP_ALBUM_ELEMENTS_DELETE_REF_FAIL_MSG);
        }
        materialBusinessRef.lambdaUpdate().in(AppMaterialBusinessRef::getBusinessId, bo.getIds()).remove();
        return business.removeBatchByIds(bo.getIds());
    }

    @Override
    public AppAlbumElementsQueryDTO getAppAlbumElement(CommonIdBO bo) {
        AppAlbumElementsQueryDTO queryDTO = new AppAlbumElementsQueryDTO();
        AppAlbumElements byId = business.getById(bo.getId());
        BeanUtil.copyProperties(byId, queryDTO);
        ProductType productType = AlbumType.AUDIO.getCode().equals(byId.getAlbumType()) ? ProductType.AUDIO_ALBUM : ProductType.VIDEO_ALBUM;
        ProductSalesConfigDTO salesConfig = productSalesConfigService.getProductSalesConfig(productType, byId.getId());
        if (salesConfig != null) {
            queryDTO.setTrialCount(salesConfig.getTrialCount())
                    .setSalesModeStatus(salesConfig.getSalesModeStatus())
                    .setSalesMode(salesConfig.getSalesMode())
                    .setSalesPrice(salesConfig.getSalesPrice())
                    .setOriginalPrice(salesConfig.getOriginalPrice());
        }
        return queryDTO;
    }

    @Override
    public Boolean updateAppAlbumElement(AppAlbumElementsUpdateBO bo) {
        AppAlbumElements appAlbumElements = new AppAlbumElements();
        BeanUtil.copyProperties(bo, appAlbumElements);
        return business.updateById(appAlbumElements);
    }

    @Override
    public Boolean updateAppAlbumElementTitle(AppAlbumElementsUpdateBO bo) {
        return business.lambdaUpdate().eq(AppAlbumElements::getId,bo.getId()).set(AppAlbumElements::getAlbumTitle,bo.getAlbumTitle()).update();
    }

    @Override
    public Boolean updateAppAlbumElementRemark(AppAlbumElementsUpdateBO bo) {
        return business.lambdaUpdate().eq(AppAlbumElements::getId,bo.getId()).set(AppAlbumElements::getAlbumRemark,bo.getAlbumRemark()).update();
    }

    @Override
    public Boolean updateAppAlbumElementVisible(AppAlbumElementsUpdateVisibleBO bo) {
        return business.lambdaUpdate().eq(AppAlbumElements::getId,bo.getId()).set(AppAlbumElements::getAlbumVisible,bo.getAlbumVisible()).update();
    }

    @Override
    public Boolean updateAppAlbumElementStatus(AppAlbumElementsUpdateStatusBO bo) {
        return business.lambdaUpdate().eq(AppAlbumElements::getId,bo.getId()).set(AppAlbumElements::getAlbumStatus,bo.getAlbumStatus()).update();
    }

    @Override
    public Boolean updateAppAlbumElementCover(AppAlbumElementsUpdateBO bo) {
        return business.lambdaUpdate().eq(AppAlbumElements::getId,bo.getId()).set(AppAlbumElements::getAlbumCover,bo.getAlbumCover()).update();
    }

    @Override
    public Boolean allExistsByIds(Collection<Integer> ids) {
        return business.allExistsByIds(ids);
    }

    @Override
    public List<AppAlbumElementsQueryApiDTO> selectAppAlbumElementsListByIds(Collection<Integer> ids) {
        return business.lambdaQuery().in(AppAlbumElements::getId, ids).list().stream().map( dto -> {
                AppAlbumElementsQueryApiDTO queryApiDTO = new AppAlbumElementsQueryApiDTO();
                BeanUtil.copyProperties(dto, queryApiDTO);
                return queryApiDTO;
        }).collect(Collectors.toList());
    }


    private AppAlbumElementsQueryDTO convertToDTO(AppAlbumElements appAlbumElements) {
        AppAlbumElementsQueryDTO vo = new AppAlbumElementsQueryDTO();
        BeanUtil.copyProperties(appAlbumElements, vo);
        return vo;
    }
}
