package com.dbj.classpal.books.service.service.advertisement;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.client.bo.advertisement.AdevertisementDelBO;
import com.dbj.classpal.books.client.bo.advertisement.AdvertisementAppBO;
import com.dbj.classpal.books.client.bo.advertisement.AdvertisementPageBO;
import com.dbj.classpal.books.client.bo.advertisement.AdvertisementStatusUpdateBO;
import com.dbj.classpal.books.client.bo.advertisement.AdvertisementUpsertBO;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.dto.advertisement.AdvertisementAppDTO;
import com.dbj.classpal.books.client.dto.advertisement.AdvertisementDTO;
import com.dbj.classpal.books.service.entity.advertisement.Advertisement;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;

import java.util.List;

/**
 * <p>
 * 广告信息表 业务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
public interface IAdvertisementService extends IService<Advertisement> {

    /**
     * 获取广告信息分页列表
     *
     * @param pageInfo 分页信息
     * @return Page<AdvertisementDTO>
     */
    Page<AdvertisementDTO> getAdvertisementPage(PageInfo<AdvertisementPageBO> pageInfo);

    /**
     * 获取广告信息详情
     *
     * @param bo 广告信息bo
     * @return AdvertisementDTO
     * @throws BusinessException 业务异常
     */
    AdvertisementDTO getAdvertisementInfo(CommonIdApiBO bo) throws BusinessException;

    /**
     * 保存广告信息
     *
     * @param bo 广告信息bo
     * @return Boolean
     * @throws BusinessException 业务异常
     */
    Boolean saveAdvertisement(AdvertisementUpsertBO bo) throws BusinessException;

    /**
     * 更新广告信息
     *
     * @param bo 广告信息bo
     * @return Boolean
     * @throws BusinessException 业务异常
     */
    Boolean updateAdvertisement(AdvertisementUpsertBO bo) throws BusinessException;

    /**
     * 更新广告信息状态
     *
     * @param bo 广告信息bo
     * @return Boolean
     */
    Boolean updateAdvertisementStatus(AdvertisementStatusUpdateBO bo);
    /**
     * 删除广告信息
     *
     * @param bo 广告信息bo
     * @return Boolean
     * @throws BusinessException 业务异常
     */
    Boolean deleteAdvertisement(AdevertisementDelBO bo) throws BusinessException;

    /**
     * 获取用户广告信息列表
     *
     * @param bo 广告信息bo
     * @return Map<String, List<AdvertisementAppDTO>>
     */
    List<AdvertisementAppDTO> getAppUserAdvertisement(AdvertisementAppBO bo);
}
