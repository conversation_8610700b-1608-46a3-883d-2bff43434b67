package com.dbj.classpal.books.service.strategy.advertisement.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.dbj.classpal.app.client.api.regions.RegionsClientApi;
import com.dbj.classpal.app.client.bo.regions.RegionsBO;
import com.dbj.classpal.app.client.dto.regions.RegionsDTO;
import com.dbj.classpal.app.client.dto.regions.RegionsDataDTO;
import com.dbj.classpal.books.client.dto.advertisement.AdvertisementLevelConditionDTO;
import com.dbj.classpal.books.client.dto.advertisement.AdvertisementLevelConditionOptionDTO;
import com.dbj.classpal.books.common.enums.advertisement.AdvertiseOptionTypeEnum;
import com.dbj.classpal.books.common.enums.advertisement.AdvertisementConditionCodeEnum;
import com.dbj.classpal.books.service.api.client.advertisement.AdvertisementContext;
import com.dbj.classpal.books.service.strategy.advertisement.IAdvertisementConditionStrategy;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import com.dbj.classpal.framework.redisson.config.RedissonRedisUtils;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.dbj.classpal.app.client.constant.ConstantRedis.DBJ_CLASSPAL_ADVERTISEMENT_REGIONS_CACHE_KEY;

/**
 * <AUTHOR> Yi
 * @since 2025-04-26 14:27
 */
@Component
public class AreaConditionStrategy implements IAdvertisementConditionStrategy {

    @Resource
    private RegionsClientApi regionsClientApi;

    @Resource
    private RedissonRedisUtils redisUtils;

    @Override
    public String getCode() {
        return AdvertisementConditionCodeEnum.AREA.getCode();
    }

    @Override
    public void doHandle(AdvertisementContext context) {
        //缓存预热
        RegionsDataDTO regionsDataDTO = getRegionsDataDTO();

        AdvertisementLevelConditionDTO condition = context.getAdvertisementLevelCondition();
        List<AdvertisementLevelConditionOptionDTO> conditionOptionList = condition.getAdvertisementLevelConditionOptionList();
        for (AdvertisementLevelConditionOptionDTO optionDTO : conditionOptionList) {
            List<Integer> areaIds = StrUtil.split(optionDTO.getOptionValue(), ',')
                    .stream()
                    .map(Integer::parseInt)
                    .toList();
            List<RegionsDTO> regionsDTOS;
            if(AdvertiseOptionTypeEnum.AREA_PROVINCE.getType().equals(optionDTO.getType())) {
                regionsDTOS = regionsDataDTO.getProvinces().stream().filter(regionsDTO -> areaIds.contains(regionsDTO.getId())).collect(Collectors.toList());
            } else if(AdvertiseOptionTypeEnum.AREA_CITY.getType().equals(optionDTO.getType())) {
                regionsDTOS = regionsDataDTO.getCities().stream().filter(regionsDTO -> areaIds.contains(regionsDTO.getId())).collect(Collectors.toList());
            } else if(AdvertiseOptionTypeEnum.AREA_LEVEL.getType().equals(optionDTO.getType())) {
                regionsDTOS = regionsDataDTO.getCities().stream().filter(regionsDTO -> areaIds.contains(regionsDTO.getRating())).collect(Collectors.toList());
            } else {
                regionsDTOS = Collections.emptyList();
            }

            if (AdvertiseOptionTypeEnum.AREA_PROVINCE.getType().equals(optionDTO.getType())) {
                condition.setIsEligible(regionsDTOS.stream().map(RegionsDTO::getName)
                        .anyMatch(name -> name.equals(context.getProvinceName())));
                break;
            } else if (AdvertiseOptionTypeEnum.AREA_CITY.getType().equals(optionDTO.getType())
                || AdvertiseOptionTypeEnum.AREA_LEVEL.getType().equals(optionDTO.getType())) {
                condition.setIsEligible(regionsDTOS.stream().map(RegionsDTO::getName)
                        .anyMatch(name -> name.equals(context.getCityName())));
                break;
            }
        }
    }

    /**
     * todo getRegionsData远程接口实现本身已做了redis缓存处理
     */
    private RegionsDataDTO getRegionsDataDTO() {
        RegionsDataDTO regionsDataDTO;
        if(redisUtils.hasKey(DBJ_CLASSPAL_ADVERTISEMENT_REGIONS_CACHE_KEY)) {
            regionsDataDTO = JSON.parseObject(redisUtils.getValue(DBJ_CLASSPAL_ADVERTISEMENT_REGIONS_CACHE_KEY), RegionsDataDTO.class);
        } else {
            RestResponse<RegionsDataDTO> result = regionsClientApi.getRegionsData(new RegionsBO());
            try {
                regionsDataDTO = result.returnProcess(result);
            } catch (BusinessException e) {
                regionsDataDTO = new RegionsDataDTO();
            }}
        return regionsDataDTO;
    }
}
