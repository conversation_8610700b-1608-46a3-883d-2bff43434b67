package com.dbj.classpal.books.service.service.ebooks;

import com.dbj.classpal.books.common.bo.ebooks.H5LandingPageBO;
import com.dbj.classpal.books.common.dto.ebooks.H5LandingPageDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;

/**
 * H5落地页服务接口
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
public interface IH5LandingPageService {

    /**
     * 获取H5落地页数据
     *
     * @param request 请求参数
     * @return H5落地页数据
     * @throws BusinessException 业务异常
     */
    H5LandingPageDTO getLandingPageData(H5LandingPageBO request) throws BusinessException;
}
