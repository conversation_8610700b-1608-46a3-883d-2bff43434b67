package com.dbj.classpal.books.service.service.pointreading.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingHotspotMediaBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingHotspotQueryBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingHotspotSaveBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingHotspotUpdateBO;
import com.dbj.classpal.books.common.bo.question.QuestionCategoryBusinessSettingsBO;
import com.dbj.classpal.books.common.dto.pointreading.PointReadingHotspotDTO;
import com.dbj.classpal.books.common.dto.pointreading.PointReadingHotspotMediaDTO;
import com.dbj.classpal.books.common.dto.question.QuestionCategoryBusinessSettingsDTO;
import com.dbj.classpal.books.common.enums.PointReadingEventTypeEnum;
import com.dbj.classpal.books.common.enums.QuestionBusinessTypeEnum;
import com.dbj.classpal.books.service.biz.pointreading.IPointReadingHotspotBiz;
import com.dbj.classpal.books.service.biz.pointreading.IPointReadingHotspotMediaBiz;
import com.dbj.classpal.books.service.biz.question.IQuestionCategoryBusinessSettingsBiz;
import com.dbj.classpal.books.service.entity.pointreading.PointReadingHotspot;
import com.dbj.classpal.books.service.processor.media.MediaRefProcessorManager;
import com.dbj.classpal.books.service.service.pointreading.IPointReadingHotspotService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 点读书热点区域 服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
@Service
public class PointReadingHotspotServiceImpl implements IPointReadingHotspotService {

    @Resource
    private IPointReadingHotspotBiz pointReadingHotspotBiz;

    @Resource
    private IQuestionCategoryBusinessSettingsBiz questionCategoryBusinessSettingsBiz;

    @Resource
    private IPointReadingHotspotMediaBiz pointReadingHotspotMediaBiz;

    @Resource
    private MediaRefProcessorManager mediaRefProcessorManager;

    @Override
    public Page<PointReadingHotspotDTO> pageHotspot(PageInfo<PointReadingHotspotQueryBO> pageInfo) throws BusinessException {
        Page<PointReadingHotspot> page = new Page<>(pageInfo.getPageNum(), pageInfo.getPageSize());
        Page<PointReadingHotspotDTO> resultPage = pointReadingHotspotBiz.pageHotspot(page, pageInfo.getData());

        // 为分页结果中的每个热点加载媒体文件列表
        if (resultPage != null && CollUtil.isNotEmpty(resultPage.getRecords())) {
            for (PointReadingHotspotDTO hotspot : resultPage.getRecords()) {
                try {
                    List<PointReadingHotspotMediaDTO> mediaList = getHotspotMediaList(hotspot.getId());
                    hotspot.setMediaList(mediaList);
                } catch (Exception e) {
                    log.warn("查询热点媒体文件失败，热点ID：{}，错误：{}", hotspot.getId(), e.getMessage());
                    hotspot.setMediaList(new ArrayList<>());
                }
            }
        }

        return resultPage;
    }

    @Override
    public PointReadingHotspotDTO detail(Integer id) throws BusinessException {
        // 1. 获取热点详情
        PointReadingHotspotDTO dto = pointReadingHotspotBiz.detail(id);

        if (dto != null) {
            // 2. 查询媒体文件列表
            try {
                List<PointReadingHotspotMediaDTO> mediaList = getHotspotMediaList(id);
                dto.setMediaList(mediaList);
            } catch (Exception e) {
                log.warn("查询热点媒体文件失败，热点ID：{}，错误：{}", id, e.getMessage());
                dto.setMediaList(new ArrayList<>());
            }

            // 3. 查询答题设置（仅当事件类型为答题时）
            if (Objects.equals(dto.getEventType(), PointReadingEventTypeEnum.QUIZ.getCode())) {
                try {
                    QuestionCategoryBusinessSettingsDTO questionSettings = questionCategoryBusinessSettingsBiz
                            .getSettingsByBusiness(id, QuestionBusinessTypeEnum.POINT_READING.getValue());
                    dto.setQuestionSettings(questionSettings);
                } catch (Exception e) {
                    log.warn("查询热点答题设置失败，热点ID：{}，错误：{}", id, e.getMessage());
                }
            }
        }

        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer save(PointReadingHotspotSaveBO saveBO) throws BusinessException {
        // 1. 保存热点
        Integer hotspotId = pointReadingHotspotBiz.saveHotspot(saveBO);

        // 2. 处理媒体文件
        handleMediaFiles(hotspotId, saveBO.getMediaList());

        // 3. 处理答题设置
        handleQuestionSettings(hotspotId, saveBO.getQuestionSettings(), saveBO.getEventType());

        return hotspotId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveChapterHotspots(Integer chapterId, List<PointReadingHotspotSaveBO> hotspots) throws BusinessException {
        // 1. 查询章节下现有热点ID，用于删除答题设置
        List<PointReadingHotspotDTO> existingHotspots = pointReadingHotspotBiz.getHotspotsByChapter(chapterId);
        List<Integer> existingHotspotIds = existingHotspots.stream()
                .map(PointReadingHotspotDTO::getId)
                .collect(Collectors.toList());

        // 2. 批量删除现有答题设置
        if (CollUtil.isNotEmpty(existingHotspotIds)) {
            try {
                questionCategoryBusinessSettingsBiz.deleteSettingsBatch(existingHotspotIds, QuestionBusinessTypeEnum.POINT_READING.getValue());
                log.info("批量保存章节热点前删除答题设置完成，章节ID：{}，热点数量：{}", chapterId, existingHotspotIds.size());
            } catch (Exception e) {
                log.warn("批量删除热点答题设置失败，章节ID：{}，错误：{}", chapterId, e.getMessage());
            }
        }

        // 3. 保存新热点
        Boolean result = pointReadingHotspotBiz.saveChapterHotspots(chapterId, hotspots);

        // 4. 批量处理媒体文件
        if (result) {
            handleMediaFilesBatch(chapterId, hotspots);
        }

        // 5. 批量创建答题设置
        if (result) {
            handleQuestionSettingsBatch(chapterId, hotspots);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateChapterHotspots(Integer chapterId, List<PointReadingHotspotSaveBO> hotspots) throws BusinessException {
        // 1. 查询章节下现有热点ID，用于删除答题设置
        List<PointReadingHotspotDTO> existingHotspots = pointReadingHotspotBiz.getHotspotsByChapter(chapterId);
        List<Integer> existingHotspotIds = existingHotspots.stream()
                .map(PointReadingHotspotDTO::getId)
                .collect(Collectors.toList());

        // 2. 批量删除现有答题设置
        if (CollUtil.isNotEmpty(existingHotspotIds)) {
            try {
                questionCategoryBusinessSettingsBiz.deleteSettingsBatch(existingHotspotIds, QuestionBusinessTypeEnum.POINT_READING.getValue());
                log.info("批量更新章节热点前删除答题设置完成，章节ID：{}，热点数量：{}", chapterId, existingHotspotIds.size());
            } catch (Exception e) {
                log.warn("批量删除热点答题设置失败，章节ID：{}，错误：{}", chapterId, e.getMessage());
            }
        }

        // 3. 更新热点
        Boolean result = pointReadingHotspotBiz.updateChapterHotspots(chapterId, hotspots);

        // 4. 批量创建答题设置
        if (result) {
            handleQuestionSettingsBatch(chapterId, hotspots);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(PointReadingHotspotUpdateBO updateBO) throws BusinessException {
        // 1. 更新热点
        Boolean result = pointReadingHotspotBiz.updateHotspot(updateBO);

        // 2. 处理媒体文件
        if (result) {
            handleMediaFiles(updateBO.getId(), updateBO.getMediaList());
        }

        // 3. 处理答题设置
        if (result) {
            handleQuestionSettings(updateBO.getId(), updateBO.getQuestionSettings(), updateBO.getEventType());
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(Integer id) throws BusinessException {
        // 1. 删除热点
        Boolean result = pointReadingHotspotBiz.deleteHotspot(id);

        if (result) {
            // 2. 删除相关的媒体文件
            try {
                mediaRefProcessorManager.deleteHotspotMediaRefs(id);
                pointReadingHotspotMediaBiz.deleteHotspotMediaSnapshot(id);
                log.info("删除热点媒体文件成功，热点ID：{}", id);
            } catch (Exception e) {
                log.warn("删除热点媒体文件失败，热点ID：{}，错误：{}", id, e.getMessage());
                // 不影响热点删除的主流程
            }

            // 3. 删除相关的答题设置
            try {
                questionCategoryBusinessSettingsBiz.deleteSettingsByBusiness(id, QuestionBusinessTypeEnum.POINT_READING.getValue());
                log.info("删除热点答题设置成功，热点ID：{}", id);
            } catch (Exception e) {
                log.warn("删除热点答题设置失败，热点ID：{}，错误：{}", id, e.getMessage());
                // 不影响热点删除的主流程
            }
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteBatch(List<Integer> ids) throws BusinessException {
        // 1. 删除热点
        Boolean result = pointReadingHotspotBiz.deleteBatch(ids);

        if (result) {
            // 2. 批量删除相关的媒体文件
            try {
                mediaRefProcessorManager.batchDeleteHotspotMediaRefs(ids);
                pointReadingHotspotMediaBiz.batchDeleteHotspotMediaSnapshot(ids);
                log.info("批量删除热点媒体文件完成，热点ID列表：{}", ids);
            } catch (Exception e) {
                log.warn("批量删除热点媒体文件失败，热点ID列表：{}，错误：{}", ids, e.getMessage());
                // 不影响热点删除的主流程
            }

            // 3. 批量删除相关的答题设置
            try {
                questionCategoryBusinessSettingsBiz.deleteSettingsBatch(ids, QuestionBusinessTypeEnum.POINT_READING.getValue());
                log.info("批量删除热点答题设置完成，热点ID列表：{}", ids);
            } catch (Exception e) {
                log.warn("批量删除热点答题设置失败，热点ID列表：{}，错误：{}", ids, e.getMessage());
                // 不影响热点删除的主流程
            }
        }

        return result;
    }

    @Override
    public List<PointReadingHotspotDTO> getHotspotsByChapter(Integer chapterId) throws BusinessException {
        // 1. 获取热点基础信息
        List<PointReadingHotspotDTO> hotspots = pointReadingHotspotBiz.getHotspotsByChapter(chapterId);

        // 2. 为每个热点加载媒体文件列表
        if (CollUtil.isNotEmpty(hotspots)) {
            for (PointReadingHotspotDTO hotspot : hotspots) {
                try {
                    List<PointReadingHotspotMediaDTO> mediaList = getHotspotMediaList(hotspot.getId());
                    hotspot.setMediaList(mediaList);
                } catch (Exception e) {
                    log.warn("查询热点媒体文件失败，热点ID：{}，错误：{}", hotspot.getId(), e.getMessage());
                    hotspot.setMediaList(new ArrayList<>());
                }
            }
        }

        return hotspots;
    }

    @Override
    public List<PointReadingHotspotDTO> getHotspotsTreeByChapter(Integer chapterId) throws BusinessException {
        // 1. 获取热点树形结构
        List<PointReadingHotspotDTO> hotspotsTree = pointReadingHotspotBiz.getHotspotsTreeByChapter(chapterId);

        // 2. 为树形结构中的每个热点加载媒体文件列表（递归处理）
        if (CollUtil.isNotEmpty(hotspotsTree)) {
            loadMediaListForHotspotsTree(hotspotsTree);
        }

        return hotspotsTree;
    }

    /**
     * 处理答题设置（service 层编排）
     *
     * @param hotspotId 热点ID
     * @param questionSettings 答题设置
     * @param eventType 事件类型
     */
    private void handleQuestionSettings(Integer hotspotId, QuestionCategoryBusinessSettingsBO questionSettings, Integer eventType) throws BusinessException {
        // 先删除可能存在的答题设置（无论事件类型是什么）
        try {
            questionCategoryBusinessSettingsBiz.deleteSettingsByBusiness(hotspotId, QuestionBusinessTypeEnum.POINT_READING.getValue());
        } catch (Exception e) {
            log.warn("删除热点答题设置失败，热点ID：{}，错误：{}", hotspotId, e.getMessage());
        }

        // 只有事件类型为答题时才创建新的答题设置
        if (!Objects.equals(eventType, PointReadingEventTypeEnum.QUIZ.getCode())) {
            return;
        }

        if (questionSettings == null) {
            log.warn("事件类型为答题但未提供答题设置，热点ID：{}", hotspotId);
            return;
        }

        // 设置业务ID和业务类型
        questionSettings.setBusinessId(hotspotId);
        questionSettings.setBusinessType(QuestionBusinessTypeEnum.POINT_READING.getValue());

        // 创建新设置
        questionCategoryBusinessSettingsBiz.createSettings(questionSettings);
        log.debug("创建点读热点答题设置成功，热点ID：{}", hotspotId);
    }

    /**
     * 批量处理答题设置
     *
     * @param chapterId 章节ID
     * @param hotspots 热点保存BO列表
     */
    private void handleQuestionSettingsBatch(Integer chapterId, List<PointReadingHotspotSaveBO> hotspots) throws BusinessException {
        if (CollUtil.isEmpty(hotspots)) {
            return;
        }

        // 获取新保存的热点列表
        List<PointReadingHotspotDTO> newHotspots = pointReadingHotspotBiz.getHotspotsByChapter(chapterId);
        if (CollUtil.isEmpty(newHotspots)) {
            return;
        }

        // 收集需要创建答题设置的热点
        List<QuestionCategoryBusinessSettingsBO> settingsToCreate = new ArrayList<>();

        for (int i = 0; i < Math.min(hotspots.size(), newHotspots.size()); i++) {
            PointReadingHotspotSaveBO saveBO = hotspots.get(i);
            PointReadingHotspotDTO hotspotDTO = newHotspots.get(i);

            // 只处理答题类型的热点
            if (Objects.equals(hotspotDTO.getEventType(), PointReadingEventTypeEnum.QUIZ.getCode())
                && saveBO.getQuestionSettings() != null) {

                QuestionCategoryBusinessSettingsBO questionSettings = saveBO.getQuestionSettings();
                questionSettings.setBusinessId(hotspotDTO.getId());
                questionSettings.setBusinessType(QuestionBusinessTypeEnum.POINT_READING.getValue());

                settingsToCreate.add(questionSettings);
            }
        }

        // 批量创建答题设置
        if (CollUtil.isNotEmpty(settingsToCreate)) {
            try {
                questionCategoryBusinessSettingsBiz.createSettingsBatch(settingsToCreate);
                log.info("批量创建章节热点答题设置完成，章节ID：{}，设置数量：{}", chapterId, settingsToCreate.size());
            } catch (Exception e) {
                log.error("批量创建章节热点答题设置失败，章节ID：{}，错误：{}", chapterId, e.getMessage());
                // 降级为逐个处理
                for (QuestionCategoryBusinessSettingsBO settings : settingsToCreate) {
                    try {
                        questionCategoryBusinessSettingsBiz.createSettings(settings);
                    } catch (Exception ex) {
                        log.warn("降级处理热点答题设置失败，热点ID：{}，错误：{}", settings.getBusinessId(), ex.getMessage());
                    }
                }
            }
        }
    }

    /**
     * 处理热点媒体文件
     *
     * @param hotspotId 热点ID
     * @param mediaList 媒体文件列表
     */
    private void handleMediaFiles(Integer hotspotId, List<PointReadingHotspotMediaBO> mediaList) throws BusinessException {
        try {
            if (CollUtil.isNotEmpty(mediaList)) {
                // 1. 保存实时媒体引用
                mediaRefProcessorManager.saveHotspotMediaRefs(hotspotId, mediaList);

                // 2. 保存媒体快照
                pointReadingHotspotMediaBiz.saveHotspotMediaSnapshot(hotspotId, mediaList);

                log.info("处理热点媒体文件完成，热点ID：{}，媒体数量：{}", hotspotId, mediaList.size());
            } else {
                // 删除现有媒体文件
                mediaRefProcessorManager.deleteHotspotMediaRefs(hotspotId);
                pointReadingHotspotMediaBiz.deleteHotspotMediaSnapshot(hotspotId);

                log.info("删除热点媒体文件完成，热点ID：{}", hotspotId);
            }
        } catch (Exception e) {
            log.error("处理热点媒体文件失败，热点ID：{}，错误：{}", hotspotId, e.getMessage(), e);
            throw new BusinessException("处理媒体文件失败：" + e.getMessage());
        }
    }

    /**
     * 获取热点媒体文件列表（快照数据 + 实时数据）
     *
     * @param hotspotId 热点ID
     * @return 媒体文件列表
     */
    private List<PointReadingHotspotMediaDTO> getHotspotMediaList(Integer hotspotId) {
        try {
            // 1. 获取快照数据
            List<PointReadingHotspotMediaDTO> snapshotList = pointReadingHotspotMediaBiz.getHotspotMediaSnapshot(hotspotId);

            // 2. 获取实时数据
            List<PointReadingHotspotMediaDTO> currentList = mediaRefProcessorManager.getHotspotMediaRefs(hotspotId);

            // 3. 合并数据：以快照为基础，补充实时数据
            Map<String, PointReadingHotspotMediaDTO> currentMap = currentList.stream()
                    .collect(Collectors.toMap(
                            media -> media.getMediaSource() + "_" + media.getMediaId(),
                            media -> media,
                            (existing, replacement) -> replacement
                    ));

            for (PointReadingHotspotMediaDTO snapshot : snapshotList) {
                String key = snapshot.getMediaSource() + "_" + snapshot.getMediaId();
                PointReadingHotspotMediaDTO current = currentMap.get(key);

                if (current != null) {
                    // 设置实时数据
                    snapshot.setCurrentMediaUrl(current.getCurrentMediaUrl());
                    snapshot.setCurrentMediaName(current.getCurrentMediaName());
                    snapshot.setCurrentMediaAvailable(current.getCurrentMediaAvailable());
                } else {
                    // 实时数据不存在，标记为不可用
                    snapshot.setCurrentMediaAvailable(false);
                }
            }

            return snapshotList;
        } catch (Exception e) {
            log.error("获取热点媒体文件列表失败，热点ID：{}，错误：{}", hotspotId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 批量处理媒体文件
     *
     * @param chapterId 章节ID
     * @param hotspots 热点保存BO列表
     */
    private void handleMediaFilesBatch(Integer chapterId, List<PointReadingHotspotSaveBO> hotspots) throws BusinessException {
        if (CollUtil.isEmpty(hotspots)) {
            return;
        }

        // 获取新保存的热点列表
        List<PointReadingHotspotDTO> newHotspots = pointReadingHotspotBiz.getHotspotsByChapter(chapterId);
        if (CollUtil.isEmpty(newHotspots)) {
            return;
        }

        // 处理每个热点的媒体文件
        for (int i = 0; i < Math.min(hotspots.size(), newHotspots.size()); i++) {
            PointReadingHotspotSaveBO saveBO = hotspots.get(i);
            PointReadingHotspotDTO hotspotDTO = newHotspots.get(i);

            if (CollUtil.isNotEmpty(saveBO.getMediaList())) {
                try {
                    handleMediaFiles(hotspotDTO.getId(), saveBO.getMediaList());
                } catch (Exception e) {
                    log.warn("批量处理热点媒体文件失败，热点ID：{}，错误：{}", hotspotDTO.getId(), e.getMessage());
                    // 单个热点媒体处理失败不影响其他热点
                }
            }
        }

        log.info("批量处理章节热点媒体文件完成，章节ID：{}，热点数量：{}", chapterId, newHotspots.size());
    }

    /**
     * 为热点树形结构递归加载媒体文件列表
     *
     * @param hotspots 热点列表
     */
    private void loadMediaListForHotspotsTree(List<PointReadingHotspotDTO> hotspots) {
        if (CollUtil.isEmpty(hotspots)) {
            return;
        }

        for (PointReadingHotspotDTO hotspot : hotspots) {
            // 为当前热点加载媒体文件列表
            try {
                List<PointReadingHotspotMediaDTO> mediaList = getHotspotMediaList(hotspot.getId());
                hotspot.setMediaList(mediaList);
            } catch (Exception e) {
                log.warn("查询热点媒体文件失败，热点ID：{}，错误：{}", hotspot.getId(), e.getMessage());
                hotspot.setMediaList(new ArrayList<>());
            }

            // 递归处理子节点
            if (CollUtil.isNotEmpty(hotspot.getChildren())) {
                loadMediaListForHotspotsTree(hotspot.getChildren());
            }
        }
    }

}
