package com.dbj.classpal.books.service.api.client.pointreading;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.pointreading.PointReadingChapterApi;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingChapterBatchApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingChapterQueryApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingChapterSaveApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingChapterSortApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingChapterUpdateApiBO;
import com.dbj.classpal.books.client.dto.pointreading.PointReadingChapterApiDTO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingChapterQueryBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingChapterSaveBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingChapterUpdateBO;
import com.dbj.classpal.books.common.dto.pointreading.PointReadingChapterDTO;
import com.dbj.classpal.books.service.service.pointreading.IPointReadingChapterService;
import com.dbj.classpal.framework.commons.converter.PageInfoConverter;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

/**
 * 点读书章节 API实现类
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
@RestController
public class PointReadingChapterApiImpl implements PointReadingChapterApi {

    @Resource
    private IPointReadingChapterService pointReadingChapterService;

    @Override
    public RestResponse<Page<PointReadingChapterApiDTO>> pageChapter(PageInfo<PointReadingChapterQueryApiBO> pageInfo) throws BusinessException {
        log.info("分页查询点读书章节 入参：{}", JSON.toJSONString(pageInfo));

        PageInfo<PointReadingChapterQueryBO> servicePageRequest = PageInfoConverter.convertPageInfo(pageInfo, PointReadingChapterQueryBO.class);
        Page<PointReadingChapterDTO> servicePage = pointReadingChapterService.pageChapter(servicePageRequest);
        Page<PointReadingChapterApiDTO> apiPage = convertApiPage(servicePage);

        log.info("分页查询点读书章节 结果：{}", JSON.toJSONString(apiPage));
        return RestResponse.success(apiPage);
    }

    @Override
    public RestResponse<PointReadingChapterApiDTO> detail(Integer id) throws BusinessException {
        log.info("查询章节详情 入参：{}", id);
        
        PointReadingChapterDTO dto = pointReadingChapterService.detail(id);
        PointReadingChapterApiDTO apiDTO = convertToApiDTO(dto);
        
        log.info("查询章节详情 结果：{}", JSON.toJSONString(apiDTO));
        return RestResponse.success(apiDTO);
    }

    @Override
    public RestResponse<Integer> save(PointReadingChapterSaveApiBO saveBO) throws BusinessException {
        log.info("保存点读书章节 入参：{}", JSON.toJSONString(saveBO));
        
        PointReadingChapterSaveBO saveDTO = new PointReadingChapterSaveBO();
        BeanUtil.copyProperties(saveBO, saveDTO);
        
        Integer id = pointReadingChapterService.save(saveDTO);
        
        log.info("保存点读书章节 结果：{}", id);
        return RestResponse.success(id);
    }

    @Override
    public RestResponse<Boolean> update(PointReadingChapterUpdateApiBO updateBO) throws BusinessException {
        log.info("更新点读书章节 入参：{}", JSON.toJSONString(updateBO));
        
        PointReadingChapterUpdateBO updateDTO = new PointReadingChapterUpdateBO();
        BeanUtil.copyProperties(updateBO, updateDTO);
        
        Boolean result = pointReadingChapterService.update(updateDTO);
        
        log.info("更新点读书章节 结果：{}", result);
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<Boolean> delete(Integer id) throws BusinessException {
        log.info("删除点读书章节 入参：{}", id);
        
        Boolean result = pointReadingChapterService.delete(id);
        
        log.info("删除点读书章节 结果：{}", result);
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<Boolean> deleteBatch(PointReadingChapterBatchApiBO batchBO) throws BusinessException {
        log.info("批量删除点读书章节 入参：{}", JSON.toJSONString(batchBO));
        
        Boolean result = pointReadingChapterService.deleteBatch(batchBO.getIds());
        
        log.info("批量删除点读书章节 结果：{}", result);
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<List<PointReadingChapterApiDTO>> getChapterByMenu(Integer menuId) throws BusinessException {
        log.info("查询目录下的章节列表 入参：{}", menuId);
        
        List<PointReadingChapterDTO> Chapter = pointReadingChapterService.getChapterByMenu(menuId);
        List<PointReadingChapterApiDTO> apiChapter = Chapter.stream()
                .map(this::convertToApiDTO)
                .collect(Collectors.toList());
        
        log.info("查询目录下的章节列表 结果：{}", JSON.toJSONString(apiChapter));
        return RestResponse.success(apiChapter);
    }

    @Override
    public RestResponse<List<PointReadingChapterApiDTO>> getChapterByBook(Integer bookId) throws BusinessException {
        log.info("查询点读书下的章节列表 入参：{}", bookId);
        
        List<PointReadingChapterDTO> Chapter = pointReadingChapterService.getChapterByBook(bookId);
        List<PointReadingChapterApiDTO> apiChapter = Chapter.stream()
                .map(this::convertToApiDTO)
                .collect(Collectors.toList());
        
        log.info("查询点读书下的章节列表 结果：{}", JSON.toJSONString(apiChapter));
        return RestResponse.success(apiChapter);
    }

    @Override
    public RestResponse<Boolean> updateSort(PointReadingChapterSortApiBO sortBO) throws BusinessException {
        log.info("更新排序 入参：{}", JSON.toJSONString(sortBO));
        
        Boolean result = pointReadingChapterService.updateSort(sortBO.getId(), sortBO.getSortNum());
        
        log.info("更新排序 结果：{}", result);
        return RestResponse.success(result);
    }



    /**
     * 转换API分页
     */
    private Page<PointReadingChapterApiDTO> convertApiPage(Page<PointReadingChapterDTO> servicePage) {
        Page<PointReadingChapterApiDTO> apiPage = new Page<>(servicePage.getCurrent(), servicePage.getSize(), servicePage.getTotal());
        List<PointReadingChapterApiDTO> apiList = servicePage.getRecords().stream()
                .map(dto -> BeanUtil.copyProperties(dto, PointReadingChapterApiDTO.class))
                .collect(Collectors.toList());
        apiPage.setRecords(apiList);
        return apiPage;
    }

    /**
     * 转换为API DTO
     */
    private PointReadingChapterApiDTO convertToApiDTO(PointReadingChapterDTO dto) {
        PointReadingChapterApiDTO apiDTO = new PointReadingChapterApiDTO();
        BeanUtil.copyProperties(dto, apiDTO);
        return apiDTO;
    }
}
