package com.dbj.classpal.books.service.service.pointreading.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingChapterQueryBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingChapterSaveBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingChapterUpdateBO;
import com.dbj.classpal.books.common.dto.pointreading.PointReadingChapterDTO;
import com.dbj.classpal.books.service.biz.pointreading.IPointReadingChapterBiz;
import com.dbj.classpal.books.service.entity.pointreading.PointReadingChapter;
import com.dbj.classpal.books.service.service.pointreading.IPointReadingChapterService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 点读书章节 服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
@Service
public class PointReadingChapterServiceImpl implements IPointReadingChapterService {

    @Resource
    private IPointReadingChapterBiz pointReadingPageBiz;

    @Override
    public Page<PointReadingChapterDTO> pagePage(PageInfo<PointReadingChapterQueryBO> pageInfo) throws BusinessException {
        Page<PointReadingChapter> page = new Page<>(pageInfo.getPageNum(), pageInfo.getPageSize());
        return pointReadingPageBiz.pagePage(page, pageInfo.getData());
    }

    @Override
    public PointReadingChapterDTO detail(Integer id) throws BusinessException {
        return pointReadingPageBiz.detail(id);
    }

    @Override
    public Integer save(PointReadingChapterSaveBO saveBO) throws BusinessException {
        return pointReadingPageBiz.savePage(saveBO);
    }

    @Override
    public Boolean update(PointReadingChapterUpdateBO updateBO) throws BusinessException {
        return pointReadingPageBiz.updatePage(updateBO);
    }

    @Override
    public Boolean delete(Integer id) throws BusinessException {
        return pointReadingPageBiz.deletePage(id);
    }

    @Override
    public Boolean deleteBatch(List<Integer> ids) throws BusinessException {
        return pointReadingPageBiz.deleteBatch(ids);
    }

    @Override
    public List<PointReadingChapterDTO> getPagesByMenu(Integer menuId) throws BusinessException {
        return pointReadingPageBiz.getPagesByMenu(menuId);
    }

    @Override
    public List<PointReadingChapterDTO> getPagesByBook(Integer bookId) throws BusinessException {
        return pointReadingPageBiz.getPagesByBook(bookId);
    }

    @Override
    public Boolean updateSort(Integer id, Integer sortNum) throws BusinessException {
        return pointReadingPageBiz.updateSort(id, sortNum);
    }

    @Override
    public Boolean enable(Integer id) throws BusinessException {
        return pointReadingPageBiz.enable(id);
    }

    @Override
    public Boolean disable(Integer id) throws BusinessException {
        return pointReadingPageBiz.disable(id);
    }

    @Override
    public Boolean enableBatch(List<Integer> ids) throws BusinessException {
        return pointReadingPageBiz.enableBatch(ids);
    }

    @Override
    public Boolean disableBatch(List<Integer> ids) throws BusinessException {
        return pointReadingPageBiz.disableBatch(ids);
    }
}
