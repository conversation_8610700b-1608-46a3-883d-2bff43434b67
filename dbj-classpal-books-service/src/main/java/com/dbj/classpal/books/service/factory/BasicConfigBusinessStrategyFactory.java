package com.dbj.classpal.books.service.factory;

import com.aliyun.core.utils.StringUtils;
import com.dbj.classpal.books.service.strategy.basicConfig.common.IBasicConfigCommonBusinessStrategy;
import com.dbj.classpal.framework.utils.util.SpringUtils;
import org.springframework.stereotype.Component;

@Component
public class BasicConfigBusinessStrategyFactory {

    public IBasicConfigCommonBusinessStrategy getStrategy(String handeler) {
        if(StringUtils.isEmpty(handeler)) {
            return null;
        }
        return SpringUtils.getBean(handeler);
    }
}
