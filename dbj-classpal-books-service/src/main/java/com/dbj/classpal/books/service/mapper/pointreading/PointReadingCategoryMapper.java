package com.dbj.classpal.books.service.mapper.pointreading;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingCategoryQueryBO;
import com.dbj.classpal.books.service.entity.pointreading.PointReadingCategory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 点读书分类表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Mapper
public interface PointReadingCategoryMapper extends BaseMapper<PointReadingCategory> {

    /**
     * 分页查询点读书分类
     *
     * @param page 分页参数
     * @param query 查询条件
     * @return 分页结果
     */
    Page<PointReadingCategory> selectPage(@Param("page") Page<PointReadingCategory> page, @Param("query") PointReadingCategoryQueryBO query);

    /**
     * 查询所有分类（用于构建树形结构）
     *
     * @param query 查询条件
     * @return 分类列表
     */
    List<PointReadingCategory> selectAllForTree(@Param("query") PointReadingCategoryQueryBO query);

    /**
     * 查询子分类数量
     *
     * @param parentId 父级分类ID
     * @return 子分类数量
     */
    int countChildren(@Param("parentId") Integer parentId);
}
