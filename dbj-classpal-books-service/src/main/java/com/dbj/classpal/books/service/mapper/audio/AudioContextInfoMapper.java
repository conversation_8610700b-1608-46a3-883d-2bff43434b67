package com.dbj.classpal.books.service.mapper.audio;

import com.dbj.classpal.books.common.dto.audio.AudioHintRefNumDTO;
import com.dbj.classpal.books.service.entity.audio.AudioContextInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 音频文本详情 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
public interface AudioContextInfoMapper extends BaseMapper<AudioContextInfo> {

    List<AudioHintRefNumDTO> statRefNum(@Param("audioHintMusicIds") List<Integer> audioHintMusicIds);
}
