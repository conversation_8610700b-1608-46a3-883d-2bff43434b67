package com.dbj.classpal.books.service.mapper.pointreading;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingChapterQueryBO;
import com.dbj.classpal.books.service.entity.pointreading.PointReadingChapter;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 点读书章节 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Mapper
public interface PointReadingChapterMapper extends BaseMapper<PointReadingChapter> {

    /**
     * 分页查询点读书章节
     *
     * @param page 分页参数
     * @param query 查询条件
     * @return 分页结果
     */
    Page<PointReadingChapter> selectPage(Page<PointReadingChapter> page, @Param("query") PointReadingChapterQueryBO query);

    /**
     * 查询目录下的章节列表
     *
     * @param menuId 目录ID
     * @return 章节列表
     */
    List<PointReadingChapter> selectByMenuId(@Param("menuId") Integer menuId);

    /**
     * 查询目录下的章节数量
     *
     * @param menuId 目录ID
     * @return 章节数量
     */
    int countByMenu(@Param("menuId") Integer menuId);
}
