package com.dbj.classpal.books.service.mapper.books;

import com.dbj.classpal.books.client.dto.books.BooksScanCountDTO;
import com.dbj.classpal.books.service.entity.books.BooksScanInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 图书配置-图书内容分类 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
public interface BooksScanInfoMapper extends BaseMapper<BooksScanInfo> {



    /**
     * <AUTHOR>
     * @Description  统计图书扫码次数
     * @Date 2025/4/25 9:40
     * @param bookIds 图书id集合
     * @return BooksScanCountDTO
     **/
    List<BooksScanCountDTO> bookScanCount(@Param("bookIds") List<Integer> bookIds);
}
