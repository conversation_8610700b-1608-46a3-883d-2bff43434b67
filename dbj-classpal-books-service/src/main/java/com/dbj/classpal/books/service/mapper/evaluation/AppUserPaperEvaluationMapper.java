package com.dbj.classpal.books.service.mapper.evaluation;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.common.bo.evaluation.AdminUserPaperEvaluationQueryBO;
import com.dbj.classpal.books.service.entity.evaluation.AppUserPaperEvaluation;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppEvaluationMapper
 * Date:     2025-05-16 14:57:09
 * Description: 表名： ,描述： 表
 */
public interface AppUserPaperEvaluationMapper extends BaseMapper<AppUserPaperEvaluation> {

    /**
     * 分页查询评测模板列表
     * @param page
     * @param bo
     * @return
     */
    Page<AppUserPaperEvaluation> pageInfo(Page page, @Param("bo") AdminUserPaperEvaluationQueryBO bo);
}
