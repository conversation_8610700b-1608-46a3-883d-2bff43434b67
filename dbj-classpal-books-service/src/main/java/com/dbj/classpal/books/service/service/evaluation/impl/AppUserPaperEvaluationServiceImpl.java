package com.dbj.classpal.books.service.service.evaluation.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.app.client.bo.user.UserIdApiBO;
import com.dbj.classpal.app.client.dto.user.CurrentUserApiDTO;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.dto.evaluation.app.AppUserPaperEvaluationAiParamsApiDTO;
import com.dbj.classpal.books.client.dto.evaluation.app.AppUserPaperEvaluationAnalysisAiParamsApiDTO;
import com.dbj.classpal.books.common.bo.common.CommonIdBO;
import com.dbj.classpal.books.common.bo.evaluation.AdminUserPaperEvaluationQueryBO;
import com.dbj.classpal.books.common.bo.evaluation.app.*;
import com.dbj.classpal.books.common.dto.evaluation.AdminUserPaperEvaluationQueryPageDTO;
import com.dbj.classpal.books.common.dto.evaluation.app.AppEvaluationReportQueryDTO;
import com.dbj.classpal.books.common.dto.evaluation.app.AppUserPaperEvaluationAnalysisQueryDTO;
import com.dbj.classpal.books.common.dto.evaluation.app.AppUserPaperEvaluationSaveDTO;
import com.dbj.classpal.books.service.biz.evaluation.IAppEvaluationBiz;
import com.dbj.classpal.books.service.biz.evaluation.IAppEvaluationNodeBiz;
import com.dbj.classpal.books.service.biz.evaluation.IAppUserPaperEvaluationAnalysisBiz;
import com.dbj.classpal.books.service.biz.evaluation.IAppUserPaperEvaluationBiz;
import com.dbj.classpal.books.service.entity.evaluation.AppEvaluation;
import com.dbj.classpal.books.service.entity.evaluation.AppEvaluationNode;
import com.dbj.classpal.books.service.entity.evaluation.AppUserPaperEvaluation;
import com.dbj.classpal.books.service.entity.evaluation.AppUserPaperEvaluationAnalysis;
import com.dbj.classpal.books.service.remote.sys.SysAppUserRemoteService;
import com.dbj.classpal.books.service.service.evaluation.IAppUserPaperEvaluationService;
import com.dbj.classpal.books.service.service.paper.IAppUserPaperService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;
import static com.dbj.classpal.books.common.enums.evaluation.PaperEnvaluationStatusEnum.EVALUATION_GENERATED_YES;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppUserPaperEvaluationServiceImpl
 * Date:     2025-05-16 15:00:23
 * Description: 表名： ,描述： 表
 */
@Service
public class AppUserPaperEvaluationServiceImpl implements IAppUserPaperEvaluationService {

    @Resource
    private IAppUserPaperEvaluationBiz appUserPaperEvaluationBiz;
    @Resource
    private SysAppUserRemoteService appUserRemoteService;
    @Resource
    private IAppUserPaperEvaluationAnalysisBiz appUserPaperEvaluationAnalysisBiz;
    @Resource
    private IAppEvaluationBiz appEvaluationBiz;
    @Resource
    private IAppEvaluationNodeBiz appEvaluationNodeBiz;
    @Resource
    private IAppUserPaperService appUserPaperService;


    @Override
    public Page<AdminUserPaperEvaluationQueryPageDTO> pageInfo(PageInfo<AdminUserPaperEvaluationQueryBO> pageRequest) throws BusinessException {
        AdminUserPaperEvaluationQueryBO condition = pageRequest.getData();
        // 1. 查询分页数据
        Page<AppUserPaperEvaluation> page = appUserPaperEvaluationBiz.pageInfo(pageRequest);
        List<AppUserPaperEvaluation> userPaperEvaluations = page.getRecords();

        List<Integer> userIdList = userPaperEvaluations.stream().map(AppUserPaperEvaluation::getAppUserId).collect(Collectors.toList());
        Map<Integer, CurrentUserApiDTO> userMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(userIdList)) {
            List<CurrentUserApiDTO> currentUserApiDTOList = appUserRemoteService.listById(userIdList);
            if (CollectionUtils.isNotEmpty(currentUserApiDTOList)) {
                Stream<CurrentUserApiDTO> stream = currentUserApiDTOList.stream();
                if (StringUtils.isNotEmpty(condition.getUid())) {
                    stream = stream.filter(d -> d.getUid() != null && d.getUid().equals(condition.getUid()));
                }
                userMap = stream.collect(Collectors.toMap(CurrentUserApiDTO::getId, d -> d));
            }
        }
        Map<Integer, CurrentUserApiDTO> finalUserMap = userMap;
        List<AdminUserPaperEvaluationQueryPageDTO> adminUserPaperEvaluationQueryPageDTOS = userPaperEvaluations.stream().filter(d -> finalUserMap.containsKey(d.getAppUserId())).map(d -> {
            AdminUserPaperEvaluationQueryPageDTO adminUserPaperEvaluationQueryPageDTO = new AdminUserPaperEvaluationQueryPageDTO();
            adminUserPaperEvaluationQueryPageDTO.setId(d.getId());
            adminUserPaperEvaluationQueryPageDTO.setUserId(d.getAppUserId());
            adminUserPaperEvaluationQueryPageDTO.setGeneratedTime(d.getGeneratedTime());
            adminUserPaperEvaluationQueryPageDTO.setGradeName(d.getGradeName());
            adminUserPaperEvaluationQueryPageDTO.setIpCity(d.getRegion());
            if (finalUserMap.containsKey(d.getAppUserId())) {
                adminUserPaperEvaluationQueryPageDTO.setUserName(finalUserMap.get(d.getAppUserId()).getNickname());
                adminUserPaperEvaluationQueryPageDTO.setUid(finalUserMap.get(d.getAppUserId()).getUid());
            }
            return adminUserPaperEvaluationQueryPageDTO;
        }).collect(Collectors.toList());
        Page<AdminUserPaperEvaluationQueryPageDTO> appUserPaperEvaluationQueryPageDTOPage = new Page<>();
        appUserPaperEvaluationQueryPageDTOPage.setTotal(page.getTotal());
        appUserPaperEvaluationQueryPageDTOPage.setSize(page.getSize());
        appUserPaperEvaluationQueryPageDTOPage.setCurrent(page.getCurrent());
        appUserPaperEvaluationQueryPageDTOPage.setPages(page.getPages());
        appUserPaperEvaluationQueryPageDTOPage.setRecords(adminUserPaperEvaluationQueryPageDTOS);
        return appUserPaperEvaluationQueryPageDTOPage;
    }

    @Override
    public AppUserPaperEvaluationSaveDTO saveEvaluationReport(AppUserPaperEvaluationSaveBO bo) throws BusinessException {
        return appUserPaperEvaluationBiz.saveEvaluationReport(bo);
    }

    @Override
    public AppUserPaperEvaluationAiParamsApiDTO getAiParams(CommonIdApiBO bo) throws BusinessException {
        AppUserPaperEvaluation userPaperEvaluation = appUserPaperEvaluationBiz.getById(bo.getId());
        if (ObjectUtils.isEmpty(userPaperEvaluation)){
            throw new BusinessException(APP_EVALUATION_ANALYSIS_PARAMS_ERROR_CODE,APP_EVALUATION_ANALYSIS_PARAMS_ERROR_MSG);
        }
        AppEvaluation evaluation = appEvaluationBiz.getById(userPaperEvaluation.getAppEvaluationId());
        if (ObjectUtils.isEmpty(evaluation)){
            throw new BusinessException(APP_EVALUATION_NOT_EXIST_CODE,APP_EVALUATION_NOT_EXIST_MSG);
        }
        AppUserPaperEvaluationAiParamsApiDTO aiParamsApiDTO = new AppUserPaperEvaluationAiParamsApiDTO();
        aiParamsApiDTO.setTitle(evaluation.getEvaluationName());
        List<AppUserPaperEvaluationAnalysis> analysisList = appUserPaperEvaluationAnalysisBiz.lambdaQuery()
                .eq(AppUserPaperEvaluationAnalysis::getAppUserPaperEvaluationId, userPaperEvaluation.getId())
                .list();
        List<AppUserPaperEvaluationAnalysisAiParamsApiDTO> paramsApiDTOList = analysisList.stream()
                .map(d -> {
                    AppUserPaperEvaluationAnalysisAiParamsApiDTO analysisAiParamsApiDTO = new AppUserPaperEvaluationAnalysisAiParamsApiDTO();
                    BeanUtil.copyProperties(d,analysisAiParamsApiDTO);
                    analysisAiParamsApiDTO.setId(d.getId());
                    analysisAiParamsApiDTO.setName(d.getAppEvaluationNodeName());
                    analysisAiParamsApiDTO.setCorrectCount(d.getRightCount());
                    analysisAiParamsApiDTO.setIncorrectCount(d.getErrorCount());
                    return analysisAiParamsApiDTO;
                }).collect(Collectors.toList());
        aiParamsApiDTO.setAnalysisAiParamsApiDTOList(paramsApiDTOList);
        return aiParamsApiDTO;
    }

    @Override
    public Boolean checkSubmitEvaluationReport(CommonIdApiBO bo) throws BusinessException {
        AppUserPaperEvaluation userPaperEvaluation = appUserPaperEvaluationBiz.getById(bo.getId());
        if (ObjectUtils.isEmpty(userPaperEvaluation)) {
            throw new BusinessException(APP_EVALUATION_REPORT_NOT_EXIST_CODE,APP_EVALUATION_REPORT_NOT_EXIST_MSG);
        }
        //查询评测表记录
        AppEvaluation evaluation = appEvaluationBiz.getById(userPaperEvaluation.getAppEvaluationId());
        if (ObjectUtils.isEmpty(evaluation)) {
            throw new BusinessException(APP_EVALUATION_NOT_EXIST_CODE,APP_EVALUATION_NOT_EXIST_MSG);
        }
        //查询评测表下有多少评测项
        List<AppEvaluationNode> nodeList = appEvaluationNodeBiz.lambdaQuery().eq(AppEvaluationNode::getAppEvaluationId, evaluation.getId()).list();
        //获取nodeIdSet
        Set<Integer> nodeIdSet = nodeList.stream().map(AppEvaluationNode::getId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(nodeList)) {
            throw new BusinessException(APP_EVALUATION_NODE_SORT_FAIL_CODE,APP_EVALUATION_NODE_SORT_FAIL_MSG);
        }
        //查询多少评测项提交了试卷
        List<AppUserPaperEvaluationAnalysis> analysisList = appUserPaperEvaluationAnalysisBiz.lambdaQuery().in(AppUserPaperEvaluationAnalysis::getAppEvaluationNodeId, nodeIdSet).eq(AppUserPaperEvaluationAnalysis::getAppUserPaperEvaluationId, bo.getId()).list();
        //如果提交的试卷数小于要考的评测项数,则提示需要所有评测项考完后才可以查看评测报告
        if (analysisList.size() < nodeList.size()) {
            throw new BusinessException(APP_EVALUATION_GENERATE_REPORT_FAIL_CODE,APP_EVALUATION_GENERATE_REPORT_FAIL_MSG);
        }
        return true;
    }

    public AppEvaluationReportQueryDTO queryEvaluationReport(CommonIdBO bo) throws BusinessException {
        AppUserPaperEvaluation userPaperEvaluation = appUserPaperEvaluationBiz.getById(bo.getId());
        AppEvaluationReportQueryDTO reportQueryDTO = new AppEvaluationReportQueryDTO();
        if (ObjectUtils.isEmpty(userPaperEvaluation)) {
            throw new BusinessException(APP_EVALUATION_REPORT_NOT_EXIST_CODE,APP_EVALUATION_REPORT_NOT_EXIST_MSG);
        }
        UserIdApiBO userIdApiBO = new UserIdApiBO();
        userIdApiBO.setUserId(userPaperEvaluation.getAppUserId());
        CurrentUserApiDTO currentUser = appUserRemoteService.getCurrentUser(userIdApiBO);
        if (ObjectUtils.isEmpty(currentUser)) {
            throw new BusinessException(OPENAPI_GET_USER_INFO_FAIL_CODE,OPENAPI_GET_USER_INFO_FAIL_MSG);
        }
        reportQueryDTO.setId(userPaperEvaluation.getId());
        reportQueryDTO.setUserId(userPaperEvaluation.getAppUserId());
        reportQueryDTO.setUid(currentUser.getUid());
        reportQueryDTO.setUserName(currentUser.getNickname());
        reportQueryDTO.setGradeName(userPaperEvaluation.getGradeName());
        reportQueryDTO.setAvatar(currentUser.getAvatar());
        reportQueryDTO.setEvaluation(userPaperEvaluation.getEvaluation());
        reportQueryDTO.setGeneratedTime(LocalDateTime.now());
        List<AppUserPaperEvaluationAnalysisQueryDTO> analysisQueryDTOList = new ArrayList<>();
        List<AppUserPaperEvaluationAnalysis> appUserPaperEvaluationAnalysisList = appUserPaperEvaluationAnalysisBiz.lambdaQuery().eq(AppUserPaperEvaluationAnalysis::getAppUserPaperEvaluationId, bo.getId()).list();
        if (CollectionUtils.isNotEmpty(appUserPaperEvaluationAnalysisList)) {
            analysisQueryDTOList = BeanUtil.copyToList(appUserPaperEvaluationAnalysisList, AppUserPaperEvaluationAnalysisQueryDTO.class);
        }
        reportQueryDTO.setAnalysisQueryDTOList(analysisQueryDTOList);
        return reportQueryDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AppEvaluationReportQueryDTO genEvaluationReport(AppUserPaperEvaluationGenerateBO bo) throws BusinessException {
        AppUserPaperEvaluation userPaperEvaluation = appUserPaperEvaluationBiz.getById(bo.getId());
        if (ObjectUtils.isEmpty(userPaperEvaluation)) {
            throw new BusinessException(APP_EVALUATION_REPORT_NOT_EXIST_CODE,APP_EVALUATION_REPORT_NOT_EXIST_MSG);
        }
        Integer isGenerated = userPaperEvaluation.getIsGenerated();
        if (!(isGenerated.equals(EVALUATION_GENERATED_YES.getCode())) && StringUtils.isEmpty(bo.getData())){
            throw new BusinessException("生成评测报告失败,ai返回结果不能为空");
        }
        //查询评测表记录
        AppEvaluation evaluation = appEvaluationBiz.getById(userPaperEvaluation.getAppEvaluationId());
        if (ObjectUtils.isEmpty(evaluation)) {
            throw new BusinessException(APP_EVALUATION_NOT_EXIST_CODE,APP_EVALUATION_NOT_EXIST_MSG);
        }
        //查询评测表下有多少评测项
        List<AppEvaluationNode> nodeList = appEvaluationNodeBiz.lambdaQuery().eq(AppEvaluationNode::getAppEvaluationId, evaluation.getId()).list();
        //评测项map
        Map<Integer,AppEvaluationNode>nodeMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(nodeList)) {
            nodeMap = nodeList.stream().collect(Collectors.toMap(AppEvaluationNode::getId, d -> d));
        }
        CommonIdBO commonIdBO = new CommonIdBO();
        commonIdBO.setId(bo.getId());
        UserIdApiBO userIdApiBO = new UserIdApiBO();
        userIdApiBO.setUserId(userPaperEvaluation.getAppUserId());
        CurrentUserApiDTO currentUser = appUserRemoteService.getCurrentUser(userIdApiBO);
        if (ObjectUtils.isEmpty(currentUser)) {
            throw new BusinessException(OPENAPI_GET_USER_INFO_FAIL_CODE,OPENAPI_GET_USER_INFO_FAIL_MSG);
        }
        LocalDateTime now = LocalDateTime.now();
        AppEvaluationReportQueryDTO reportQueryDTO = new AppEvaluationReportQueryDTO();
        reportQueryDTO.setId(userPaperEvaluation.getId());
        reportQueryDTO.setUserId(userPaperEvaluation.getAppUserId());
        reportQueryDTO.setUid(currentUser.getUid());
        reportQueryDTO.setUserName(currentUser.getNickname());
        reportQueryDTO.setGradeName(userPaperEvaluation.getGradeName());
        reportQueryDTO.setAvatar(currentUser.getAvatar());
        reportQueryDTO.setEvaluationName(evaluation.getEvaluationName());
        reportQueryDTO.setEvaluation(userPaperEvaluation.getEvaluation());
        reportQueryDTO.setGeneratedTime(now);
        List<AppUserPaperEvaluationAnalysisQueryDTO> analysisQueryDTOList = new ArrayList<>();
        //如果查询的评测结果是已经生成过的了,则直接查询保存的评测报告
        if (isGenerated.equals(EVALUATION_GENERATED_YES.getCode())){
            List<AppUserPaperEvaluationAnalysis> appUserPaperEvaluationAnalysisList = appUserPaperEvaluationAnalysisBiz.lambdaQuery().eq(AppUserPaperEvaluationAnalysis::getAppUserPaperEvaluationId, bo.getId()).list();
            if (CollectionUtils.isNotEmpty(appUserPaperEvaluationAnalysisList)) {
                analysisQueryDTOList = BeanUtil.copyToList(appUserPaperEvaluationAnalysisList, AppUserPaperEvaluationAnalysisQueryDTO.class);
            }
        }else{
            Gson gson = new Gson();
            JsonObject jsonObject = JsonParser.parseString(bo.getData()).getAsJsonObject();
            AppUserPaperEvaluationGenerateGsonTransBO generateBO = gson.fromJson(jsonObject, AppUserPaperEvaluationGenerateGsonTransBO.class);
            generateBO.setId(bo.getId());
            List<AppUserPaperEvaluationAnalysisGenerateBO> analysisGenerateApiBOList = generateBO.getEvaluationNodes().stream().map(d ->{
                AppUserPaperEvaluationAnalysisGenerateBO analysisGenerateBO = new AppUserPaperEvaluationAnalysisGenerateBO();
                analysisGenerateBO.setId(d.getId());
                analysisGenerateBO.setAnalysis(d.getAnalysis());
                analysisGenerateBO.setEvaluationName(d.getEvaluationName());
                analysisGenerateBO.setScore(Double.valueOf(d.getScore()));
                analysisGenerateBO.setAbilityScore(Double.valueOf(d.getAbilityScore()));
                analysisGenerateBO.setSuggest(d.getSuggest());
                analysisGenerateBO.setDescription(d.getDescription());
                return analysisGenerateBO;
            }).collect(Collectors.toList());
            Map<Integer,AppUserPaperEvaluationAnalysisGenerateBO> analysisGenerateApiBOMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(analysisGenerateApiBOList)) {
                analysisGenerateApiBOMap = analysisGenerateApiBOList.stream().collect(Collectors.toMap(AppUserPaperEvaluationAnalysisGenerateBO::getId,d -> d));
            }
            Set<Integer> analysisIdSet = generateBO.getEvaluationNodes().stream().map(AppUserPaperEvaluationAnalysisGenerateGsonTransBO::getId).collect(Collectors.toSet());
            //查询多少评测项提交了试卷
            List<AppUserPaperEvaluationAnalysis> analysisList = appUserPaperEvaluationAnalysisBiz.lambdaQuery().in(AppUserPaperEvaluationAnalysis::getId, analysisIdSet).eq(AppUserPaperEvaluationAnalysis::getAppUserPaperEvaluationId, bo.getId()).list();
            for (AppUserPaperEvaluationAnalysis analysis : analysisList) {
                Integer analysisId = analysis.getId();
                //ai分析并生成报告保存并返回数据
                if (analysisGenerateApiBOMap.containsKey(analysisId)) {
                    analysis.setScore(analysisGenerateApiBOMap.get(analysisId).getScore());
                    analysis.setAnalysis(analysisGenerateApiBOMap.get(analysisId).getAnalysis());
                    analysis.setSuggest(analysisGenerateApiBOMap.get(analysisId).getSuggest());
                    analysis.setAbilityAnalysis(analysisGenerateApiBOMap.get(analysisId).getDescription());
                    analysis.setAbilityScore(analysisGenerateApiBOMap.get(analysisId).getAbilityScore());
                }
            }
            boolean updateAnalysisFlag = appUserPaperEvaluationAnalysisBiz.updateBatchById(analysisList);
            if (!updateAnalysisFlag){
                throw new BusinessException(APP_EVALUATION_ANALYSIS_SAVE_FAIL_CODE,APP_EVALUATION_ANALYSIS_SAVE_FAIL_MSG);
            }
            boolean generateRes = appUserPaperEvaluationBiz.lambdaUpdate().eq(AppUserPaperEvaluation::getId, userPaperEvaluation.getId()).set(AppUserPaperEvaluation::getEvaluation,generateBO.getEvaluation()).set(AppUserPaperEvaluation::getIsGenerated, EVALUATION_GENERATED_YES.getCode()).set(AppUserPaperEvaluation::getGeneratedTime,now).update();
            if (!generateRes){
                throw new BusinessException(APP_EVALUATION_GENERATE_FAIL_CODE,APP_EVALUATION_GENERATE_FAIL_MSG);
            }
            analysisQueryDTOList = BeanUtil.copyToList(analysisList, AppUserPaperEvaluationAnalysisQueryDTO.class);
            reportQueryDTO.setEvaluation(generateBO.getEvaluation());
        }
        Map<Integer, AppEvaluationNode> finalNodeMap = nodeMap;
        analysisQueryDTOList = analysisQueryDTOList.stream().peek(d -> {
            if (finalNodeMap.containsKey(d.getAppEvaluationNodeId())){
                d.setOrderNum(finalNodeMap.get(d.getAppEvaluationNodeId()).getAppEvaluationOrder());
            }else{
                d.setOrderNum(999999);
            }
        }).sorted(Comparator.comparingInt(AppUserPaperEvaluationAnalysisQueryDTO::getOrderNum)).collect(Collectors.toList());
        reportQueryDTO.setAnalysisQueryDTOList(analysisQueryDTOList);
        return reportQueryDTO;
    }
}
