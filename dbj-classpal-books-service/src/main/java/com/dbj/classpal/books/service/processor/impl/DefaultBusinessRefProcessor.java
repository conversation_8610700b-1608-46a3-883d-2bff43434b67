package com.dbj.classpal.books.service.processor.impl;

import com.dbj.classpal.books.client.enums.BusinessTypeEnum;
import com.dbj.classpal.books.common.dto.question.QuestionCategoryRefDTO;
import com.dbj.classpal.books.service.entity.question.QuestionCategoryBusinessRef;
import com.dbj.classpal.books.service.processor.BusinessRefProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 默认业务引用处理器
 * 用于处理未知或暂不支持的业务类型
 */
@Slf4j
@Component
public class DefaultBusinessRefProcessor implements BusinessRefProcessor {

    @Override
    public BusinessTypeEnum getSupportedBusinessType() {
        return null;
    }

    @Override
    public void processBusinessInfo(QuestionCategoryRefDTO dto, QuestionCategoryBusinessRef businessRef) throws Exception {
        BusinessTypeEnum typeEnum = BusinessTypeEnum.getByCode(businessRef.getBusinessType());
        String typeName = typeEnum != null ? typeEnum.getName() : "未知类型";
        
        log.warn("使用默认处理器处理业务引用, businessType: {}, businessId: {}", 
            businessRef.getBusinessType(), businessRef.getBusinessId());
        
        dto.setBusinessName(String.format("%s(ID:%d)", typeName, businessRef.getBusinessId()));
    }

    @Override
    public int getPriority() {
        return Integer.MAX_VALUE;
    }
}
