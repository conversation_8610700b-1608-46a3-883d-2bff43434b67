package com.dbj.classpal.books.service.mapper.pinyin;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.pinyin.PinyinPageBO;
import com.dbj.classpal.books.client.dto.pinyin.PinyinDTO;
import com.dbj.classpal.books.service.entity.pinyin.Pinyin;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 拼音信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
public interface PinyinMapper extends BaseMapper<Pinyin> {

    /**
     * 获取拼音信息分页列表
     *
     * @param page 分页对象
     * @param bo   拼音信息分页查询参数
     * @return 拼音信息分页列表
     */
    Page<PinyinDTO> getPinyinPage(Page<Object> page, @Param("bo") PinyinPageBO bo);
}
