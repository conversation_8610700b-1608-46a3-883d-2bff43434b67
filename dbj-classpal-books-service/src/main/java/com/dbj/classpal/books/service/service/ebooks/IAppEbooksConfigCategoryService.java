package com.dbj.classpal.books.service.service.ebooks;

import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigCategoryQueryApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigCategorySaveApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigCategoryUpdateApiBO;
import com.dbj.classpal.books.client.dto.ebooks.AppEbooksConfigCategoryQueryApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface IAppEbooksConfigCategoryService {

    /**
     * 查询所有图书分类
     * @param bo
     * @return
     */
    List<AppEbooksConfigCategoryQueryApiDTO> getAllCategory(AppEbooksConfigCategoryQueryApiBO bo) throws BusinessException;


    /**
     * 新增图书分类
     * @param bo
     * @return
     */
    Boolean saveCategory(AppEbooksConfigCategorySaveApiBO bo) throws BusinessException;


    /**
     * 编辑图书分类
     * @param bo
     * @return
     */
    Boolean updateCategory(AppEbooksConfigCategoryUpdateApiBO bo) throws BusinessException;


    /**
     * 删除图书分类
     * @param bo
     * @return
     */
    Boolean deleteCategory(CommonIdsApiBO bo) throws BusinessException;
}
