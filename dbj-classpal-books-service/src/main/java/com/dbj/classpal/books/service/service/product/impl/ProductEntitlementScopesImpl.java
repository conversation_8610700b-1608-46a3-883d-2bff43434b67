package com.dbj.classpal.books.service.service.product.impl;

import cn.hutool.core.bean.BeanUtil;
import com.dbj.classpal.books.client.bo.books.BooksInfoSaleConfigBO.RightScopeBO;
import com.dbj.classpal.books.client.dto.books.BooksInfoRightScopeDTO;
import com.dbj.classpal.books.service.biz.product.IProductEntitlementScopesBiz;
import com.dbj.classpal.books.service.entity.product.ProductEntitlementScopes;
import com.dbj.classpal.books.service.service.product.IProductEntitlementScopesService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.utils.Assert;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/7/31 17:21
 */
@Service
@RequiredArgsConstructor
public class ProductEntitlementScopesImpl implements IProductEntitlementScopesService {

    private final IProductEntitlementScopesBiz productEntitlementScopesBiz;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(Integer configId, List<RightScopeBO> rightScopeList) throws BusinessException {
        productEntitlementScopesBiz.lambdaUpdate()
                .eq(ProductEntitlementScopes::getConfigId, configId)
                .remove();
        List<ProductEntitlementScopes> scopesList = rightScopeList.stream()
                .map(e -> BeanUtil.copyProperties(e, ProductEntitlementScopes.class)
                        .setConfigId(configId))
                .toList();
        Assert.isTrue(productEntitlementScopesBiz.saveBatch(scopesList));
    }

    @Override
    public List<BooksInfoRightScopeDTO> getScopeList(Integer configId) {
        List<ProductEntitlementScopes> scopesList = productEntitlementScopesBiz.lambdaQuery()
                .eq(ProductEntitlementScopes::getConfigId, configId)
                .list();
        return BeanUtil.copyToList(scopesList, BooksInfoRightScopeDTO.class);
    }
}
