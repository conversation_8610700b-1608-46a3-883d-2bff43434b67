-- 题库业务设置统一管理表数据迁移回滚脚本
-- 用于回滚 question_category_business_settings 表的数据迁移

-- =====================================================
-- 回滚前的数据备份验证
-- 执行回滚前请确认以下查询结果
-- =====================================================

-- 查看当前统一管理表中的数据分布
SELECT 
    business_type,
    CASE 
        WHEN business_type = 1 THEN '学习模块答题'
        WHEN business_type = 2 THEN '图书书内码答题'
        WHEN business_type = 3 THEN '点读答题'
        ELSE '未知类型'
    END as business_type_desc,
    COUNT(*) as record_count
FROM question_category_business_settings 
WHERE is_deleted = 0
GROUP BY business_type;

-- =====================================================
-- 第一步：回滚题库分类业务关联表的业务类型更新
-- 将 business_type 从 2 改回 3（如果之前有这样的更新）
-- =====================================================

-- 如果在迁移过程中将 business_type = 3 更新为了 2，现在改回来
-- 注意：这里需要根据实际的迁移逻辑进行调整
UPDATE question_category_business_ref 
SET business_type = 3 
WHERE business_type = 2 
    AND business_id IN (
        SELECT business_id 
        FROM question_category_business_settings 
        WHERE business_type = 2 AND is_deleted = 0
    )
    AND is_deleted = 0;

-- =====================================================
-- 第二步：删除从图书书内码表迁移的数据
-- business_type = 2 的记录
-- =====================================================

-- 软删除图书书内码答题设置的迁移数据
UPDATE question_category_business_settings 
SET is_deleted = 1,
    update_time = NOW(),
    update_by = 'ROLLBACK_SCRIPT'
WHERE business_type = 2 AND is_deleted = 0;

-- 或者直接物理删除（谨慎使用）
-- DELETE FROM question_category_business_settings WHERE business_type = 2;

-- =====================================================
-- 第三步：删除从学习模块表迁移的数据
-- business_type = 1 的记录
-- =====================================================

-- 软删除学习模块答题设置的迁移数据
UPDATE question_category_business_settings 
SET is_deleted = 1,
    update_time = NOW(),
    update_by = 'ROLLBACK_SCRIPT'
WHERE business_type = 1 AND is_deleted = 0;

-- 或者直接物理删除（谨慎使用）
-- DELETE FROM question_category_business_settings WHERE business_type = 1;

-- =====================================================
-- 第四步：验证回滚结果
-- =====================================================

-- 检查统一管理表是否已清空迁移的数据
SELECT 
    business_type,
    COUNT(*) as remaining_count
FROM question_category_business_settings 
WHERE is_deleted = 0
GROUP BY business_type;

-- 检查原表数据是否完整
SELECT 
    '学习模块原表' as table_name,
    COUNT(*) as record_count
FROM app_study_module_question_ext 
WHERE is_deleted = 0;

SELECT 
    '图书书内码原表' as table_name,
    COUNT(*) as record_count
FROM books_rank_in_codes_contents_question 
WHERE is_deleted = 0;

-- 检查题库分类业务关联表的状态
SELECT 
    business_type,
    COUNT(*) as record_count
FROM question_category_business_ref 
WHERE is_deleted = 0
GROUP BY business_type;

-- =====================================================
-- 第五步：完全清理统一管理表（可选）
-- 如果需要完全清理新表的数据，取消以下注释
-- =====================================================

-- 清空统一管理表的所有数据（物理删除）
-- TRUNCATE TABLE question_category_business_settings;

-- 或者软删除所有数据
-- UPDATE question_category_business_settings 
-- SET is_deleted = 1, 
--     update_time = NOW(),
--     update_by = 'ROLLBACK_SCRIPT'
-- WHERE is_deleted = 0;

-- =====================================================
-- 第六步：重置自增ID（可选）
-- 如果使用了物理删除并且需要重置自增ID
-- =====================================================

-- 重置自增ID到1
-- ALTER TABLE question_category_business_settings AUTO_INCREMENT = 1;

-- =====================================================
-- 回滚完成后的验证清单：
-- 1. 确认 question_category_business_settings 表中没有迁移的数据
-- 2. 确认原表 app_study_module_question_ext 数据完整
-- 3. 确认原表 books_rank_in_codes_contents_question 数据完整
-- 4. 确认 question_category_business_ref 表的 business_type 已恢复
-- 5. 如果有应用程序在使用新表，需要停止相关服务
-- =====================================================

-- 最终验证查询
SELECT 'ROLLBACK_VERIFICATION' as status,
       'question_category_business_settings' as table_name,
       COUNT(*) as active_records
FROM question_category_business_settings 
WHERE is_deleted = 0

UNION ALL

SELECT 'ROLLBACK_VERIFICATION' as status,
       'app_study_module_question_ext' as table_name,
       COUNT(*) as active_records
FROM app_study_module_question_ext 
WHERE is_deleted = 0

UNION ALL

SELECT 'ROLLBACK_VERIFICATION' as status,
       'books_rank_in_codes_contents_question' as table_name,
       COUNT(*) as active_records
FROM books_rank_in_codes_contents_question 
WHERE is_deleted = 0;
