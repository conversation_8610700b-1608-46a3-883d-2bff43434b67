# 常用模式和最佳实践

- 统一配置缓存管理最佳实践：使用ConfigCacheManager统一管理basic_config和app_ebooks_config_category缓存，通过智能增量更新机制（只在数据真正变化时写入缓存）避免并发覆盖问题，相比分布式锁方案更简单可靠且性能更好。RedissonRedisUtils不支持Hash操作和分布式锁，因此采用数据变更检测+条件写入的方式实现并发安全。
- PDF任务结果继承机制：当新增或更新eBook使用相同文件（sameFile=true）时，应该主动查询该文件MD5对应的最新成功任务结果，并将结果（封面URL、文件状态、错误信息等）继承到新的eBook中，避免因为没有businessId而无法获取任务结果的问题。
- PDF任务继承机制设计要点：1)继承时需要根据任务状态更新eBook状态和错误信息；2)多用户同时操作相同MD5文件时避免结果互相覆盖；3)资源复制不需要，因为资源查询基于MD5统一查询
