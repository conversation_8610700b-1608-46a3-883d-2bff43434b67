# 开发规范和规则

- 用户关注代码质量问题：大量使用e.printStackTrace()在生产环境中的影响，需要从C端商城、B端项目和Java语言层面进行全面分析
- 兑换服务设计规则：商品权益统一使用"goods"命名而非"product"；不需要订单表，订单由其他统一服务模块管理，兑换记录表只关联外部订单号；兑换码表需要精细化分表策略；兑换记录表与兑换码表是1对多关系
- 兑换服务设计优化：VIP服务SKU和商品权益配置表应该合并，对于兑换来说不区分商品类型，都是可兑换的权益，统一管理
- 兑换服务设计回滚：恢复VIP服务SKU和商品权益配置表分开设计；VIP或商品需要做快照；批次只针对兑换码生成逻辑，不参与兑换动作；需要增加兑换记录的商品明细表，采用快照模式
- 兑换服务表结构重新设计：兑换码表和批次表只做兑换码生成管理，记录基本信息；兑换记录以兑换码为维度作为主表；需要VIP SKU和商品配置作为兑换记录的明细表（商品快照模式）；兑换码绑定商品信息通过exchange_vip_sku和exchange_goods_config管理
- 兑换服务架构澄清：兑换码商品绑定表与exchange_goods_config、exchange_vip_sku存在冲突；兑换是针对某个批次的兑换码；exchange_code是批次的汇总表，不是单个兑换码记录
- ThreadLocal清理规则：当多个AOP切面都需要使用ThreadLocal时，应该只在最外层的切面（请求入口处）进行清理，避免内层切面过早清理导致其他切面无法获取数据。MQ监听器和定时任务切面都不应该清理ThreadLocal，应该由统一的请求处理切面负责清理。
