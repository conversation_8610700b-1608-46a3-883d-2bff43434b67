-- 为点读热点表添加填空文本内容字段和模式配置冗余字段
-- 执行时间：2025-01-01

-- 添加点读书ID字段
ALTER TABLE `point_reading_hotspot`
ADD COLUMN `book_id` INT NULL COMMENT '点读书ID'
AFTER `chapter_id`;

-- 添加填空文本内容字段
ALTER TABLE `point_reading_hotspot`
ADD COLUMN `fill_blank_content` TEXT NULL COMMENT '填空文本内容（富文本格式，当模式为填空时使用）'
AFTER `verify_text`;

-- 添加模式配置字典ID字段（冗余存储，用于历史数据保护）
ALTER TABLE `point_reading_hotspot`
ADD COLUMN `config_dict_id` INT NULL COMMENT '模式配置字典ID（冗余存储，用于历史数据保护）'
AFTER `fill_blank_content`;

-- 添加模式显示名称字段（冗余存储，用于历史数据保护）
ALTER TABLE `point_reading_hotspot`
ADD COLUMN `display_name` VARCHAR(100) NULL COMMENT '模式显示名称（冗余存储，用于历史数据保护）'
AFTER `config_dict_id`;

-- 验证字段是否添加成功
SELECT
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'point_reading_hotspot'
    AND COLUMN_NAME IN ('book_id', 'fill_blank_content', 'config_dict_id', 'display_name');

-- 创建TTS音频业务关联表
CREATE TABLE `audio_intro_business_ref` (
    `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `business_id` INT NOT NULL COMMENT '业务ID',
    `business_type` VARCHAR(50) NOT NULL COMMENT '业务类型',
    `audio_id` VARCHAR(255) NOT NULL COMMENT '音频ID',
    `audio_url` VARCHAR(500) NULL COMMENT '音频URL',
    `audio_name` VARCHAR(255) NULL COMMENT '音频名称',
    `duration` INT NULL COMMENT '音频时长（秒）',
    `file_size` BIGINT NULL COMMENT '音频大小（字节）',
    `sort_num` INT NULL DEFAULT 0 COMMENT '排序号',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用 1-启用',
    `tenant_id` INT NULL COMMENT '租户ID',
    `create_by` VARCHAR(50) NULL COMMENT '创建人',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` VARCHAR(50) NULL COMMENT '更新人',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除 1-已删除',
    PRIMARY KEY (`id`),
    INDEX `idx_business` (`business_id`, `business_type`),
    INDEX `idx_audio_id` (`audio_id`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='TTS音频业务关联表';

-- 创建点读热点媒体快照表
CREATE TABLE `point_reading_hotspot_media` (
    `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `hotspot_id` INT NOT NULL COMMENT '热点ID',
    `media_source` TINYINT NOT NULL COMMENT '媒体来源：10-TTS合成 20-素材中心',
    `media_id` VARCHAR(255) NOT NULL COMMENT '媒体文件ID',
    `media_url` VARCHAR(500) NULL COMMENT '媒体文件URL（快照数据）',
    `media_name` VARCHAR(255) NULL COMMENT '媒体文件名（快照数据）',
    `media_type` TINYINT NOT NULL COMMENT '媒体类型：10-音频 20-视频',
    `duration` INT NULL COMMENT '媒体时长（秒）',
    `file_size` BIGINT NULL COMMENT '媒体大小（字节）',
    `sort_num` INT NULL DEFAULT 0 COMMENT '排序号',
    `snapshot_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '快照时间',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用 1-启用',
    `tenant_id` INT NULL COMMENT '租户ID',
    `create_by` VARCHAR(50) NULL COMMENT '创建人',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` VARCHAR(50) NULL COMMENT '更新人',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除 1-已删除',
    PRIMARY KEY (`id`),
    INDEX `idx_hotspot_id` (`hotspot_id`),
    INDEX `idx_media_source` (`media_source`),
    INDEX `idx_media_id` (`media_id`),
    INDEX `idx_sort_num` (`sort_num`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='点读热点媒体快照表';

-- 查看表结构
DESCRIBE point_reading_hotspot;
DESCRIBE audio_intro_business_ref;
DESCRIBE point_reading_hotspot_media;
