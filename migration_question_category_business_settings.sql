-- 题库业务设置统一管理表数据迁移脚本
-- 执行前请先创建 question_category_business_settings 表

-- =====================================================
-- 第一步：迁移学习模块答题设置数据
-- 从 app_study_module_question_ext 迁移到 question_category_business_settings
-- business_type = 1 (学习模块答题)
-- =====================================================

INSERT INTO question_category_business_settings (
    business_id,
    business_type,
    question_method,
    question_num,
    status,
    tenant_id,
    create_by,
    create_time,
    update_by,
    update_time,
    is_deleted
)
SELECT 
    module_id as business_id,
    1 as business_type,  -- 学习模块答题
    question_method,
    question_num,
    status,
    tenant_id,
    create_by,
    create_time,
    update_by,
    update_time,
    is_deleted
FROM app_study_module_question_ext
WHERE is_deleted = 0;

-- =====================================================
-- 第二步：迁移图书书内码答题设置数据
-- 从 books_rank_in_codes_contents_question 迁移到 question_category_business_settings
-- business_type = 2 (图书书内码答题)
-- =====================================================

INSERT INTO question_category_business_settings (
    business_id,
    business_type,
    question_method,
    question_num,
    status,
    tenant_id,
    create_by,
    create_time,
    update_by,
    update_time,
    is_deleted
)
SELECT 
    in_codes_contents_id as business_id,
    2 as business_type,  -- 图书书内码答题
    question_method,
    question_num,
    status,
    tenant_id,
    create_by,
    create_time,
    update_by,
    update_time,
    is_deleted
FROM books_rank_in_codes_contents_question
WHERE is_deleted = 0;

-- =====================================================
-- 第三步：更新题库分类业务关联表的业务类型
-- 将现有的 question_category_business_ref 表中的业务类型进行统一
-- =====================================================

-- 更新学习模块相关的题库关联记录
-- 假设原来 business_type 中 1-音频专辑 2-视频专辑 3-图书 对应学习模块
-- 需要根据实际业务逻辑调整这个映射关系

-- 如果原来的 business_type = 1 对应学习模块，则保持不变
-- 如果原来的 business_type = 3 对应图书书内码，则更新为 2
UPDATE question_category_business_ref 
SET business_type = 2 
WHERE business_type = 3 AND is_deleted = 0;

-- =====================================================
-- 第四步：数据验证查询
-- 用于验证迁移结果的查询语句
-- =====================================================

-- 验证学习模块答题设置迁移结果
SELECT 
    '学习模块答题设置' as data_type,
    COUNT(*) as migrated_count
FROM question_category_business_settings 
WHERE business_type = 1 AND is_deleted = 0;

-- 验证图书书内码答题设置迁移结果
SELECT 
    '图书书内码答题设置' as data_type,
    COUNT(*) as migrated_count
FROM question_category_business_settings 
WHERE business_type = 2 AND is_deleted = 0;

-- 验证原表数据总数
SELECT 
    '原学习模块表' as data_type,
    COUNT(*) as original_count
FROM app_study_module_question_ext 
WHERE is_deleted = 0;

SELECT 
    '原图书书内码表' as data_type,
    COUNT(*) as original_count
FROM books_rank_in_codes_contents_question 
WHERE is_deleted = 0;

-- =====================================================
-- 第五步：数据一致性检查
-- 检查迁移后的数据是否与原表一致
-- =====================================================

-- 检查学习模块数据一致性
SELECT 
    'MISMATCH' as status,
    a.module_id,
    a.question_method as original_method,
    b.question_method as migrated_method,
    a.question_num as original_num,
    b.question_num as migrated_num
FROM app_study_module_question_ext a
LEFT JOIN question_category_business_settings b 
    ON a.module_id = b.business_id AND b.business_type = 1
WHERE a.is_deleted = 0 
    AND (a.question_method != b.question_method 
         OR a.question_num != b.question_num 
         OR a.status != b.status);

-- 检查图书书内码数据一致性
SELECT 
    'MISMATCH' as status,
    a.in_codes_contents_id,
    a.question_method as original_method,
    b.question_method as migrated_method,
    a.question_num as original_num,
    b.question_num as migrated_num
FROM books_rank_in_codes_contents_question a
LEFT JOIN question_category_business_settings b 
    ON a.in_codes_contents_id = b.business_id AND b.business_type = 2
WHERE a.is_deleted = 0 
    AND (a.question_method != b.question_method 
         OR a.question_num != b.question_num 
         OR a.status != b.status);

-- =====================================================
-- 注意事项：
-- 1. 执行前请备份相关表数据
-- 2. 请根据实际的业务类型映射关系调整 business_type 的值
-- 3. 如果原表中存在 NULL 值，请根据业务需求进行处理
-- 4. 执行后请验证数据完整性和一致性
-- 5. 确认迁移成功后，可以考虑是否需要保留原表或进行归档
-- =====================================================
