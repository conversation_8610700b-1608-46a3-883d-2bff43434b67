package com.dbj.classpal.books.client.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: BusinessTypeEnum
 * Date:     2025-04-14 08:48:01
 * Description: 表名： ,描述： 表
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum BusinessTypeEnum {

    AUDIO_ALBUM_BUSINESS(1,"内容管理-音频专辑","音频专辑",1),
    VIDEO_ALBUM_BUSINESS(2,"内容管理-视频专辑","视频专辑",2),
    BOOKS_RESOURCE_BUSINESS(3,"图书管理-图书资源","书内码",0),
    QUESTION_BUSINESS(4,"图书管理-题库","书内码",0),
    BOOKS_AUDIO_BUSINESS(5,"图书管理-音频专辑","书内码",1),
    BOOKS_VIDEO_BUSINESS(6,"图书管理-视频专辑","书内码",2),
    QUESTION_MEDIA_BUSINESS(7,"题目-媒体文件","题库",0),
    QUESTION_RECOGNITION_BUSINESS(8,"题目-辅助识图","题库",0),
    ANSWER_MEDIA_BUSINESS(9,"题目-答案","题库",0),
    EVALUATION_BUSINESS(10,"内容管理-评测","内容管理-评测",0),
    PINYIN_ORAL_ANIMATION_BUSINESS(11,"拼音-口型动画","拼音",0),
    PINYIN_PRONOUNCE_BUSINESS(12,"拼音-发音","拼音",0),
    PINYIN_FOUR_TONE_PRONOUNCE_BUSINESS(13,"拼音-四声发音","拼音",0),
    ANCIENT_POEM_ORIGINAL_AUDIO_BUSINESS(14,"古诗文-原文音频","古诗文",0),
    ANCIENT_POEM_EXPLANATION_AUDIO_BUSINESS(15,"古诗文-解析音频","古诗文",0),
    EVALUATION_NODE_BUSINESS(16,"内容管理-评测-评测项","内容管理-评测-评测项",0),
    STUDY_CENTER_AUDIO_BUSINESS(17,"学习模块-音频专辑","学习模块",1),
    STUDY_CENTER_VIDEO_BUSINESS(18,"学习模块-视频专辑","学习模块",2),
    STUDY_CENTER_QUESTION_BUSINESS(19,"学习模块-答题","学习模块",0),
    AUDIO_PRODUCTION_BUSINESS(20,"音频制作-语音合成","音频制作-语音合成",0),

    SYNC_COURSE_VIDEO_BUSINESS(21,"学习中心-同步课程","同步课程",2),

    SYNC_COURSE_TEXTBOOK_BUSINESS(22,"学习中心-同步课程","教材",0),

    SYNC_COURSE_VIP_RIGHTS_TAG_BUSINESS(23,"学习中心-同步课程","会员权益",0),
    POINT_READING_HOTSPOT_MEDIA_BUSINESS(24,"点读热点-媒体文件","点读热点",0),
    BOOKS_SALES_CONFIG_BUSINESS(24,"图书管理-商品设置","商品设置",0),

    ;

    private int code;
    private String type;
    private String name;
    private int albumType;

    public static BusinessTypeEnum getByCode(Integer code) {
        for (BusinessTypeEnum businessTypeEnum : BusinessTypeEnum.values()) {
            if (businessTypeEnum.getCode() == code) {
                return businessTypeEnum;
            }
        }
        return null;
    }
}
