package com.dbj.classpal.books.client.api.pointreading;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingChapterBatchApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingChapterQueryApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingChapterSaveApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingChapterSortApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingChapterUpdateApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingIdApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingMenuIdApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingBookIdApiBO;
import com.dbj.classpal.books.client.dto.pointreading.PointReadingChapterApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 点读书章节 API接口
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}", path = "/api/classpal/books/${spring.application.version}")
public interface PointReadingChapterApi {

    /**
     * 分页查询点读书章节
     *
     * @param pageInfo 分页查询参数
     * @return 分页结果
     */
    @Operation(summary = "分页查询点读书章节")
    @PostMapping("/point-reading/chapter/page")
    RestResponse<Page<PointReadingChapterApiDTO>> pageChapter(@RequestBody @Validated PageInfo<PointReadingChapterQueryApiBO> pageInfo) throws BusinessException;

    /**
     * 查询章节详情
     *
     * @param idBO 章节ID参数
     * @return 章节详情
     */
    @Operation(summary = "查询章节详情")
    @PostMapping("/point-reading/chapter/detail")
    RestResponse<PointReadingChapterApiDTO> detail(@RequestBody @Validated PointReadingIdApiBO idBO) throws BusinessException;

    /**
     * 保存点读书章节
     *
     * @param saveBO 保存参数
     * @return 章节ID
     */
    @Operation(summary = "保存点读书章节")
    @PostMapping("/point-reading/chapter/save")
    RestResponse<Integer> save(@RequestBody @Validated PointReadingChapterSaveApiBO saveBO) throws BusinessException;

    /**
     * 更新点读书章节
     *
     * @param updateBO 更新参数
     * @return 更新结果
     */
    @Operation(summary = "更新点读书章节")
    @PostMapping("/point-reading/chapter/update")
    RestResponse<Boolean> update(@RequestBody @Validated PointReadingChapterUpdateApiBO updateBO) throws BusinessException;

    /**
     * 删除点读书章节
     *
     * @param idBO 章节ID参数
     * @return 删除结果
     */
    @Operation(summary = "删除点读书章节")
    @PostMapping("/point-reading/chapter/delete")
    RestResponse<Boolean> delete(@RequestBody @Validated PointReadingIdApiBO idBO) throws BusinessException;

    /**
     * 批量删除点读书章节
     *
     * @param batchBO 批量删除参数
     * @return 删除结果
     */
    @Operation(summary = "批量删除点读书章节")
    @PostMapping("/point-reading/chapter/delete/batch")
    RestResponse<Boolean> deleteBatch(@RequestBody @Validated PointReadingChapterBatchApiBO batchBO) throws BusinessException;

    /**
     * 查询目录下的章节列表
     *
     * @param menuIdBO 目录ID参数
     * @return 章节列表
     */
    @Operation(summary = "查询目录下的章节列表")
    @PostMapping("/point-reading/chapter/menu/list")
    RestResponse<List<PointReadingChapterApiDTO>> getChapterByMenu(@RequestBody @Validated PointReadingMenuIdApiBO menuIdBO) throws BusinessException;

    /**
     * 查询点读书下的章节列表
     *
     * @param bookIdBO 点读书ID参数
     * @return 章节列表
     */
    @Operation(summary = "查询点读书下的章节列表")
    @PostMapping("/point-reading/chapter/book/list")
    RestResponse<List<PointReadingChapterApiDTO>> getChapterByBook(@RequestBody @Validated PointReadingBookIdApiBO bookIdBO) throws BusinessException;

    /**
     * 更新排序
     *
     * @param sortBO 排序参数
     * @return 更新结果
     */
    @Operation(summary = "更新排序")
    @PostMapping("/point-reading/chapter/sort")
    RestResponse<Boolean> updateSort(@RequestBody @Validated PointReadingChapterSortApiBO sortBO) throws BusinessException;

}
