package com.dbj.classpal.books.client.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 点读书ID参数 API BO
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@Schema(name = "PointReadingIdApiBO", description = "点读书ID参数 API BO")
public class PointReadingIdApiBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "ID")
    @NotNull(message = "ID不能为空")
    private Integer id;

}
