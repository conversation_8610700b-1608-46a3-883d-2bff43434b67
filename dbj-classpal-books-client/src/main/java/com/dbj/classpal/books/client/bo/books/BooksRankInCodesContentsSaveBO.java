package com.dbj.classpal.books.client.bo.books;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 图书书内码分类表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="BooksRankInCodesContents对象", description="图书书内码分类表")
public class BooksRankInCodesContentsSaveBO implements Serializable {



    @Schema(description = "名称")
    @NotEmpty(message = "名称不能为空")
    private String contentName;

    @Schema(description = "图书id")
    @NotNull(message = "图书id不能为空")
    private Integer booksId;

    @Schema(description = "册书id")
    @NotNull(message = "册书id不能为空")
    private Integer rankId;

    @Schema(description = "册数功能分类id")
    @NotNull(message = "册数功能分类id不能为空")
    private Integer rankClassifyId;

    @Schema(description = "类型 目录-directory 图书资源-resource 答题-question 音频专辑-audio 视频专辑-video 点读书-pointReading")
    private String type;

    @Schema(description = "父级id")
    private Integer fatherId;

    @Schema(description = "排序序号")
    private Integer contentsIndex;

}
