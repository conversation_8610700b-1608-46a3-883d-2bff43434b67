package com.dbj.classpal.books.client.api.pointreading;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingChapterHotspotSaveApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingHotspotBatchApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingHotspotQueryApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingHotspotSaveApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingHotspotUpdateApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingIdApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingChapterIdApiBO;
import com.dbj.classpal.books.client.dto.pointreading.PointReadingHotspotApiDTO;
import com.dbj.classpal.books.client.dto.pointreading.PointReadingHotspotTreeApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 点读书热点区域 API接口
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}", path = "/api/classpal/books/${spring.application.version}")
public interface PointReadingHotspotApi {

    /**
     * 分页查询点读书热点区域
     *
     * @param pageInfo 分页查询参数
     * @return 分页结果
     */
    @Operation(summary = "分页查询点读书热点区域")
    @PostMapping("/point-reading/hotspot/page")
    RestResponse<Page<PointReadingHotspotApiDTO>> pageHotspot(@RequestBody @Validated PageInfo<PointReadingHotspotQueryApiBO> pageInfo) throws BusinessException;

    /**
     * 查询热点详情
     *
     * @param idBO 热点ID参数
     * @return 热点详情
     */
    @Operation(summary = "查询热点详情")
    @PostMapping("/point-reading/hotspot/detail")
    RestResponse<PointReadingHotspotApiDTO> detail(@RequestBody @Validated PointReadingIdApiBO idBO) throws BusinessException;

    /**
     * 保存点读书热点区域
     *
     * @param saveBO 保存参数
     * @return 热点ID
     */
    @Operation(summary = "保存点读书热点区域")
    @PostMapping("/point-reading/hotspot/save")
    RestResponse<Integer> save(@RequestBody @Validated PointReadingHotspotSaveApiBO saveBO) throws BusinessException;

    /**
     * 批量保存章节热点区域
     *
     * @param saveBO 批量保存参数
     * @return 保存结果
     */
    @Operation(summary = "批量保存章节热点区域")
    @PostMapping("/point-reading/hotspot/save/batch")
    RestResponse<Boolean> saveChapterHotspotsBatch(@RequestBody @Validated PointReadingChapterHotspotSaveApiBO saveBO) throws BusinessException;

    /**
     * 批量更新章节热点区域（先删除后新增）
     *
     * @param saveBO 批量更新参数
     * @return 更新结果
     */
    @Operation(summary = "批量更新章节热点区域")
    @PostMapping("/point-reading/hotspot/update/batch")
    RestResponse<Boolean> updateChapterHotspotsBatch(@RequestBody @Validated PointReadingChapterHotspotSaveApiBO saveBO) throws BusinessException;

    /**
     * 更新点读书热点区域
     *
     * @param updateBO 更新参数
     * @return 更新结果
     */
    @Operation(summary = "更新点读书热点区域")
    @PostMapping("/point-reading/hotspot/update")
    RestResponse<Boolean> update(@RequestBody @Validated PointReadingHotspotUpdateApiBO updateBO) throws BusinessException;

    /**
     * 删除点读书热点区域
     *
     * @param idBO 热点ID参数
     * @return 删除结果
     */
    @Operation(summary = "删除点读书热点区域")
    @PostMapping("/point-reading/hotspot/delete")
    RestResponse<Boolean> delete(@RequestBody @Validated PointReadingIdApiBO idBO) throws BusinessException;

    /**
     * 批量删除点读书热点区域
     *
     * @param batchBO 批量删除参数
     * @return 删除结果
     */
    @Operation(summary = "批量删除点读书热点区域")
    @PostMapping("/point-reading/hotspot/delete/batch")
    RestResponse<Boolean> deleteBatch(@RequestBody @Validated PointReadingHotspotBatchApiBO batchBO) throws BusinessException;

    /**
     * 查询章节下的热点列表
     *
     * @param chapterIdBO 章节ID参数
     * @return 热点列表
     */
    @Operation(summary = "查询章节下的热点列表")
    @PostMapping("/point-reading/hotspot/chapter/list")
    RestResponse<List<PointReadingHotspotApiDTO>> getHotspotsByChapter(@RequestBody @Validated PointReadingChapterIdApiBO chapterIdBO) throws BusinessException;

    /**
     * 查询章节下的热点树形结构
     *
     * @param chapterIdBO 章节ID参数
     * @return 热点树形结构
     */
    @Operation(summary = "查询章节下的热点树形结构")
    @PostMapping("/point-reading/hotspot/chapter/tree")
    RestResponse<List<PointReadingHotspotTreeApiDTO>> getHotspotsTreeByChapter(@RequestBody @Validated PointReadingChapterIdApiBO chapterIdBO) throws BusinessException;

}
