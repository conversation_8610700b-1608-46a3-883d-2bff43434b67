package com.dbj.classpal.books.client.dto.books;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/8/1 14:08
 */
@Data
public class BooksInfoRightScopeDTO {

    @Schema(description = "权益范围")
    private String scopeType;

    @Schema(description = "试学类型 0-按资源类型 1-试学")
    private Integer trialMode;

    @Schema(description = "试学个数")
    private Integer trialCount;

    @Schema(description = "资源类型（多个英文逗号分隔）")
    private String resourceTypes;
}
