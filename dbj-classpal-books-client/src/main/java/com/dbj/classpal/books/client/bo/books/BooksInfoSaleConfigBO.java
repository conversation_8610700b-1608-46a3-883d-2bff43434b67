package com.dbj.classpal.books.client.bo.books;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/8/1 11:42
 */
@Data
public class BooksInfoSaleConfigBO {

    @NotNull
    @Schema(description = "图书id", requiredMode = RequiredMode.REQUIRED)
    private Integer booksId;

    @Schema(description = "销售方式开关")
    private Boolean salesModeStatus;

    @Schema(description = "销售方式 0-免费 1-收费")
    private Integer salesMode;

    @Schema(description = "销售价格")
    private BigDecimal salesPrice;

    @Schema(description = "划线价格")
    private BigDecimal originalPrice;

    @Schema(description = "权益标签")
    private Set<Integer> vipRightsTagIds;

    @Valid
    @Schema(description = "权益范围")
    private List<RightScopeBO> rightScopeList;

    @Data
    public static class RightScopeBO {

        @NotEmpty
        @Schema(description = "权益范围 BOOK_IN_CODES-图书资源 AUDIO_ALBUM-音频专辑 VIDEO_ALBUM-视频专辑 POINT_READ-点读书")
        private String scopeType;

        @NotNull
        @Schema(description = "试学类型 0-按资源类型 1-试学")
        private Integer trialMode;

        @Schema(description = "试学个数")
        private Integer trialCount;

        @Schema(description = "资源类型（多个英文逗号分隔）")
        private String resourceTypes;
    }

}
