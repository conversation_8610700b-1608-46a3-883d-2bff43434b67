package com.dbj.classpal.books.client.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 点读书父级ID参数 API BO
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@Schema(name = "PointReadingParentIdApiBO", description = "点读书父级ID参数 API BO")
public class PointReadingParentIdApiBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "父级ID")
    @NotNull(message = "父级ID不能为空")
    private Integer parentId;

}
