package com.dbj.classpal.books.client.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 点读书章节ID参数 API BO
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@Schema(name = "PointReadingChapterIdApiBO", description = "点读书章节ID参数 API BO")
public class PointReadingChapterIdApiBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "章节ID")
    @NotNull(message = "章节ID不能为空")
    private Integer chapterId;

}
