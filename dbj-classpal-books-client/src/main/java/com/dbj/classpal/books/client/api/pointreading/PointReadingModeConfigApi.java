package com.dbj.classpal.books.client.api.pointreading;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingModeConfigBatchApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingModeConfigProcessStatusApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingModeConfigQueryApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingModeConfigSaveApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingModeConfigSortApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingModeConfigUpdateApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingIdApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingBookIdApiBO;
import com.dbj.classpal.books.client.dto.pointreading.PointReadingModeConfigApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 点读书模式配置 API接口
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}", path = "/api/classpal/books/${spring.application.version}")
public interface PointReadingModeConfigApi {

    /**
     * 分页查询点读书模式配置
     *
     * @param pageInfo 分页查询参数
     * @return 分页结果
     */
    @Operation(summary = "分页查询点读书模式配置")
    @PostMapping("/point-reading/mode-config/page")
    RestResponse<Page<PointReadingModeConfigApiDTO>> pageModeConfig(@RequestBody @Validated PageInfo<PointReadingModeConfigQueryApiBO> pageInfo) throws BusinessException;

    /**
     * 查询模式配置详情
     *
     * @param id 配置ID
     * @return 配置详情
     */
    @Operation(summary = "查询模式配置详情")
    @PostMapping("/point-reading/mode-config/detail")
    RestResponse<PointReadingModeConfigApiDTO> detail(@RequestBody @Validated Integer id) throws BusinessException;

    /**
     * 保存点读书模式配置
     *
     * @param saveBO 保存参数
     * @return 配置ID
     */
    @Operation(summary = "保存点读书模式配置")
    @PostMapping("/point-reading/mode-config/save")
    RestResponse<Integer> save(@RequestBody @Validated PointReadingModeConfigSaveApiBO saveBO) throws BusinessException;

    /**
     * 更新点读书模式配置
     *
     * @param updateBO 更新参数
     * @return 更新结果
     */
    @Operation(summary = "更新点读书模式配置")
    @PostMapping("/point-reading/mode-config/update")
    RestResponse<Boolean> update(@RequestBody @Validated PointReadingModeConfigUpdateApiBO updateBO) throws BusinessException;

    /**
     * 删除点读书模式配置
     *
     * @param id 配置ID
     * @return 删除结果
     */
    @Operation(summary = "删除点读书模式配置")
    @PostMapping("/point-reading/mode-config/delete")
    RestResponse<Boolean> delete(@RequestBody @Validated Integer id) throws BusinessException;

    /**
     * 批量删除点读书模式配置
     *
     * @param batchBO 批量删除参数
     * @return 删除结果
     */
    @Operation(summary = "批量删除点读书模式配置")
    @PostMapping("/point-reading/mode-config/delete/batch")
    RestResponse<Boolean> deleteBatch(@RequestBody @Validated PointReadingModeConfigBatchApiBO batchBO) throws BusinessException;

    /**
     * 查询点读书下的模式配置列表
     *
     * @param bookId 点读书ID
     * @return 配置列表
     */
    @Operation(summary = "查询点读书下的模式配置列表")
    @PostMapping("/point-reading/mode-config/book/list")
    RestResponse<List<PointReadingModeConfigApiDTO>> getModeConfigsByBook(@RequestBody @Validated Integer bookId) throws BusinessException;

    /**
     * 更新排序
     *
     * @param sortBO 排序参数
     * @return 更新结果
     */
    @Operation(summary = "更新排序")
    @PostMapping("/point-reading/mode-config/sort")
    RestResponse<Boolean> updateSort(@RequestBody @Validated PointReadingModeConfigSortApiBO sortBO) throws BusinessException;

    /**
     * 启用配置
     *
     * @param id 配置ID
     * @return 启用结果
     */
    @Operation(summary = "启用配置")
    @PostMapping("/point-reading/mode-config/enable")
    RestResponse<Boolean> enable(@RequestBody @Validated Integer id) throws BusinessException;

    /**
     * 禁用配置
     *
     * @param id 配置ID
     * @return 禁用结果
     */
    @Operation(summary = "禁用配置")
    @PostMapping("/point-reading/mode-config/disable")
    RestResponse<Boolean> disable(@RequestBody @Validated Integer id) throws BusinessException;

    /**
     * 批量启用配置
     *
     * @param batchBO 批量启用参数
     * @return 启用结果
     */
    @Operation(summary = "批量启用配置")
    @PostMapping("/point-reading/mode-config/enable/batch")
    RestResponse<Boolean> enableBatch(@RequestBody @Validated PointReadingModeConfigBatchApiBO batchBO) throws BusinessException;

    /**
     * 批量禁用配置
     *
     * @param batchBO 批量禁用参数
     * @return 禁用结果
     */
    @Operation(summary = "批量禁用配置")
    @PostMapping("/point-reading/mode-config/disable/batch")
    RestResponse<Boolean> disableBatch(@RequestBody @Validated PointReadingModeConfigBatchApiBO batchBO) throws BusinessException;

    /**
     * 更新处理状态
     *
     * @param statusBO 处理状态参数
     * @return 更新结果
     */
    @Operation(summary = "更新处理状态")
    @PostMapping("/point-reading/mode-config/process-status")
    RestResponse<Boolean> updateProcessStatus(@RequestBody @Validated PointReadingModeConfigProcessStatusApiBO statusBO) throws BusinessException;
}
