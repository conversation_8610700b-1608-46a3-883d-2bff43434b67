package com.dbj.classpal.books.client.dto.books;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/8/1 14:07
 */
@Data
public class BooksInfoProductSalesConfigDTO {

    @Schema(description = "图书id")
    private Integer booksId;

    @Schema(description = "销售方式开关")
    private Boolean salesModeStatus;

    @Schema(description = "销售方式 0-免费 1-收费")
    private Integer salesMode;

    @Schema(description = "销售价格")
    private BigDecimal salesPrice;

    @Schema(description = "划线价格")
    private BigDecimal originalPrice;

    @Schema(description = "权益标签")
    private Set<Integer> vipRightsTagIds;

    @Schema(description = "权益范围")
    private List<BooksInfoRightScopeDTO> rightScopeList;
}
