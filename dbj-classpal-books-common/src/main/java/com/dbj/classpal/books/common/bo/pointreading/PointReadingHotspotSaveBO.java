package com.dbj.classpal.books.common.bo.pointreading;

import com.dbj.classpal.books.common.bo.question.QuestionCategoryBusinessSettingsBO;
import com.dbj.classpal.books.common.dto.pointreading.PointReadingHotspotMediaDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * 点读书热点区域保存BO
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Schema(name = "PointReadingHotspotSaveBO", description = "点读书热点区域保存BO")
public class PointReadingHotspotSaveBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "所属章节ID")
    @NotNull(message = "所属章节ID不能为空")
    private Integer chapterId;

    @Schema(description = "点读书ID")
    @NotNull(message = "点读书ID不能为空")
    private Integer bookId;

    @Schema(description = "热点名称")
    @NotBlank(message = "热点名称不能为空")
    private String name;

    @Schema(description = "前端节点ID（前端定义唯一性）")
    private String nodeId;

    @Schema(description = "父节点ID（支持层级结构）")
    private String parentNodeId;

    @Schema(description = "节点名称")
    private String nodeName;

    @Schema(description = "节点层级（1-根节点，2-二级节点...）")
    private Integer nodeLevel;

    @Schema(description = "区域类型：10-热点区域 20-标记点")
    private Integer areaType;

    @Schema(description = "事件类型：10-点读 20-答题 30-跟读")
    private Integer eventType;

    @Schema(description = "X坐标")
    private BigDecimal coordinateX;

    @Schema(description = "Y坐标")
    private BigDecimal coordinateY;

    @Schema(description = "宽度")
    private BigDecimal width;

    @Schema(description = "高度")
    private BigDecimal height;

    @Schema(description = "媒体来源：10-TTS合成 20-素材中心")
    private Integer mediaSource;

    @Schema(description = "是否支持跟读：0-否 1-是")
    private Integer followRead;

    @Schema(description = "跟读校验文本")
    private String verifyText;

    @Schema(description = "填空文本内容（富文本格式，当模式为填空时使用）")
    private String fillBlankContent;

    @Schema(description = "模式配置字典ID（冗余存储，用于历史数据保护）")
    private Integer configDictId;

    @Schema(description = "模式显示名称（冗余存储，用于历史数据保护）")
    private String displayName;

    @Schema(description = "排序")
    private Integer sortNum;

    @Schema(description = "状态：0-停用 1-正常")
    private Integer status;

    @Schema(description = "答题设置，仅事件类型为答题时使用")
    private QuestionCategoryBusinessSettingsBO questionSettings;

    @Schema(description = "媒体文件列表")
    private List<PointReadingHotspotMediaBO> mediaList;
}
