package com.dbj.classpal.books.common.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 点读热点媒体DTO
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@Schema(description = "点读热点媒体BO")
public class PointReadingHotspotMediaBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    private Integer id;

    @Schema(description = "热点ID")
    private Integer hotspotId;

    @Schema(description = "媒体来源：10-TTS合成 20-素材中心")
    private Integer mediaSource;


    @Schema(description = "媒体文件ID")
    private String mediaId;

    @Schema(description = "媒体文件URL（快照数据）")
    private String mediaUrl;

    @Schema(description = "媒体文件名（快照数据）")
    private String mediaName;

    @Schema(description = "媒体类型：10-音频 20-视频")
    private Integer mediaType;

    @Schema(description = "排序号")
    private Integer sortNum;

}
