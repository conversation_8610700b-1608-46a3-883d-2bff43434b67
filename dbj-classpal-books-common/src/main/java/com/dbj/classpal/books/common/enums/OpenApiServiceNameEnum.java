package com.dbj.classpal.books.common.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 出题方式
 */
@Getter
public enum OpenApiServiceNameEnum {
    BOOK("book", "图书服务"),;

    /**
     * 类型值
     */
    private final String code;

    /**
     * 类型描述
     */
    private final String desc;

    OpenApiServiceNameEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据类型值获取枚举
     *
     * @param code 类型值
     * @return 对应的枚举值，如果未找到则返回null
     */
    public static OpenApiServiceNameEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (OpenApiServiceNameEnum type : values()) {
            if (Objects.equals(type.getCode(), code)) {
                return type;
            }
        }
        return null;
    }

} 