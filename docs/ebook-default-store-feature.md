# 电子书默认书城查询功能实现

## 功能概述

在H5分页查询电子书时，如果用户没有提供 `shelfId` 和 `storeId` 参数，系统会自动查询已启用的默认书城中的图书。

## 实现方案

### 1. 数据库层实现 (推荐)

在 `AppEBookMapper.xml` 的 `pageForH5` 查询中增加了 `<otherwise>` 分支：

```xml
<otherwise>
    <!-- 如果没有提供shelfId和storeId，则查询默认启用的书城和书架 -->
    app_ebookshelf_book_ref sbr
    INNER JOIN app_ebookshelf shelf ON sbr.shelf_id = shelf.id
    INNER JOIN app_ebookstore_shelf_ref ssr ON sbr.shelf_id = ssr.shelf_id
    INNER JOIN app_ebookstore es ON ssr.store_id = es.id
    WHERE es.launch_status = 1
    AND shelf.launch_status = 1
    AND es.is_deleted = 0
    AND shelf.is_deleted = 0
    AND ssr.is_deleted = 0
    AND sbr.is_deleted = 0
</otherwise>
```

**查询逻辑**：
- 查询 `app_ebookstore` 表中 `launch_status = 1`（已启用）的书城
- 同时确保关联的书架也是启用状态 `shelf.launch_status = 1`
- 过滤掉已删除的记录 `is_deleted = 0`

### 2. 业务层实现 (备选方案)

在 `AppEBookstoreBiz` 中新增方法获取默认书城ID：

```java
@Override
public Integer getDefaultEnabledStoreId() throws BusinessException {
    LambdaQueryWrapper<AppEBookstore> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(AppEBookstore::getLaunchStatus, YesOrNoEnum.YES.getCode())
           .eq(AppEBookstore::getIsDeleted, YesOrNoEnum.NO.getCode())
           .orderByAsc(AppEBookstore::getId)
           .last("LIMIT 1");
    
    AppEBookstore defaultStore = this.getOne(wrapper);
    return defaultStore != null ? defaultStore.getId() : null;
}
```

在 `AppEBookBizImpl.pageForH5` 方法中可以选择使用：

```java
if (query.getShelfId() == null && query.getStoreId() == null) {
    Integer defaultStoreId = eBookstoreBiz.getDefaultEnabledStoreId();
    if (defaultStoreId != null) {
        query.setStoreId(defaultStoreId);
        log.info("设置默认书城ID: {}", defaultStoreId);
    }
}
```

## 查询条件优先级

1. **指定 shelfId**: 查询指定书架中的图书
2. **指定 storeId**: 查询指定书城中所有书架的图书
3. **都不指定**: 查询所有已启用书城中已启用书架的图书

## 数据表关系

```
app_ebook (图书表)
    ↑
app_ebookshelf_book_ref (书架图书关联表)
    ↑
app_ebookshelf (书架表) - launch_status 字段
    ↑
app_ebookstore_shelf_ref (书城书架关联表)
    ↑
app_ebookstore (书城表) - launch_status 字段
```

## 关键字段说明

- `app_ebookstore.launch_status`: 书城启用状态 (0-禁用, 1-启用)
- `app_ebookshelf.launch_status`: 书架启用状态 (0-禁用, 1-启用)
- `*.is_deleted`: 删除标记 (0-未删除, 1-已删除)

## 测试用例

### 测试场景1: 不提供任何ID
```java
AppEBookH5QueryBO query = new AppEBookH5QueryBO();
// shelfId = null, storeId = null
// 期望: 查询所有启用书城的启用书架中的图书
```

### 测试场景2: 提供shelfId
```java
AppEBookH5QueryBO query = new AppEBookH5QueryBO();
query.setShelfId(1);
// 期望: 查询指定书架中的图书
```

### 测试场景3: 提供storeId
```java
AppEBookH5QueryBO query = new AppEBookH5QueryBO();
query.setStoreId(1);
// 期望: 查询指定书城中所有书架的图书
```

## 性能考虑

1. **索引优化**: 确保 `launch_status` 和 `is_deleted` 字段有合适的索引
2. **查询缓存**: 对于默认书城查询结果可以考虑缓存
3. **分页优化**: 使用 MyBatis Plus 的分页插件，避免全表扫描

## 配置建议

### 数据库索引
```sql
-- 书城表索引
CREATE INDEX idx_ebookstore_launch_status ON app_ebookstore(launch_status, is_deleted);

-- 书架表索引  
CREATE INDEX idx_ebookshelf_launch_status ON app_ebookshelf(launch_status, is_deleted);

-- 关联表索引
CREATE INDEX idx_store_shelf_ref ON app_ebookstore_shelf_ref(store_id, shelf_id, is_deleted);
CREATE INDEX idx_shelf_book_ref ON app_ebookshelf_book_ref(shelf_id, book_id, is_deleted);
```

### 缓存配置
```java
@Cacheable(value = "defaultStore", key = "'enabled'")
public Integer getDefaultEnabledStoreId() {
    // 实现逻辑
}
```

## 日志监控

在关键位置添加日志，便于问题排查：

```java
log.info("H5分页查询单书，shelfId: {}, storeId: {}", query.getShelfId(), query.getStoreId());
log.info("未提供shelfId和storeId，将查询默认启用的书城");
```

## 注意事项

1. **数据一致性**: 确保书城和书架的启用状态同步
2. **默认值策略**: 如果有多个启用的书城，按ID升序取第一个作为默认
3. **异常处理**: 当没有启用的书城时，返回空结果而不是报错
4. **权限控制**: 确保用户有权限访问默认书城的内容

## 后续优化建议

1. **配置化**: 将默认书城ID配置到系统参数中，避免硬编码查询逻辑
2. **多租户支持**: 如果系统支持多租户，需要按租户查询默认书城
3. **个性化推荐**: 可以根据用户历史行为推荐个性化的默认书城
4. **A/B测试**: 支持不同的默认书城策略进行效果对比
