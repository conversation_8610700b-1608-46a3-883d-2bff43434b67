# 业务引用处理器设计文档

## 概述

为了解决 `getBusinessRefs` 方法需要处理多个业务模块引用的问题，我们设计了一套可扩展的处理器模式。目前已支持 BusinessTypeEnum=4（图书管理-题库）和 19（学习模块-答题）。

## 设计架构

### 1. 核心接口

#### BusinessRefProcessor
```java
public interface BusinessRefProcessor {
    BusinessTypeEnum getSupportedBusinessType();
    void processBusinessInfo(QuestionCategoryRefDTO dto, QuestionCategoryBusinessRef businessRef) throws Exception;
    default int getPriority() { return 100; }
}
```

#### BusinessRefProcessorManager
负责管理和调度所有的业务引用处理器，提供统一的处理入口。

### 2. 已实现的处理器

#### BookQuestionBusinessRefProcessor
- **业务类型**: BusinessTypeEnum.QUESTION_BUSINESS (4)
- **功能**: 处理图书管理-题库业务引用
- **数据源**: BooksRankInCodesContents + BooksInfo
- **优先级**: 10

#### StudyCenterQuestionBusinessRefProcessor  
- **业务类型**: BusinessTypeEnum.STUDY_CENTER_QUESTION_BUSINESS (19)
- **功能**: 处理学习模块-答题业务引用
- **数据源**: 待实现（学习模块相关实体）
- **优先级**: 20

#### DefaultBusinessRefProcessor
- **业务类型**: null（兜底处理器）
- **功能**: 处理未知或暂不支持的业务类型
- **优先级**: Integer.MAX_VALUE

## 使用方式

### 1. 在服务类中使用

```java
@Service
@RequiredArgsConstructor
public class QuestionCategoryServiceImpl implements QuestionCategoryService {
    
    private final BusinessRefProcessorManager processorManager;
    
    @Override
    public List<QuestionCategoryRefDTO> getBusinessRefs(QuestionCategoryIdQueryBO queryBO) {
        // ... 获取业务引用列表
        
        for (QuestionCategoryBusinessRef businessRef : entityList) {
            QuestionCategoryRefDTO dto = new QuestionCategoryRefDTO();
            // ... 设置基础信息
            
            // 使用处理器管理器处理业务信息
            try {
                processorManager.processBusinessInfo(dto, businessRef);
                result.add(dto);
            } catch (Exception e) {
                log.error("处理业务引用信息失败", e);
            }
        }
        
        return result;
    }
}
```

### 2. 查询支持的业务类型

```java
List<BusinessTypeEnum> supportedTypes = processorManager.getSupportedBusinessTypes();
boolean isSupported = processorManager.isSupported(BusinessTypeEnum.QUESTION_BUSINESS);
```

## 扩展新的业务类型

### 1. 创建新的处理器

```java
@Slf4j
@Component
public class NewBusinessRefProcessor implements BusinessRefProcessor {

    @Resource
    private INewBusinessBiz newBusinessBiz;

    @Override
    public BusinessTypeEnum getSupportedBusinessType() {
        return BusinessTypeEnum.NEW_BUSINESS_TYPE;
    }

    @Override
    public void processBusinessInfo(QuestionCategoryRefDTO dto, QuestionCategoryBusinessRef businessRef) throws Exception {
        // 实现具体的业务逻辑
        NewBusinessEntity entity = newBusinessBiz.getById(businessRef.getBusinessId());
        if (entity != null) {
            dto.setBusinessName(entity.getName());
            // 设置其他相关字段
        } else {
            dto.setBusinessName("业务实体不存在(ID:" + businessRef.getBusinessId() + ")");
        }
    }

    @Override
    public int getPriority() {
        return 30; // 设置优先级
    }
}
```

### 2. 自动注册

由于使用了 `@Component` 注解，Spring 会自动扫描并注册新的处理器。`BusinessRefProcessorManager` 在初始化时会自动发现并注册所有实现了 `BusinessRefProcessor` 接口的 Bean。

## 配置说明

### 1. 处理器优先级

- 数值越小，优先级越高
- 建议预留优先级区间：
  - 1-10: 核心业务处理器
  - 11-50: 扩展业务处理器  
  - 51-100: 通用业务处理器
  - Integer.MAX_VALUE: 默认处理器

### 2. 异常处理策略

- 单个处理器失败不影响其他记录的处理
- 异常信息会记录到日志中
- 可以根据需要实现重试机制

### 3. 性能考虑

- 处理器按优先级排序，避免每次查找
- 使用 Map 缓存业务类型到处理器的映射
- 支持并发处理

## 数据流程

```
QuestionCategoryServiceImpl.getBusinessRefs()
    ↓
BusinessRefProcessorManager.processBusinessInfo()
    ↓
根据 BusinessType 选择对应的 Processor
    ↓
BookQuestionBusinessRefProcessor / StudyCenterQuestionBusinessRefProcessor / DefaultBusinessRefProcessor
    ↓
填充 QuestionCategoryRefDTO 的业务信息
```

## 测试

### 1. 单元测试

```java
@Test
void testBookQuestionBusinessProcessing() throws Exception {
    QuestionCategoryBusinessRef businessRef = new QuestionCategoryBusinessRef();
    businessRef.setBusinessId(1);
    businessRef.setBusinessType(BusinessTypeEnum.QUESTION_BUSINESS.getCode());
    
    QuestionCategoryRefDTO dto = new QuestionCategoryRefDTO();
    processorManager.processBusinessInfo(dto, businessRef);
    
    assertNotNull(dto.getBusinessName());
}
```

### 2. 集成测试

通过 `BusinessRefProcessorManagerTest` 验证整个处理流程。

## 监控和日志

### 1. 关键日志点

- 处理器注册: `BusinessRefProcessorManager.init()`
- 业务处理: 每个处理器的 `processBusinessInfo()` 方法
- 异常处理: 处理失败时的错误日志

### 2. 监控指标

- 各业务类型的处理次数
- 处理失败率
- 处理耗时

## 注意事项

### 1. 数据一致性

- 确保业务实体存在性检查
- 处理数据不存在的情况
- 避免空指针异常

### 2. 性能优化

- 避免在处理器中进行复杂查询
- 考虑使用缓存减少数据库访问
- 批量查询优化

### 3. 向后兼容

- 新增处理器不影响现有功能
- 保持接口稳定性
- 提供平滑的迁移路径

## 后续规划

### 1. 功能增强

- 支持批量处理
- 添加缓存机制
- 实现异步处理

### 2. 扩展支持

- 支持更多业务类型
- 提供处理器配置化
- 支持动态加载处理器

### 3. 监控完善

- 添加性能监控
- 实现健康检查
- 提供管理界面
