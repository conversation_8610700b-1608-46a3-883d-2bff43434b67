# 1.2

## 一、新建表结构

### 1. 点读书模块 (Point Reading Module)

#### 1.1 点读书分类表 (point_reading_category)
```sql
CREATE TABLE point_reading_category (
    id          INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    name        VARCHAR(64) NOT NULL COMMENT '分类名称',
    parent_id   INT DEFAULT 0 COMMENT '父级分类ID',
    is_default  TINYINT(1) DEFAULT 0 COMMENT '是否默认分类：0-否 1-是',
    sort_num    INT COMMENT '排序',
    status      TINYINT(1) DEFAULT 1 COMMENT '状态：0-停用 1-正常',
    create_by   VARCHAR(64) COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by   VARCHAR(64) COMMENT '更新者',
    update_time DATETIME ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted  TINYINT(1) DEFAULT 0 COMMENT '是否删除：0-否 1-是',
    version     INT DEFAULT 1 COMMENT '版本号',
    tenant_id   INT COMMENT '租户ID'
) COMMENT '点读书分类表';
```

**用途**: 管理点读书的分类层级  
**核心字段**: 
- `name` - 分类名称
- `parent_id` - 父级分类ID (支持树形结构)
- `is_default` - 是否默认分类
- `sort_num` - 排序

#### 1.2 点读书表 (point_reading_book)
```sql
CREATE TABLE point_reading_book (
    id              INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    name            VARCHAR(128) NOT NULL COMMENT '点读书名称',
    category_id     INT NOT NULL COMMENT '所属分类ID',
    cover_id        VARCHAR(255) COMMENT '封面文件ID',
    cover_url       VARCHAR(500) COMMENT '封面URL',
    cover_name      VARCHAR(255) COMMENT '封面文件名',
    original_file_id VARCHAR(255) COMMENT '原始文件ID',
    original_url    VARCHAR(500) COMMENT '原始文件URL',
    original_name   VARCHAR(255) COMMENT '原始文件名',
    original_md5    VARCHAR(32) COMMENT '原始文件MD5',
    parse_status    TINYINT DEFAULT 10 COMMENT '解析状态：10-待解析 20-解析中 30-已解析 40-解析失败',
    launch_status   TINYINT DEFAULT 0 COMMENT '启用状态：0-未启用 1-已启用',
    sort_num        INT COMMENT '排序',
    status          TINYINT(1) DEFAULT 1 COMMENT '状态：0-停用 1-正常',
    create_by       VARCHAR(64) COMMENT '创建者',
    create_time     DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by       VARCHAR(64) COMMENT '更新者',
    update_time     DATETIME ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted      TINYINT(1) DEFAULT 0 COMMENT '是否删除：0-否 1-是',
    version         INT DEFAULT 1 COMMENT '版本号',
    tenant_id       INT COMMENT '租户ID'
) COMMENT '点读书表';
```

**用途**: 存储点读书基本信息  
**核心字段**:
- `name` - 点读书名称
- `category_id` - 所属分类ID
- `parse_status` - 解析状态 (10-待解析, 20-解析中, 30-已解析, 40-解析失败)
- `launch_status` - 启用状态

#### 1.3 点读书目录表 (point_reading_menu)
```sql
CREATE TABLE point_reading_menu (
    id          INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    book_id     INT NOT NULL COMMENT '所属点读书ID',
    name        VARCHAR(128) NOT NULL COMMENT '目录名称',
    parent_id   INT DEFAULT 0 COMMENT '父级目录ID',
    level       TINYINT DEFAULT 1 COMMENT '目录层级',
    sort_num    INT COMMENT '排序',
    status      TINYINT(1) DEFAULT 1 COMMENT '状态：0-停用 1-正常',
    create_by   VARCHAR(64) COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by   VARCHAR(64) COMMENT '更新者',
    update_time DATETIME ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted  TINYINT(1) DEFAULT 0 COMMENT '是否删除：0-否 1-是',
    version     INT DEFAULT 1 COMMENT '版本号',
    tenant_id   INT COMMENT '租户ID'
) COMMENT '点读书目录表';
```

**用途**: 管理点读书的目录结构  
**核心字段**:
- `book_id` - 所属点读书ID
- `parent_id` - 父级目录ID (支持多级目录)
- `level` - 目录层级

#### 1.4 点读书页面表 (point_reading_page)
```sql
CREATE TABLE point_reading_page (
    id                  INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    menu_id             INT NOT NULL COMMENT '所属目录ID',
    name                VARCHAR(128) NOT NULL COMMENT '页面名称',
    image_id            VARCHAR(255) COMMENT '页面图片ID',
    image_url           VARCHAR(500) COMMENT '页面图片URL',
    image_name          VARCHAR(255) COMMENT '页面图片名称',
    image_source_type   TINYINT DEFAULT 1 COMMENT '图片来源：1-点读书 2-素材中心',
    hotspot_count       INT DEFAULT 0 COMMENT '热点数量',
    media_count         INT DEFAULT 0 COMMENT '关联媒体数量',
    media_type          TINYINT DEFAULT 10 COMMENT '关联媒体类型：10-音频',
    sort_num            INT COMMENT '排序',
    status              TINYINT(1) DEFAULT 1 COMMENT '状态：0-停用 1-正常',
    create_by           VARCHAR(64) COMMENT '创建者',
    create_time         DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by           VARCHAR(64) COMMENT '更新者',
    update_time         DATETIME ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted          TINYINT(1) DEFAULT 0 COMMENT '是否删除：0-否 1-是',
    version             INT DEFAULT 1 COMMENT '版本号',
    tenant_id           INT COMMENT '租户ID'
) COMMENT '点读书页面表';
```

**用途**: 存储点读书的页面信息  
**核心字段**:
- `menu_id` - 所属目录ID
- `image_source_type` - 图片来源 (1-点读书, 2-素材中心)
- `hotspot_count` - 热点数量

#### 1.5 点读书热点区域表 (point_reading_hotspot)
```sql
CREATE TABLE point_reading_hotspot (
    id              INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    page_id         INT NOT NULL COMMENT '所属页面ID',
    name            VARCHAR(128) NOT NULL COMMENT '热点名称',
    area_type       TINYINT DEFAULT 10 COMMENT '区域类型：10-热点区域 20-标记点',
    event_type      TINYINT DEFAULT 10 COMMENT '事件类型：10-点读 20-答题 30-跟读',
    coordinate_x    DECIMAL(10,4) COMMENT 'X坐标',
    coordinate_y    DECIMAL(10,4) COMMENT 'Y坐标',
    width           DECIMAL(10,4) COMMENT '宽度',
    height          DECIMAL(10,4) COMMENT '高度',
    media_id        VARCHAR(255) COMMENT '媒体文件ID',
    media_url       VARCHAR(500) COMMENT '媒体文件URL',
    media_name      VARCHAR(255) COMMENT '媒体文件名',
    media_type      TINYINT DEFAULT 10 COMMENT '媒体类型：10-音频 20-视频',
    media_source    TINYINT DEFAULT 20 COMMENT '媒体来源：10-TTS合成 20-素材中心',
    follow_read     TINYINT(1) DEFAULT 0 COMMENT '是否支持跟读：0-否 1-是',
    verify_text     TEXT COMMENT '跟读校验文本',
    sort_num        INT COMMENT '排序',
    status          TINYINT(1) DEFAULT 1 COMMENT '状态：0-停用 1-正常',
    create_by       VARCHAR(64) COMMENT '创建者',
    create_time     DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by       VARCHAR(64) COMMENT '更新者',
    update_time     DATETIME ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted      TINYINT(1) DEFAULT 0 COMMENT '是否删除：0-否 1-是',
    version         INT DEFAULT 1 COMMENT '版本号',
    tenant_id       INT COMMENT '租户ID'
) COMMENT '点读书热点区域表';
```

**用途**: 管理页面上的可交互热点区域  
**核心字段**:
- `area_type` - 区域类型 (10-热点区域, 20-标记点)
- `event_type` - 事件类型 (10-点读, 20-答题, 30-跟读)
- `coordinate_x/y, width, height` - 坐标和尺寸信息
- `follow_read` - 是否支持跟读

### 2. 业务关联模块

#### 2.1 图书分类业务关联表 (books_category_business_ref)
```sql
CREATE TABLE books_category_business_ref (
    id              INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    category_id     INT NOT NULL COMMENT '图书分类ID',
    business_type   VARCHAR(64) NOT NULL COMMENT '业务类型：BOOK_IN_CODES-图书资源 POINT_READING-点读书 AUDIO_ALBUM-音频专辑 VIDEO_ALBUM-视频专辑',
    business_id     INT NOT NULL COMMENT '业务ID',
    sort_num        INT COMMENT '排序',
    status          TINYINT(1) DEFAULT 1 COMMENT '状态：0-停用 1-正常',
    create_by       VARCHAR(64) COMMENT '创建者',
    create_time     DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by       VARCHAR(64) COMMENT '更新者',
    update_time     DATETIME ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted      TINYINT(1) DEFAULT 0 COMMENT '是否删除：0-否 1-是',
    version         INT DEFAULT 1 COMMENT '版本号',
    tenant_id       INT COMMENT '租户ID'
) COMMENT '图书分类业务关联表';
```

#### 2.2 商品销售配置表 (product_sales_config)
```sql
CREATE TABLE product_sales_config (
    id                  INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    product_id          INT NOT NULL COMMENT '商品ID',
    product_type        VARCHAR(50) NOT NULL COMMENT '商品类型：BOOK-图书 VIDEO_ALBUM-视频专辑 AUDIO_ALBUM-音频专辑 POINT_READING-点读书',
    sales_mode          TINYINT(1) DEFAULT 1 COMMENT '销售模式：0-免费 1-收费',
    sales_price         DECIMAL(10,2) COMMENT '销售价格(元)',
    original_price      DECIMAL(10,2) COMMENT '原价(元)',
    listing_status      TINYINT(1) DEFAULT 1 COMMENT '上架状态：0-下架 1-上架',
    is_hidden           TINYINT(1) DEFAULT 0 COMMENT '是否隐藏：0-显示 1-隐藏',
    status              TINYINT(1) DEFAULT 1 COMMENT '状态：0-停用 1-正常',
    create_by           VARCHAR(64) COMMENT '创建者',
    create_time         DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by           VARCHAR(64) COMMENT '更新者',
    update_time         DATETIME ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted          TINYINT(1) DEFAULT 0 COMMENT '是否删除：0-否 1-是',
    version             INT DEFAULT 1 COMMENT '版本号',
    tenant_id           INT COMMENT '租户ID',
    UNIQUE KEY uk_product_type_tenant (product_id, product_type, tenant_id)
) COMMENT '商品销售配置表';
```

## 二、表结构修改建议

### 1. 现有表优化

| 原表名                          | 新表名                       | 修改说明             |
| ------------------------------- | ---------------------------- | -------------------- |
| `app_study_module_question_ext` | `question_business_settings` | 表名更规范化         |
| `app_ebook_pdf_task`            | `assets_task`                | 扩展支持多种资源类型 |

### 2. 字段修改详情

#### 2.1 question_business_settings 表
| 原字段名    | 新字段名        | 说明               |
| ----------- | --------------- | ------------------ |
| `module_id` | `business_id`   | 更通用的业务ID     |
| -           | `business_type` | 新增：业务类型标识 |

#### 2.2 assets_task 表
| 原字段名   | 新字段名      | 说明             |
| ---------- | ------------- | ---------------- |
| `pdf_url`  | `assets_url`  | 支持多种资源类型 |
| `pdf_name` | `assets_name` | 通用资源名称     |
| -          | `assets_type` | 新增：资源类型   |

#### 2.3 book_info表

| 原字段名 | 新字段名      | 说明              |
| -------- | ------------- | ----------------- |
| -        | launch_status | 修改定义,启用状态 |

#### 2.4  提供增删改查，基础类型（配置字典表）

```mysql
# 提供增删改查，基础类型（配置字典表）
select * from books_rank_classify;
select * from books_rank_classify_business_ref;
```

## 三、索引设计

### 3.1 点读书模块索引
```sql
-- 分类表索引
CREATE INDEX idx_category_parent ON point_reading_category(parent_id);
CREATE INDEX idx_category_status ON point_reading_category(status, is_deleted);

-- 点读书表索引
CREATE INDEX idx_book_category ON point_reading_book(category_id);
CREATE INDEX idx_book_status ON point_reading_book(parse_status, launch_status);
CREATE INDEX idx_book_md5 ON point_reading_book(original_md5);

-- 目录表索引
CREATE INDEX idx_menu_book ON point_reading_menu(book_id);
CREATE INDEX idx_menu_parent ON point_reading_menu(parent_id);

-- 页面表索引
CREATE INDEX idx_page_menu ON point_reading_page(menu_id);

-- 热点区域表索引
CREATE INDEX idx_hotspot_page ON point_reading_hotspot(page_id);
CREATE INDEX idx_hotspot_type ON point_reading_hotspot(area_type, event_type);
CREATE INDEX idx_hotspot_media ON point_reading_hotspot(media_type, media_source);
```

### 3.2 业务关联模块索引
```sql
-- 图书分类关联表索引
CREATE INDEX idx_category_business ON books_category_business_ref(category_id, business_type);
CREATE INDEX idx_business_ref ON books_category_business_ref(business_type, business_id);

-- 商品配置表索引
CREATE INDEX idx_product_config ON product_sales_config(product_type, listing_status);
CREATE INDEX idx_sales_mode ON product_sales_config(sales_mode, is_hidden);
```
